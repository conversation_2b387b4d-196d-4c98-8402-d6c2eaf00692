

# **Vierla: Component Implementation Guide**

This document provides the necessary code and integration details for the finalized set of UI components. Each component is designed to be added to your vierla-rebuild project.

---

### **1\. Global Background: Background Beams**

* **Source:** kokonutd/beams-background  
* **File Path:** components/ui/background-beams.tsx  
* **Purpose:** Provides a subtle, elegant, and dynamic background for the entire page or specific high-impact sections like the Hero.

**Component Code:**

TypeScript

"use client";  
import React from "react";  
import { motion } from "framer-motion";  
import { cn } from "@/lib/utils";

export const BackgroundBeams \= ({ className }: { className?: string }) \=\> {  
  return (  
    \<div  
      className={cn(  
        "absolute top-0 left-0 w-full h-full \-z-10",  
        className  
      )}  
    \>  
      \<div className="relative w-full h-full overflow-hidden"\>  
        \<div className="absolute inset-0 bg-zinc-900"\>\</div\>  
        \<div className="absolute h-full w-full"\>  
          {/\* Placeholder for beam elements \*/}  
          {/\* This component often requires more complex SVG/div structures for the beams themselves. \*/}  
          {/\* The following is a simplified representation. Refer to the source for the full SVG implementation. \*/}  
          \<div className="absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-\[beam\_8s\_linear\_infinite\]"\>\</div\>  
          \<div className="absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-\[beam\_10s\_linear\_infinite\_2s\_\]"\>\</div\>  
          \<div className="absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-\[beam\_9s\_linear\_infinite\_1s\_\]"\>\</div\>  
        \</div\>  
      \</div\>  
    \</div\>  
  );  
};

// Add the keyframes to your globals.css or tailwind.config.js  
/\*  
@keyframes beam {  
  0% { transform: translateY(-100%); }  
  100% { transform: translateY(100%); }  
}  
\*/

**Implementation Specifics:**

* Place this component inside your main layout file (app/layout.tsx) or at the top level of app/page.tsx, ensuring it has a lower z-index (-z-10) so it sits behind all other content.  
* The animation keyframes need to be added to your global CSS file for the effect to work.

---

### **2\. Card Effects: Glowing Effect**

* **Source:** aceternity/glowing-effect  
* **File Path:** components/ui/glowing-card.tsx  
* **Purpose:** To wrap other components (like standard cards) to give them an interactive, attention-grabbing glow on hover, perfect for feature showcases.

**Component Code:**

TypeScript

"use client";  
import React, { useEffect, useRef, useState } from "react";  
import { cn } from "@/lib/utils";

export const GlowingCardContainer \= ({  
  children,  
  className,  
}: {  
  children?: React.ReactNode;  
  className?: string;  
}) \=\> {  
  const containerRef \= useRef\<HTMLDivElement\>(null);  
  const \= useState({ width: 0, height: 0 });

  useEffect(() \=\> {  
    if (containerRef.current) {  
      const { width, height } \= containerRef.current.getBoundingClientRect();  
      setDimensions({ width, height });  
    }  
  },);

  return (  
    \<div  
      ref={containerRef}  
      style={{  
        "--card-width": \`${dimensions.width}px\`,  
        "--card-height": \`${dimensions.height}px\`,  
      }}  
      className={cn("relative w-full h-full", className)}  
    \>  
      \<div className="relative z-10 w-full h-full"\>{children}\</div\>  
      \<div  
        style={  
          {  
            "--glow-color": "hsl(var(--primary))", // Uses your primary theme color  
          } as React.CSSProperties  
        }  
        className="absolute inset-0 w-full h-full bg-\[radial-gradient(var(--glow-color)\_40%,transparent\_60%)\] \-z-10 blur-3xl  
        group-hover:opacity-100 opacity-0 transition-opacity duration-500"  
      /\>  
    \</div\>  
  );  
};

**Implementation Specifics:**

* This component is a wrapper. Use it around your existing Card components.  
* The parent element of GlowingCardContainer must have group in its className for the hover effect to trigger (e.g., \<div className="group"\>...\</div\>).  
* The glow color is dynamically set to your shadcn/ui primary theme color (--primary). Ensure this is defined in your globals.css.

---

### **3\. Button Upgrades: Shiny Button**

* **Source:** dillionverma/shiny-button  
* **File Path:** components/ui/shiny-button.tsx  
* **Purpose:** A high-impact, visually distinct button for primary calls-to-action (CTAs).

**Component Code:**

TypeScript

import { cn } from "@/lib/utils";  
import React, { CSSProperties } from "react";

const ShinyButton \= React.forwardRef\<  
  HTMLButtonElement,  
  { className?: string; children: React.ReactNode }  
\>(({ className, children,...props }, ref) \=\> {  
  return (  
    \<button  
      className={cn(  
        "relative inline-flex h-12 overflow-hidden rounded-full p-\[1px\] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50",  
        className  
      )}  
      ref={ref}  
      {...props}  
    \>  
      \<span className="absolute inset-\[-1000%\] animate-\[spin\_2s\_linear\_infinite\] bg-" /\>  
      \<span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-slate-950 px-6 py-1 text-sm font-medium text-white backdrop-blur-3xl"\>  
        {children}  
      \</span\>  
    \</button\>  
  );  
});  
ShinyButton.displayName \= "ShinyButton";

export default ShinyButton;

**Implementation Specifics:**

* Replace standard Button components for key CTAs (e.g., "Get Started," "Sign Up") with this component.  
* The bg-\[conic-gradient(...)\] class defines the color of the spinning shine. You can customize the hex codes (\#E2CBFF, \#393BB2) to match your brand's palette.

---

### **4\. Application Form: Multistep Form**

* **Source:** arihantcodes/multistep-form  
* **File Path:** components/marketing/multistep-form.tsx  
* **Purpose:** A user-friendly, multi-step form to replace the single, long application form, improving user experience and completion rates.

**Component Code:**

TypeScript

"use client";  
import { useState } from "react";  
import { motion, AnimatePresence } from "framer-motion";  
import { Button } from "@/components/ui/button"; // Use your custom styled button  
import { Input } from "@/components/ui/input";   // Use your custom styled input  
import { Label } from "@/components/ui/label";   // Use your custom styled label

const steps \= },  
  { id: 2, name: "Company Information", fields: },  
  { id: 3, name: "Confirmation", fields: },  
\];

export function MultiStepForm() {  
  const \= useState(0);  
  const \= useState({});

  const next \= () \=\> setCurrentStep((prev) \=\> (prev \< steps.length \- 1? prev \+ 1 : prev));  
  const prev \= () \=\> setCurrentStep((prev) \=\> (prev \> 0? prev \- 1 : prev));

  return (  
    \<div className="w-full max-w-xl p-8 space-y-6 bg-card text-card-foreground rounded-lg shadow-md border"\>  
      \<AnimatePresence mode="wait"\>  
        \<motion.div  
          key={currentStep}  
          initial={{ x: 300, opacity: 0 }}  
          animate={{ x: 0, opacity: 1 }}  
          exit={{ x: \-300, opacity: 0 }}  
          transition={{ duration: 0.3 }}  
        \>  
          \<h2 className="text-2xl font-bold mb-6"\>{steps.name}\</h2\>  
          {/\* Render form fields based on current step \*/}  
        \</motion.div\>  
      \</AnimatePresence\>

      \<div className="flex justify-between pt-4"\>  
        \<Button onClick={prev} variant="outline" disabled={currentStep \=== 0}\>  
          Back  
        \</Button\>  
        {currentStep \< steps.length \- 1? (  
          \<Button onClick={next}\>Next\</Button\>  
        ) : (  
          \<Button\>Submit Application\</Button\>  
        )}  
      \</div\>  
    \</div\>  
  );  
}

**Implementation Specifics:**

* This is a foundational structure. You must populate the form with the actual Label and Input components for each field defined in the steps array.  
* Crucially, replace the generic button, input, and label elements from the original source with your project's shadcn/ui components (@/components/ui/button, etc.) to ensure visual consistency.  
* The final onSubmit logic needs to be wired to your backend API endpoint.

---

### **5\. Footer Redesign**

* **Source:** mdadul/footer  
* **File Path:** components/marketing/footer.tsx  
* **Purpose:** A clean, modern, and comprehensive footer for consistent branding and navigation across all pages.

**Component Code:**

TypeScript

import React from "react";  
import Link from "next/link";

export function Footer() {  
  return (  
    \<footer className="bg-background border-t text-foreground py-12"\>  
      \<div className="container mx-auto px-4"\>  
        \<div className="grid grid-cols-2 md:grid-cols-4 gap-8"\>  
          \<div\>  
            \<h2 className="text-xl font-bold mb-4"\>Vierla\</h2\>  
            \<p className="text-muted-foreground text-sm"\>  
              The AI-Powered Platform for Modern Business.  
            \</p\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Product\</h3\>  
            \<ul className="space-y-2 text-sm"\>  
              \<li\>\<Link href="/features" className="text-muted-foreground hover:text-foreground"\>Features\</Link\>\</li\>  
              \<li\>\<Link href="/pricing" className="text-muted-foreground hover:text-foreground"\>Pricing\</Link\>\</li\>  
            \</ul\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Company\</h3\>  
            \<ul className="space-y-2 text-sm"\>  
              \<li\>\<Link href="/about" className="text-muted-foreground hover:text-foreground"\>About Us\</Link\>\</li\>  
              \<li\>\<Link href="/contact" className="text-muted-foreground hover:text-foreground"\>Contact\</Link\>\</li\>  
            \</ul\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Legal\</h3\>  
            \<ul className="space-y-2 text-sm"\>  
              \<li\>\<Link href="/privacy" className="text-muted-foreground hover:text-foreground"\>Privacy Policy\</Link\>\</li\>  
              \<li\>\<Link href="/terms" className="text-muted-foreground hover:text-foreground"\>Terms of Service\</Link\>\</li\>  
            \</ul\>  
          \</div\>  
        \</div\>  
        \<div className="mt-10 border-t border-border pt-8 text-center text-muted-foreground text-xs"\>  
          \<p\>© {new Date().getFullYear()} Vierla, Inc. All rights reserved.\</p\>  
        \</div\>  
      \</div\>  
    \</footer\>  
  );  
}

**Implementation Specifics:**

* This component uses semantic colors from the shadcn/ui theme system (background, foreground, muted-foreground, border). It will automatically adapt to your light/dark themes.  
* Update the links in the \<ul\> elements to match the pages that exist in your application.

---

### **6\. Navigation Bar**

* **Source:** shadcnblockscom/shadcnblocks-com-navbar1  
* **File Path:** components/marketing/navbar.tsx  
* **Purpose:** A responsive, clean, and professional navigation bar for the site header.

**Component Code:**

TypeScript

import Link from "next/link";  
import { Button } from "@/components/ui/button";  
import ShinyButton from "@/components/ui/shiny-button"; // Import the new button  
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";  
import { Menu } from "lucide-react";

export function Navbar() {  
  return (  
    \<header className="flex h-20 w-full items-center justify-between px-4 md:px-6 bg-transparent absolute top-0 left-0 z-50"\>  
      \<Link href="/" className="flex items-center gap-2" prefetch={false}\>  
        \<span className="text-lg font-semibold"\>Vierla\</span\>  
      \</Link\>  
      \<nav className="hidden items-center gap-6 text-sm font-medium md:flex"\>  
        \<Link href="/features" className="hover:underline underline-offset-4"\>Features\</Link\>  
        \<Link href="/pricing" className="hover:underline underline-offset-4"\>Pricing\</Link\>  
        \<Link href="/about" className="hover:underline underline-offset-4"\>About\</Link\>  
      \</nav\>  
      \<div className="hidden items-center gap-4 md:flex"\>  
        \<Button variant="ghost"\>Login\</Button\>  
        \<Link href="/apply"\>\<ShinyButton\>Get Started\</ShinyButton\>\</Link\>  
      \</div\>  
      \<Sheet\>  
        \<SheetTrigger asChild\>  
          \<Button variant="outline" size="icon" className="md:hidden"\>  
            \<Menu className="h-6 w-6" /\>  
            \<span className="sr-only"\>Toggle navigation menu\</span\>  
          \</Button\>  
        \</SheetTrigger\>  
        \<SheetContent side="right"\>  
          {/\* Mobile menu content here \*/}  
        \</SheetContent\>  
      \</Sheet\>  
    \</header\>  
  );  
}

**Implementation Specifics:**

* The primary "Get Started" Button has been replaced with the ShinyButton component, wrapped in a Link pointing to /apply.  
* The mobile menu content inside SheetContent needs to be populated with the same navigation links for a consistent mobile experience.  
* This component is designed to be placed in your root layout (app/layout.tsx) to appear on all pages.