import { NextRequest, NextResponse } from 'next/server'
import { sendEmail, createProfessionalApplicationNotificationEmail } from '@/lib/email'

interface ProfessionalApplicationData {
  firstName: string
  lastName: string
  email: string
  phone: string
  businessName: string
  services: string[]
  experience: string
  certifications: string[]
  serviceAreas: string[]
  availability: string[]
  travelRadius: string
  insurance: boolean
  license: string
  portfolio: string
  rates: string
  motivation: string
  references: string
  timestamp: string
}

export async function POST(request: NextRequest) {
  try {
    const body: ProfessionalApplicationData = await request.json()
    
    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'services', 'experience', 'license', 'motivation']
    const missingFields = requiredFields.filter(field => {
      const value = body[field as keyof ProfessionalApplicationData]
      return !value || (Array.isArray(value) && value.length === 0)
    })
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      )
    }

    // Validate phone format (basic validation)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    const cleanPhone = body.phone.replace(/[\s\-\(\)]/g, '')
    if (!phoneRegex.test(cleanPhone)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid phone number format'
        },
        { status: 400 }
      )
    }

    // Validate experience level
    const validExperience = ['1-2', '3-5', '6-10', '10+']
    if (!validExperience.includes(body.experience)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid experience level'
        },
        { status: 400 }
      )
    }

    // Validate services (at least one required)
    if (!body.services || body.services.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'At least one service must be selected'
        },
        { status: 400 }
      )
    }

    // Generate application ID
    const applicationId = `APP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Log the application
    console.log('Professional application received:', {
      id: applicationId,
      name: `${body.firstName} ${body.lastName}`,
      email: body.email,
      phone: body.phone,
      servicesCount: body.services.length,
      experience: body.experience,
      hasInsurance: body.insurance,
      hasPortfolio: !!body.portfolio,
      timestamp: body.timestamp,
      ip: request.ip || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    // Send email notification to application team
    try {
      const emailHtml = createProfessionalApplicationNotificationEmail(body)
      await sendEmail({
        to: '<EMAIL>',
        subject: `New Professional Application: ${body.firstName} ${body.lastName}`,
        html: emailHtml
      })
    } catch (emailError) {
      console.error('Failed to send application notification email:', emailError)
      // Continue processing even if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Professional application submitted successfully',
      data: {
        applicationId,
        timestamp: new Date().toISOString(),
        status: 'pending_review',
        nextSteps: [
          'Application review (3-5 business days)',
          'Background check and license verification',
          'Video interview scheduling',
          'Platform onboarding and training'
        ]
      }
    })

  } catch (error) {
    console.error('Professional application error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
