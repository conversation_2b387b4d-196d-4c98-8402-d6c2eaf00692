{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES2022", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/hooks/*": ["./hooks/*"], "@/lib/*": ["./lib/*"], "@/app/*": ["./app/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "dist", "build", "logs"]}