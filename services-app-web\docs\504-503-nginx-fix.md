# Vierla Web Reverse-Proxy Troubleshooting & Fixes
This document outlines the issues encountered and the step-by-step resolutions applied to get <PERSON>in<PERSON> properly proxying to our Next.js app running in Docker. Paste this into your GitLab repo (e.g. docs/reverse-proxy-troubleshooting.md).

## 1. Symptoms
- <PERSON><PERSON><PERSON> shows a blank page and
504 Gateway Timeout on GET / and /sw.js.
- Nginx logs report upstream timeouts and 499 errors.

## 2. Environment
- Next.js app in a Docker container named vierla-web, listening on port 3000.
- <PERSON>inx (host) configured as a reverse proxy (HTTPS) to 127.0.0.1:3000.
- Docker Compose v3.8 with custom web and vierla-internal networks.
- UFW/Iptables firewall on the host.

## 3. Troubleshooting Steps
3.1 Verify the app inside the container
```bash
docker exec -it vierla-web sh -c "
  wget -qO- http://127.0.0.1:3000/ | head -n5 || echo 'FAILED'
"
```
Result:
Next.js returned HTML successfully, confirming the app itself was healthy
---
### 3.2 Test Nginx’s HTTP proxy on port 8080
- Add a temporary vhost /etc/nginx/conf.d/test-8080.conf:
```bash
upstream vierla_upstream { server 127.0.0.1:3000; }
server {
  listen 8080 default_server;
  server_name _;
  error_log  /var/log/nginx/test-8080.error.log debug;
  location / {
    proxy_pass         http://vierla_upstream;
  }
}
```
- Reload Nginx and curl it:
```bash
sudo nginx -t && sudo systemctl reload nginx
curl -v http://127.0.0.1:8080 | head -n5
```
Outcome: curl hung indefinitely, showing Nginx never got a response through the proxy
## 3.3 Enable and inspect debug logs
- Added error_log … debug; to the test vhost.
- Observed Nginx logs showing:
```bash
connect to 127.0.0.1:3000, fd:…
upstream connect: -2
client prematurely closed connection
```
This proved Nginx was opening the upstream connection but never receiving a reply.
## 3.4 Forward the correct Host header
Nginx defaulted to Host: vierla_upstream, which Next.js ignored.
Fix: In both the temporary vhost and production HTTPS vhost, add:
```bash
proxy_set_header Host              $host;
proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
```
Reload Nginx.  This ensured Next.js saw Host: vierla.com.
## 3.5 Ensure Next.js binds to all interfaces
By default, next start may bind only to localhost.
Fix: In the Dockerfile’s CMD, use
```Dockerfile
CMD ["node", "server.js", "--hostname", "0.0.0.0", "--port", "3000"]
```
Rebuild and restart the container.  Inside the container, netstat -tln showed 0.0.0.0:3000.
## 3.6 Verify Docker port binding on the host
docker ps confirmed 0.0.0.0:3000->3000/tcp.  Yet from the host
```bash
curl -I http://127.0.0.1:3000   # hung
curl -I http://**********:3000  # hung
```
Docker’s DOCKER-USER chain also sat at the top, dropping all forwarded packets.
## 3.8 Allow Docker forwarding
Option A: Quick unblock (all Docker traffic)
```bash
sudo iptables -I DOCKER-USER 1 -j ACCEPT
sudo iptables -P FORWARD ACCEPT'
```
Option B: UFW-friendly fix
- In /etc/default/ufw, set
DEFAULT_FORWARD_POLICY="ACCEPT"
- In /etc/ufw/before.rules (above *filter), add:
```bash
*nat
:PREROUTING ACCEPT [0:0]
-A PREROUTING -p tcp --dport 3000 -j REDIRECT --to-ports 3000
COMMIT
```
- sudo ufw reload
After this, curl -I http://127.0.0.1:3000 returned HTTP/1.1 200 OK.
## 4. Final Configurations
### 4.1 docker-compose.yml
```yaml
version: '3.8'

services:
  vierla-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vierla-web
    restart: unless-stopped

    ports:
      - '3000:3000'

    environment:
      - NODE_ENV=production
      - PORT=3000
      - APP_NAME=Vierla
      - APP_VERSION=1.0.0

    volumes:
      - ./logs:/app/logs

    networks:
      - web

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

networks:
  web:
    external: true
```
### 4.2 Dockerfile (CMD line)
```Dockerfile
EXPOSE 3000
CMD ["node", "server.js", "--hostname", "0.0.0.0", "--port", "3000"]
```
### 4.3 Nginx HTTPS server block (/etc/nginx/conf.d/vierla.conf)
```bash
upstream vierla_upstream {
    server 127.0.0.1:3000;
}

server {
    listen 443 ssl http2;
    server_name vierla.com www.vierla.com;

    ssl_certificate     /etc/letsencrypt/live/vierla.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/vierla.com/privkey.pem;
    include             /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam         /etc/letsencrypt/ssl-dhparams.pem;

    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    access_log  /var/log/nginx/vierla.access.log  combined;
    error_log   /var/log/nginx/vierla.error.log  warn;

    location / {
        if ($request_method = HEAD) {
            proxy_method GET;
        }
        proxy_pass         http://vierla_upstream;
        proxy_set_header   Host              $host;
        proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_connect_timeout  60s;
        proxy_read_timeout     60s;
        proxy_send_timeout     60s;
    }

    location /stats {
        alias /var/www/goaccess/html;
        index index.html;
    }
}
```
## 5. Verification
- Inside container
```bash
wget -qO- http://127.0.0.1:3000/ | head -n1  # returns <DOCTYPE html>
```
- On host
```bash
curl -I http://127.0.0.1:3000     # HTTP/1.1 200 OK
curl -I https://127.0.0.1 -k      # HTTP/2 200
curl -I https://vierla.com       # HTTP/2 200
```
After these steps, Nginx successfully proxies to your app, and the 504 Gateway Timeout and blank-page issues are fully resolved.
