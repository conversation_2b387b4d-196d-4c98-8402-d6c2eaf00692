import { ShimmerButton } from "@/components/ui/shimmer-button";
import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface GetStartedButtonProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "secondary" | "accent";
  shimmerColor?: string;
  backgroundColor?: string;
  onClick?: () => void;
  href?: string;
}

export function GetStartedButton({
  className,
  size = "lg",
  variant = "primary",
  shimmerColor,
  backgroundColor,
  onClick,
  href,
}: GetStartedButtonProps) {
  return (
    <ShimmerButton
      className={cn("group relative overflow-hidden", className)}
      size={size}
      variant={variant}
      shimmerColor={shimmerColor}
      background={backgroundColor}
      onClick={onClick}
    >
      <span className="mr-8 transition-opacity duration-500 group-hover:opacity-0">
        Get Started
      </span>
      <i className="absolute right-0.5 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-white/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95">
        <ChevronRight size={16} strokeWidth={2} aria-hidden="true" className="text-white" />
      </i>
    </ShimmerButton>
  );
}
