"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { <PERSON><PERSON>, Setting<PERSON>, X } from 'lucide-react';

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

export function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always required
    analytics: false,
    marketing: false
  });

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('vierla-cookie-consent');
    if (!consent) {
      setShowBanner(true);
    } else {
      const savedPreferences = JSON.parse(consent);
      setPreferences(savedPreferences);
      applyCookieSettings(savedPreferences);
    }
  }, []);

  const applyCookieSettings = (prefs: CookiePreferences) => {
    // Apply analytics cookies
    if (prefs.analytics) {
      // Enable Google Analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'analytics_storage': 'granted'
        });
      }
    } else {
      // Disable analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'analytics_storage': 'denied'
        });
      }
    }

    // Apply marketing cookies
    if (prefs.marketing) {
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'ad_storage': 'granted'
        });
      }
    } else {
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'ad_storage': 'denied'
        });
      }
    }
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true
    };
    setPreferences(allAccepted);
    localStorage.setItem('vierla-cookie-consent', JSON.stringify(allAccepted));
    applyCookieSettings(allAccepted);
    setShowBanner(false);
  };

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      analytics: false,
      marketing: false
    };
    setPreferences(onlyNecessary);
    localStorage.setItem('vierla-cookie-consent', JSON.stringify(onlyNecessary));
    applyCookieSettings(onlyNecessary);
    setShowBanner(false);
  };

  const handleSavePreferences = () => {
    localStorage.setItem('vierla-cookie-consent', JSON.stringify(preferences));
    applyCookieSettings(preferences);
    setShowBanner(false);
    setShowPreferences(false);
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Cookie Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/70 bg-neutral-charcoal-dark/80 border-t border-brand-sage/20">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            <div className="flex items-center gap-3 flex-1">
              <Cookie className="w-6 h-6 flex-shrink-0 text-neutral-off-white" />
              <div>
                <h3 className="font-semibold text-sm text-neutral-off-white">We use cookies</h3>
                <p className="text-sm text-brand-beige">
                  We use cookies to enhance your experience and analyze our traffic.
                  <Link href="/privacy" className="underline ml-1 text-neutral-off-white hover:text-brand-gold transition-colors">
                    Learn more
                  </Link>
                </p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setShowPreferences(true)}
                className="px-4 py-2 text-sm rounded-lg transition-colors bg-neutral-charcoal-light/90 border border-brand-sage text-neutral-off-white hover:bg-brand-sage/20"
              >
                <Settings className="w-4 h-4 inline mr-2" />
                Preferences
              </button>
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm rounded-lg transition-colors bg-neutral-charcoal-light/90 border border-brand-sage text-neutral-off-white hover:bg-error/20"
              >
                Reject All
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-4 py-2 text-sm rounded-lg font-medium transition-colors bg-brand-gold border border-brand-gold text-neutral-charcoal-dark hover:bg-gold-600"
              >
                Accept All
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Preferences Modal */}
      {showPreferences && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-neutral-charcoal-dark/40 backdrop-blur-sm">
          <div className="backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/70 bg-neutral-charcoal-dark/80 rounded-xl p-6 max-w-md w-full border border-brand-sage/20 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-semibold text-lg text-neutral-off-white">Cookie Preferences</h2>
              <button
                onClick={() => setShowPreferences(false)}
                className="text-brand-beige hover:text-neutral-off-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-neutral-off-white">Necessary</h3>
                  <p className="text-sm text-brand-beige">Required for basic functionality</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.necessary}
                  disabled
                  className="w-4 h-4 accent-brand-sage"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-neutral-off-white">Analytics</h3>
                  <p className="text-sm text-brand-beige">Help us improve our service</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.analytics}
                  onChange={(e) => setPreferences(prev => ({ ...prev, analytics: e.target.checked }))}
                  className="w-4 h-4 accent-brand-sage"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-neutral-off-white">Marketing</h3>
                  <p className="text-sm text-brand-beige">Personalized ads and content</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.marketing}
                  onChange={(e) => setPreferences(prev => ({ ...prev, marketing: e.target.checked }))}
                  className="w-4 h-4 accent-brand-sage"
                />
              </div>
            </div>
            
            <div className="flex gap-2 mt-6">
              <button
                onClick={() => setShowPreferences(false)}
                className="flex-1 px-4 py-2 text-sm rounded-lg transition-colors bg-neutral-charcoal-light/90 border border-brand-sage text-neutral-off-white hover:bg-brand-sage/20"
              >
                Cancel
              </button>
              <button
                onClick={handleSavePreferences}
                className="flex-1 px-4 py-2 text-sm rounded-lg font-medium transition-colors bg-brand-gold border border-brand-gold text-neutral-charcoal-dark hover:bg-gold-600"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
