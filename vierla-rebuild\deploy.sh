#!/bin/bash

# Vierla Deployment Script
# This script automates the deployment process for the Vierla application

set -e
set -o pipefail  # Catch errors in piped commands

echo "🚀 Starting Vierla deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Determine Docker Compose command (v1 vs v2)
DOCKER_COMPOSE_CMD=""
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Using Docker Compose command: $DOCKER_COMPOSE_CMD"

# Ensure the external network exists for reverse proxy
print_status "Ensuring external network 'web' exists for reverse proxy..."
docker network create web 2>/dev/null || print_status "Network 'web' already exists"

# Stop existing containers
print_status "Stopping existing containers..."
$DOCKER_COMPOSE_CMD down || true

# Remove old images (optional, uncomment if you want to force rebuild)
# print_status "Removing old images..."
# $DOCKER_COMPOSE_CMD down --rmi all || true

# Pull latest changes (if in git repository)
if [ -d ".git" ]; then
    print_status "Pulling latest changes from repository..."
    git pull origin main || print_warning "Failed to pull latest changes. Continuing with current code."
fi

# Build and start containers
print_status "Building and starting containers..."
$DOCKER_COMPOSE_CMD up -d --build

# Wait for containers to be healthy
print_status "Waiting for containers to be healthy..."
TIMEOUT=120  # 2 minutes timeout
ELAPSED=0
while [ $ELAPSED -lt $TIMEOUT ]; do
    if $DOCKER_COMPOSE_CMD ps --filter "health=healthy" | grep -q vierla-web; then
        print_status "✅ Container is healthy!"
        break
    fi

    if $DOCKER_COMPOSE_CMD ps | grep -q "Exit"; then
        print_error "❌ Container exited unexpectedly. Checking logs..."
        $DOCKER_COMPOSE_CMD logs
        exit 1
    fi

    sleep 5
    ELAPSED=$((ELAPSED + 5))
    print_status "Waiting for health check... (${ELAPSED}s/${TIMEOUT}s)"
done

if [ $ELAPSED -ge $TIMEOUT ]; then
    print_warning "Health check timeout reached. Container may still be starting..."
fi

# Check container status
print_status "Checking container status..."
if $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
    print_status "✅ Deployment successful! Vierla is now running."
    print_status "Application is accessible via reverse proxy (not directly on port 3000)"
    print_status "Container status:"
    $DOCKER_COMPOSE_CMD ps
else
    print_error "❌ Deployment failed. Checking logs..."
    $DOCKER_COMPOSE_CMD logs
    exit 1
fi

# Show logs
print_status "Recent logs:"
$DOCKER_COMPOSE_CMD logs --tail=20

# Optional: Clean up unused Docker resources
print_status "Cleaning up unused Docker resources..."
docker system prune -f --volumes || print_warning "Failed to clean up Docker resources"

print_status "🎉 Deployment completed successfully!"
print_status "To view logs: $DOCKER_COMPOSE_CMD logs -f"
print_status "To stop: $DOCKER_COMPOSE_CMD down"
print_status "To restart: $DOCKER_COMPOSE_CMD restart"
print_status "Note: Application is only accessible via your reverse proxy, not directly on port 3000"
