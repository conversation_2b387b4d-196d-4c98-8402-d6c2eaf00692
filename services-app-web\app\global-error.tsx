'use client'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <html>
      <body>
        <div className="min-h-screen bg-gradient-to-br from-[#364035] via-[#8B9A8C] to-[#364035] flex items-center justify-center px-4">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-4">Something went wrong!</h1>
            <p className="text-white/80 mb-8 max-w-md mx-auto">
              We encountered an unexpected error. Please try again.
            </p>
            <button
              onClick={() => reset()}
              className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-all duration-300 mr-4"
            >
              Try again
            </button>
            <a
              href="/"
              className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white font-medium hover:bg-white/20 transition-all duration-300"
            >
              Go home
            </a>
          </div>
        </div>
      </body>
    </html>
  )
}
