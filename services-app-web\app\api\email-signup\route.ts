import { NextRequest, NextResponse } from 'next/server'

interface EmailSignupData {
  email: string
  source: string
  timestamp: string
}

export async function POST(request: NextRequest) {
  try {
    const body: EmailSignupData = await request.json()
    
    // Validate required fields
    if (!body.email || !body.source) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email and source are required'
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      )
    }

    // Here you would typically save to a database
    // For now, we'll just log it and return success
    console.log('Email signup received:', {
      email: body.email,
      source: body.source,
      timestamp: body.timestamp,
      ip: request.ip || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    // In a real implementation, you might:
    // 1. Save to database
    // 2. Add to email marketing service (Mailchimp, SendGrid, etc.)
    // 3. Send confirmation email
    // 4. Check for duplicates

    return NextResponse.json({
      success: true,
      message: 'Email signup successful',
      data: {
        email: body.email,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Email signup error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
