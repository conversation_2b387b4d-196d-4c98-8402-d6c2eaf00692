import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import nodemailer from 'nodemailer';
import fs from 'fs'; // This line imports the required module

// --- Interfaces for your data structures ---
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// --- Transporter Creation ---
function createTransporter(): nodemailer.Transporter {
  // Read the API key from the Docker secret file
  const apiKey = fs.readFileSync('/run/secrets/sendgrid_api_key', 'utf8').trim();

  const config: EmailConfig = {
    host: process.env.SMTP_HOST || 'smtp.sendgrid.net',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true', // This should be false for port 587
    auth: {
      user: process.env.SMTP_USER || 'apikey',
      pass: apiKey, // Use the secret from the file
    },
  };

  return nodemailer.createTransport(config);
}

// --- Main Email Sending Function ---
export async function sendEmail(emailData: EmailData): Promise<boolean> {
  try {
    const transporter = createTransporter();
    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text || emailData.html.replace(/<[^>]*>/g, ''),
    };
    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return true;
  } catch (error) {
    console.error('Email sending failed:', error);
    return false;
  }
}


// Email templates
export function createContactFormNotificationEmail(formData: any): string {
  return `
    <h2>New Contact Form Submission</h2>
    <p><strong>Name:</strong> ${formData.name}</p>
    <p><strong>Email:</strong> ${formData.email}</p>
    <p><strong>Subject:</strong> ${formData.subject}</p>
    <p><strong>Type:</strong> ${formData.type}</p>
    <p><strong>Message:</strong></p>
    <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
      ${formData.message.replace(/\n/g, '<br>')}
    </div>
    <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
  `
}

export function createProfessionalApplicationNotificationEmail(applicationData: any): string {
  return `
    <h2>New Professional Application</h2>
    <p><strong>Name:</strong> ${applicationData.firstName} ${applicationData.lastName}</p>
    <p><strong>Email:</strong> ${applicationData.email}</p>
    <p><strong>Phone:</strong> ${applicationData.phone}</p>
    <p><strong>Business Name:</strong> ${applicationData.businessName || 'Not provided'}</p>
    <p><strong>Services:</strong> ${applicationData.services.join(', ')}</p>
    <p><strong>Experience:</strong> ${applicationData.experience} years</p>
    <p><strong>License:</strong> ${applicationData.license}</p>
    <p><strong>Insurance:</strong> ${applicationData.insurance ? 'Yes' : 'No'}</p>
    <p><strong>Travel Radius:</strong> ${applicationData.travelRadius || 'Not specified'}</p>
    <p><strong>Motivation:</strong></p>
    <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
      ${applicationData.motivation.replace(/\n/g, '<br>')}
    </div>
    <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
  `
}
