{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b625b40b.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-inter: 'Inter', 'Inter Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_91df29e4.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-dm-serif-display: 'Inter', 'Inter Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: var(--font-inter); /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n:root {\n  --inherit: inherit;\n  --current: currentColor;\n  --transparent: transparent;\n  --black: #000;\n  --white: #ffffff;\n  --slate-50: #f8fafc;\n  --slate-100: #f1f5f9;\n  --slate-200: #e2e8f0;\n  --slate-300: #cbd5e1;\n  --slate-400: #94a3b8;\n  --slate-500: #64748b;\n  --slate-600: #475569;\n  --slate-700: #334155;\n  --slate-800: #1e293b;\n  --slate-900: #0f172a;\n  --slate-950: #020617;\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n  --gray-950: #030712;\n  --zinc-50: #fafafa;\n  --zinc-100: #f4f4f5;\n  --zinc-200: #e4e4e7;\n  --zinc-300: #d4d4d8;\n  --zinc-400: #a1a1aa;\n  --zinc-500: #71717a;\n  --zinc-600: #52525b;\n  --zinc-700: #3f3f46;\n  --zinc-800: #27272a;\n  --zinc-900: #18181b;\n  --zinc-950: #09090b;\n  --neutral-50: #fafafa;\n  --neutral-100: #f5f5f5;\n  --neutral-200: #e5e5e5;\n  --neutral-300: #d4d4d4;\n  --neutral-400: #a3a3a3;\n  --neutral-500: #737373;\n  --neutral-600: #525252;\n  --neutral-700: #404040;\n  --neutral-800: #262626;\n  --neutral-900: #171717;\n  --neutral-950: #0a0a0a;\n  --stone-50: #fafaf9;\n  --stone-100: #f5f5f4;\n  --stone-200: #e7e5e4;\n  --stone-300: #d6d3d1;\n  --stone-400: #a8a29e;\n  --stone-500: #78716c;\n  --stone-600: #57534e;\n  --stone-700: #44403c;\n  --stone-800: #292524;\n  --stone-900: #1c1917;\n  --stone-950: #0c0a09;\n  --red-50: #fef2f2;\n  --red-100: #fee2e2;\n  --red-200: #fecaca;\n  --red-300: #fca5a5;\n  --red-400: #f87171;\n  --red-500: #ef4444;\n  --red-600: #dc2626;\n  --red-700: #b91c1c;\n  --red-800: #991b1b;\n  --red-900: #7f1d1d;\n  --red-950: #450a0a;\n  --orange-50: #fff7ed;\n  --orange-100: #ffedd5;\n  --orange-200: #fed7aa;\n  --orange-300: #fdba74;\n  --orange-400: #fb923c;\n  --orange-500: #f97316;\n  --orange-600: #ea580c;\n  --orange-700: #c2410c;\n  --orange-800: #9a3412;\n  --orange-900: #7c2d12;\n  --orange-950: #431407;\n  --amber-50: #fffbeb;\n  --amber-100: #fef3c7;\n  --amber-200: #fde68a;\n  --amber-300: #fcd34d;\n  --amber-400: #fbbf24;\n  --amber-500: #f59e0b;\n  --amber-600: #d97706;\n  --amber-700: #b45309;\n  --amber-800: #92400e;\n  --amber-900: #78350f;\n  --amber-950: #451a03;\n  --yellow-50: #fefce8;\n  --yellow-100: #fef9c3;\n  --yellow-200: #fef08a;\n  --yellow-300: #fde047;\n  --yellow-400: #facc15;\n  --yellow-500: #eab308;\n  --yellow-600: #ca8a04;\n  --yellow-700: #a16207;\n  --yellow-800: #854d0e;\n  --yellow-900: #713f12;\n  --yellow-950: #422006;\n  --lime-50: #f7fee7;\n  --lime-100: #ecfccb;\n  --lime-200: #d9f99d;\n  --lime-300: #bef264;\n  --lime-400: #a3e635;\n  --lime-500: #84cc16;\n  --lime-600: #65a30d;\n  --lime-700: #4d7c0f;\n  --lime-800: #3f6212;\n  --lime-900: #365314;\n  --lime-950: #1a2e05;\n  --green-50: #f0fdf4;\n  --green-100: #dcfce7;\n  --green-200: #bbf7d0;\n  --green-300: #86efac;\n  --green-400: #4ade80;\n  --green-500: #22c55e;\n  --green-600: #16a34a;\n  --green-700: #15803d;\n  --green-800: #166534;\n  --green-900: #14532d;\n  --green-950: #052e16;\n  --emerald-50: #ecfdf5;\n  --emerald-100: #d1fae5;\n  --emerald-200: #a7f3d0;\n  --emerald-300: #6ee7b7;\n  --emerald-400: #34d399;\n  --emerald-500: #10b981;\n  --emerald-600: #059669;\n  --emerald-700: #047857;\n  --emerald-800: #065f46;\n  --emerald-900: #064e3b;\n  --emerald-950: #022c22;\n  --teal-50: #f0fdfa;\n  --teal-100: #ccfbf1;\n  --teal-200: #99f6e4;\n  --teal-300: #5eead4;\n  --teal-400: #2dd4bf;\n  --teal-500: #14b8a6;\n  --teal-600: #0d9488;\n  --teal-700: #0f766e;\n  --teal-800: #115e59;\n  --teal-900: #134e4a;\n  --teal-950: #042f2e;\n  --cyan-50: #ecfeff;\n  --cyan-100: #cffafe;\n  --cyan-200: #a5f3fc;\n  --cyan-300: #67e8f9;\n  --cyan-400: #22d3ee;\n  --cyan-500: #06b6d4;\n  --cyan-600: #0891b2;\n  --cyan-700: #0e7490;\n  --cyan-800: #155e75;\n  --cyan-900: #164e63;\n  --cyan-950: #083344;\n  --sky-50: #f0f9ff;\n  --sky-100: #e0f2fe;\n  --sky-200: #bae6fd;\n  --sky-300: #7dd3fc;\n  --sky-400: #38bdf8;\n  --sky-500: #0ea5e9;\n  --sky-600: #0284c7;\n  --sky-700: #0369a1;\n  --sky-800: #075985;\n  --sky-900: #0c4a6e;\n  --sky-950: #082f49;\n  --blue-50: #eff6ff;\n  --blue-100: #dbeafe;\n  --blue-200: #bfdbfe;\n  --blue-300: #93c5fd;\n  --blue-400: #60a5fa;\n  --blue-500: #3b82f6;\n  --blue-600: #2563eb;\n  --blue-700: #1d4ed8;\n  --blue-800: #1e40af;\n  --blue-900: #1e3a8a;\n  --blue-950: #172554;\n  --indigo-50: #eef2ff;\n  --indigo-100: #e0e7ff;\n  --indigo-200: #c7d2fe;\n  --indigo-300: #a5b4fc;\n  --indigo-400: #818cf8;\n  --indigo-500: #6366f1;\n  --indigo-600: #4f46e5;\n  --indigo-700: #4338ca;\n  --indigo-800: #3730a3;\n  --indigo-900: #312e81;\n  --indigo-950: #1e1b4b;\n  --violet-50: #f5f3ff;\n  --violet-100: #ede9fe;\n  --violet-200: #ddd6fe;\n  --violet-300: #c4b5fd;\n  --violet-400: #a78bfa;\n  --violet-500: #8b5cf6;\n  --violet-600: #7c3aed;\n  --violet-700: #6d28d9;\n  --violet-800: #5b21b6;\n  --violet-900: #4c1d95;\n  --violet-950: #2e1065;\n  --purple-50: #faf5ff;\n  --purple-100: #f3e8ff;\n  --purple-200: #e9d5ff;\n  --purple-300: #d8b4fe;\n  --purple-400: #c084fc;\n  --purple-500: #a855f7;\n  --purple-600: #9333ea;\n  --purple-700: #7e22ce;\n  --purple-800: #6b21a8;\n  --purple-900: #581c87;\n  --purple-950: #3b0764;\n  --fuchsia-50: #fdf4ff;\n  --fuchsia-100: #fae8ff;\n  --fuchsia-200: #f5d0fe;\n  --fuchsia-300: #f0abfc;\n  --fuchsia-400: #e879f9;\n  --fuchsia-500: #d946ef;\n  --fuchsia-600: #c026d3;\n  --fuchsia-700: #a21caf;\n  --fuchsia-800: #86198f;\n  --fuchsia-900: #701a75;\n  --fuchsia-950: #4a044e;\n  --pink-50: #fdf2f8;\n  --pink-100: #fce7f3;\n  --pink-200: #fbcfe8;\n  --pink-300: #f9a8d4;\n  --pink-400: #f472b6;\n  --pink-500: #ec4899;\n  --pink-600: #db2777;\n  --pink-700: #be185d;\n  --pink-800: #9d174d;\n  --pink-900: #831843;\n  --pink-950: #500724;\n  --rose-50: #fff1f2;\n  --rose-100: #ffe4e6;\n  --rose-200: #fecdd3;\n  --rose-300: #fda4af;\n  --rose-400: #fb7185;\n  --rose-500: #f43f5e;\n  --rose-600: #e11d48;\n  --rose-700: #be123c;\n  --rose-800: #9f1239;\n  --rose-900: #881337;\n  --rose-950: #4c0519;\n  --mantle-50: #f6f7f6;\n  --mantle-100: #e1e6e1;\n  --mantle-200: #c3ccc3;\n  --mantle-300: #8b9a8c;\n  --mantle-400: #79887a;\n  --mantle-500: #5f6d61;\n  --mantle-600: #4a574b;\n  --mantle-700: #3e473f;\n  --mantle-800: #343b36;\n  --mantle-900: #2e332f;\n  --mantle-950: #181b19;\n  --terracotta-50: #FEF3E2;\n  --terracotta-100: #FDE4B8;\n  --terracotta-200: #FBD18A;\n  --terracotta-300: #F9BE5C;\n  --terracotta-400: #F7AB2E;\n  --terracotta-500: #D97706;\n  --terracotta-600: #B45309;\n  --terracotta-700: #8F420B;\n  --terracotta-800: #6A310C;\n  --terracotta-900: #45200D;\n  --terracotta-DEFAULT: #D97706;\n  --gold-50: #FEF9E7;\n  --gold-100: #FDF2C4;\n  --gold-200: #FCE8A1;\n  --gold-300: #FBDE7E;\n  --gold-400: #FAD45B;\n  --gold-500: #CA8A04;\n  --gold-600: #A16207;\n  --gold-700: #784A0A;\n  --gold-800: #4F320C;\n  --gold-900: #261A0E;\n  --gold-DEFAULT: #CA8A04;\n  --brand-neutral-charcoal: #2e332f;\n  --brand-neutral-gray: #8b9a8c;\n  --brand-primary: #79887a;\n  --border: hsl(var(--border));\n  --input: hsl(var(--input));\n  --ring: hsl(var(--ring));\n  --background: hsl(var(--background));\n  --foreground: hsl(var(--foreground));\n  --primary-DEFAULT: hsl(var(--primary));\n  --primary-foreground: hsl(var(--primary-foreground));\n  --secondary-DEFAULT: hsl(var(--secondary));\n  --secondary-foreground: hsl(var(--secondary-foreground));\n  --destructive-DEFAULT: hsl(var(--destructive));\n  --destructive-foreground: hsl(var(--destructive-foreground));\n  --muted-DEFAULT: hsl(var(--muted));\n  --muted-foreground: hsl(var(--muted-foreground));\n  --accent-DEFAULT: hsl(var(--accent));\n  --accent-foreground: hsl(var(--accent-foreground));\n  --popover-DEFAULT: hsl(var(--popover));\n  --popover-foreground: hsl(var(--popover-foreground));\n  --card-DEFAULT: hsl(var(--card));\n  --card-foreground: hsl(var(--card-foreground));\n  --aurora-bg: #8b9a8c;\n  --aurora-bg-dark: var(--mantle-900);\n  --aurora-stripe-light: var(--white);\n  --aurora-stripe-dark: var(--mantle-800);\n  --aurora-flow-1: var(--white);\n  --aurora-flow-2: var(--mantle-50);\n  --aurora-flow-3: var(--mantle-100);\n  --aurora-flow-4: var(--mantle-200);\n  --aurora-flow-5: var(--mantle-100);\n    /*\n    ========================================\n    | 🎨 MASTER CONTROL PANEL\n    | A redesigned, cohesive theme for a modern beauty brand.\n    ========================================\n    */\n\n    /* iOS Safe Area Insets */\n    --safe-area-inset-top: env(safe-area-inset-top);\n    --safe-area-inset-right: env(safe-area-inset-right);\n    --safe-area-inset-bottom: env(safe-area-inset-bottom);\n    --safe-area-inset-left: env(safe-area-inset-left);\n\n    /* Master Brand & Accent Colors - Vierla Design System */\n    --master-brand-primary: var(--mantle-400);      /* Soft, earthy sage for neutral elements */\n    --master-brand-secondary: var(--mantle-300);    /* Main background color */\n    --master-brand-accent: #CA8A04;                 /* Gold for highlights and hover effects */\n    --master-brand-glow: var(--mantle-200);         /* Subtle glow effect */\n    --master-action-primary: #D97706;               /* Terracotta for primary actions/buttons */\n    --master-action-hover: #B45309;                 /* Darker terracotta for hover states */\n\n    /* Master Text Colors */\n    --master-text-primary: var(--mantle-950);       /* Deep charcoal for main text */\n    --master-text-secondary: var(--mantle-600);     /* Softer gray for subtitles */\n    --master-text-on-dark: var(--mantle-50);        /* Off-white for dark backgrounds */\n\n    /* Master Button Colors - Vierla Design System */\n    --master-button-primary-bg: var(--master-action-primary);  /* Terracotta for primary actions */\n    --master-button-primary-text: var(--mantle-50);            /* White text on terracotta */\n    --master-button-primary-hover: var(--master-action-hover); /* Darker terracotta on hover */\n    --master-button-secondary-bg: transparent;\n    --master-button-secondary-text: var(--master-action-primary);\n    --master-button-secondary-border: var(--master-action-primary);\n    --master-button-secondary-hover: var(--master-action-primary);\n\n    /* Master Card & Container Colors */\n    --master-card-background: rgba(246, 247, 246, 0.5); /* Semi-transparent cream */\n    --master-card-border: var(--mantle-200);\n    --master-card-glow: var(--master-brand-accent);      /* Gold glow for hover effects */\n\n    /* Master Header/Navbar */\n    --master-header-background: rgba(139, 154, 140, 0.1); /* Semi-transparent sage */\n    --master-header-backdrop-blur: 50px; /* 50% blur as requested */\n    --master-header-border: transparent;\n    --master-header-logo-bg: var(--mantle-900);\n    --master-header-logo-border: var(--mantle-700);\n    --master-header-logo-icon: var(--mantle-50);\n    --master-header-brand-text: var(--mantle-50);\n    --master-header-nav-text: var(--mantle-50);\n    --master-header-nav-text-muted: var(--mantle-200);\n    --master-header-nav-active-bg: rgba(255, 255, 255, 0.3);\n    --master-header-nav-active-glow: transparent;\n    --master-header-nav-hover-bg: rgba(24, 27, 25, 0.2);\n\n    /*\n    ========================================\n    | 📄 PAGE-SPECIFIC COMPONENT CONTROLS\n    ========================================\n    */\n\n    /* ----- 🏠 HOME PAGE ----- */\n    --home-hero-title: var(--master-text-on-dark);\n    --home-hero-subtitle: var(--mantle-100);\n    --home-hero-cta-bg: var(--master-button-primary-bg);\n    --home-hero-cta-text: var(--master-button-primary-text);\n    --home-card-background: var(--master-card-background);\n    --home-card-border: var(--master-card-border);\n    --home-card-glow: var(--master-card-glow);\n\n    /* Home Page Shiny Button Controls */\n    --home-shiny-button-bg: var(--master-button-primary-bg);\n    --home-shiny-button-text: var(--master-button-primary-text);\n    --home-shiny-button-shimmer: var(--mantle-200);\n    --home-shiny-button-hero-bg: var(--mantle-800);\n    --home-shiny-button-hero-text: var(--mantle-50);\n    --home-shiny-button-hero-shimmer: var(--mantle-200);\n    --home-shiny-button-cta-bg: var(--mantle-800);\n    --home-shiny-button-cta-text: var(--mantle-50);\n    --home-shiny-button-cta-shimmer: var(--mantle-200);\n\n    /* Home Page Text Effects */\n    --home-text-shimmer-base: var(--master-text-on-dark);\n    --home-text-shimmer-highlight: var(--mantle-200);\n    --home-word-pullup-color: var(--mantle-100);\n    --home-marquee-text: var(--mantle-100);\n    --home-marquee-bg: transparent;\n\n    /* Home Page Golden Cards */\n    --home-golden-card-bg: var(--master-card-background);\n    --home-golden-card-border: var(--master-card-border);\n    --home-golden-card-glow: var(--master-card-glow);\n\n    /* ----- 🎯 FEATURES PAGE ----- */\n    --features-hero-title: var(--master-text-on-dark);\n    --features-hero-subtitle: var(--mantle-100);\n    --features-bento-card-bg: var(--master-card-background);\n    --features-bento-card-border: var(--master-card-border);\n    --features-bento-card-title: var(--master-text-primary);\n    --features-bento-card-text: var(--master-text-secondary);\n    --features-bento-icon-color: var(--master-brand-primary);\n    --features-bento-grid-gap: 1.5rem;\n    --features-cta-button-bg: var(--master-button-primary-bg);\n    --features-cta-button-text: var(--master-button-primary-text);\n    --features-cta-button-hover: var(--mantle-700);\n\n    /* Features Page Component Controls */\n    --features-shiny-button-bg: var(--master-button-primary-bg);\n    --features-shiny-button-text: var(--master-button-primary-text);\n    --features-shiny-button-shimmer: var(--master-brand-accent);\n    --features-golden-card-bg: var(--master-card-background);\n    --features-golden-card-border: var(--master-card-border);\n    --features-golden-card-glow: var(--master-card-glow);\n\n    /* ----- 💰 PRICING PAGE ----- */\n    --pricing-hero-title: var(--master-text-on-dark);\n    --pricing-hero-subtitle: var(--mantle-100);\n    --pricing-card-background: var(--master-card-background);\n    --pricing-card-border: var(--master-card-border);\n    --pricing-card-glow: var(--master-card-glow);\n    --pricing-card-title: var(--master-text-primary);\n    --pricing-card-price: var(--master-brand-primary);\n    --pricing-card-text: var(--master-text-secondary);\n    --pricing-popular-bg: rgba(121, 136, 122, 0.1);\n    --pricing-popular-border: var(--master-brand-primary);\n    --pricing-shiny-button-bg: var(--master-button-primary-bg);\n    --pricing-shiny-button-text: var(--master-button-primary-text);\n    --pricing-shiny-button-shimmer: var(--mantle-200);\n    --pricing-badge-bg: var(--master-brand-primary);\n    --pricing-badge-text: var(--master-text-on-dark);\n\n    /* ----- 👥 ABOUT PAGE ----- */\n    --about-hero-title: var(--master-text-on-dark);\n    --about-hero-subtitle: var(--mantle-100);\n    --about-card-background: var(--master-card-background);\n    --about-card-border: var(--master-card-border);\n    --about-card-glow: var(--master-card-glow);\n    --about-team-card-bg: transparent;\n    --about-team-card-border: transparent;\n    --about-team-name: var(--master-text-primary);\n    --about-team-role: var(--master-brand-primary);\n    --about-team-bio: var(--master-text-secondary);\n    --about-shiny-button-bg: var(--master-button-primary-bg);\n    --about-shiny-button-text: var(--master-button-primary-text);\n    --about-shiny-button-shimmer: var(--mantle-200);\n    --about-mission-bg: transparent;\n\n    /* ----- 📞 CONTACT PAGE ----- */\n    --contact-hero-title: var(--master-text-on-dark);\n    --contact-hero-subtitle: var(--mantle-100);\n    --contact-form-bg: var(--master-card-background);\n    --contact-form-border: var(--master-card-border);\n    --contact-input-bg: rgba(255, 255, 255, 0.7);\n    --contact-input-border: var(--mantle-200);\n    --contact-input-text: var(--master-text-primary);\n    --contact-label-text: var(--master-text-secondary);\n    --contact-input-focus: var(--master-brand-primary);\n    --contact-shiny-button-bg: var(--master-button-primary-bg);\n    --contact-shiny-button-text: var(--master-button-primary-text);\n    --contact-shiny-button-shimmer: var(--mantle-200);\n\n    /* ----- 🧩 GLOBAL COMPONENTS ----- */\n    --global-footer-bg: var(--mantle-200);\n    --global-footer-border: var(--mantle-300);\n    --global-footer-text: var(--master-text-secondary);\n    --global-footer-link: var(--master-text-primary);\n    --global-footer-link-hover: var(--master-brand-primary);\n    --global-cookie-bg: var(--mantle-950);\n    --global-cookie-border: var(--mantle-800);\n    --global-cookie-text: var(--mantle-100);\n    --global-cookie-button-bg: var(--mantle-50);\n    --global-cookie-button-text: var(--mantle-950);\n\n    /* Shiny Button Global Controls */\n    --global-shiny-button-primary-bg: var(--master-button-primary-bg);\n    --global-shiny-button-primary-text: var(--master-button-primary-text);\n    --global-shiny-button-primary-shimmer: var(--master-brand-accent);\n    --global-shiny-button-secondary-bg: var(--master-button-secondary-bg);\n    --global-shiny-button-secondary-text: var(--master-button-secondary-text);\n    --global-shiny-button-secondary-shimmer: var(--master-brand-primary);\n\n    /* Shimmer Button Global Controls */\n    --global-shimmer-button-primary-bg: var(--master-button-primary-bg);\n    --global-shimmer-button-primary-shimmer: var(--master-brand-accent);\n    --global-shimmer-button-secondary-bg: var(--master-button-secondary-bg);\n    --global-shimmer-button-secondary-shimmer: var(--master-brand-accent);\n\n    /* Text Shimmer Global Controls */\n    --global-text-shimmer-base: var(--master-text-on-dark);\n    --global-text-shimmer-highlight: var(--master-brand-accent);\n\n    /* Golden Glowing Card Global Controls */\n    --global-golden-card-bg: var(--master-card-background);\n    --global-golden-card-border: var(--master-card-border);\n    --global-golden-card-glow: var(--master-card-glow);\n\n    /*\n    ========================================\n    | Shadcn UI Theme Variables\n    ========================================\n    */\n    --background: transparent;\n    --foreground: 20 15% 9%; /* Corresponds to mantle-950 */\n    --card: transparent;\n    --card-foreground: 20 15% 9%;\n    --popover: transparent;\n    --popover-foreground: 20 15% 9%;\n    --primary: 138 9% 53%; /* Corresponds to mantle-400 */\n    --primary-foreground: 120 20% 97%; /* Corresponds to mantle-50 */\n    --secondary: 138 9% 60%; /* Corresponds to mantle-300 */\n    --secondary-foreground: 20 15% 9%;\n    --muted: transparent;\n    --muted-foreground: 20 15% 40%; /* Corresponds to mantle-600 */\n    --accent: transparent;\n    --accent-foreground: 20 15% 9%;\n    --destructive: 0 84% 60%;\n    --destructive-foreground: 0 0% 98%;\n    --border: 120 11% 88%; /* Corresponds to mantle-200 */\n    --input: 120 11% 88%;\n    --ring: 138 9% 53%;\n    --radius: 0.5rem;\n}\n\n  .dark {\n    /* Dark mode can be refined here if needed */\n    --foreground: 120 20% 97%;\n    --card-foreground: 120 20% 97%;\n    --popover-foreground: 120 20% 97%;\n    --primary-foreground: 120 20% 97%;\n    --secondary-foreground: 120 20% 97%;\n    --muted-foreground: 120 11% 88%;\n    --accent-foreground: 120 20% 97%;\n    --destructive-foreground: 0 0% 98%;\n    --border: 20 10% 22%; /* Corresponds to mantle-800 */\n    --input: 20 10% 22%;\n    --ring: 138 9% 53%;\n  }\n\n  /*\n  ========================================\n  | BASE ELEMENT STYLES\n  ========================================\n  */\n  * {\n  border-color: hsl(var(--border));\n}\n  body {\n  --tw-bg-opacity: 1;\n  background-color: rgb(139 154 140 / var(--tw-bg-opacity, 1));\n  font-family: var(--font-inter);\n  color: hsl(var(--foreground));\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n  h1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-playfair-display);\n}\n.container {\n  width: 100%;\n}\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.pointer-events-auto {\n  pointer-events: auto;\n}\n.visible {\n  visibility: visible;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.-inset-0\\.5 {\n  inset: -0.125rem;\n}\n.-inset-\\[10px\\] {\n  inset: -10px;\n}\n.-inset-full {\n  inset: -100%;\n}\n.-inset-px {\n  inset: -1px;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-px {\n  inset: 1px;\n}\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\n.-right-20 {\n  right: -5rem;\n}\n.-top-20 {\n  top: -5rem;\n}\n.bottom-0 {\n  bottom: 0px;\n}\n.bottom-1 {\n  bottom: 0.25rem;\n}\n.left-0 {\n  left: 0px;\n}\n.right-0 {\n  right: 0px;\n}\n.right-0\\.5 {\n  right: 0.125rem;\n}\n.right-4 {\n  right: 1rem;\n}\n.top-0 {\n  top: 0px;\n}\n.top-1 {\n  top: 0.25rem;\n}\n.top-4 {\n  top: 1rem;\n}\n.-z-10 {\n  z-index: -10;\n}\n.-z-20 {\n  z-index: -20;\n}\n.-z-30 {\n  z-index: -30;\n}\n.z-0 {\n  z-index: 0;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-50 {\n  z-index: 50;\n}\n.col-span-3 {\n  grid-column: span 3 / span 3;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.-mr-1 {\n  margin-right: -0.25rem;\n}\n.-mt-1 {\n  margin-top: -0.25rem;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.mb-12 {\n  margin-bottom: 3rem;\n}\n.mb-16 {\n  margin-bottom: 4rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.mb-8 {\n  margin-bottom: 2rem;\n}\n.ml-1 {\n  margin-left: 0.25rem;\n}\n.ml-2 {\n  margin-left: 0.5rem;\n}\n.mr-2 {\n  margin-right: 0.5rem;\n}\n.mr-3 {\n  margin-right: 0.75rem;\n}\n.mr-4 {\n  margin-right: 1rem;\n}\n.mr-8 {\n  margin-right: 2rem;\n}\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\n.mt-1 {\n  margin-top: 0.25rem;\n}\n.mt-1\\.5 {\n  margin-top: 0.375rem;\n}\n.mt-10 {\n  margin-top: 2.5rem;\n}\n.mt-12 {\n  margin-top: 3rem;\n}\n.mt-16 {\n  margin-top: 4rem;\n}\n.mt-2 {\n  margin-top: 0.5rem;\n}\n.mt-4 {\n  margin-top: 1rem;\n}\n.mt-6 {\n  margin-top: 1.5rem;\n}\n.mt-8 {\n  margin-top: 2rem;\n}\n.mt-auto {\n  margin-top: auto;\n}\n.\\!block {\n  display: block !important;\n}\n.block {\n  display: block;\n}\n.inline-block {\n  display: inline-block;\n}\n.inline {\n  display: inline;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.grid {\n  display: grid;\n}\n.\\!hidden {\n  display: none !important;\n}\n.hidden {\n  display: none;\n}\n.size-full {\n  width: 100%;\n  height: 100%;\n}\n.h-1\\.5 {\n  height: 0.375rem;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-11 {\n  height: 2.75rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-20 {\n  height: 5rem;\n}\n.h-24 {\n  height: 6rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-40 {\n  height: 10rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-6 {\n  height: 1.5rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-9 {\n  height: 2.25rem;\n}\n.h-\\[100\\%\\] {\n  height: 100%;\n}\n.h-\\[100cqh\\] {\n  height: 100cqh;\n}\n.h-\\[100vh\\] {\n  height: 100vh;\n}\n.h-full {\n  height: 100%;\n}\n.h-px {\n  height: 1px;\n}\n.max-h-48 {\n  max-height: 12rem;\n}\n.max-h-60 {\n  max-height: 15rem;\n}\n.min-h-\\[800px\\] {\n  min-height: 800px;\n}\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-1\\.5 {\n  width: 0.375rem;\n}\n.w-1\\/4 {\n  width: 25%;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-20 {\n  width: 5rem;\n}\n.w-24 {\n  width: 6rem;\n}\n.w-3\\/4 {\n  width: 75%;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-40 {\n  width: 10rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-6 {\n  width: 1.5rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-\\[100\\%\\] {\n  width: 100%;\n}\n.w-auto {\n  width: auto;\n}\n.w-full {\n  width: 100%;\n}\n.min-w-\\[280px\\] {\n  min-width: 280px;\n}\n.min-w-\\[300px\\] {\n  min-width: 300px;\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-3xl {\n  max-width: 48rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-6xl {\n  max-width: 72rem;\n}\n.max-w-7xl {\n  max-width: 80rem;\n}\n.max-w-\\[100vw\\] {\n  max-width: 100vw;\n}\n.max-w-\\[180px\\] {\n  max-width: 180px;\n}\n.max-w-\\[400px\\] {\n  max-width: 400px;\n}\n.max-w-\\[42rem\\] {\n  max-width: 42rem;\n}\n.max-w-\\[58rem\\] {\n  max-width: 58rem;\n}\n.max-w-lg {\n  max-width: 32rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-none {\n  max-width: none;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.flex-grow {\n  flex-grow: 1;\n}\n.grow {\n  flex-grow: 1;\n}\n.origin-left {\n  transform-origin: left;\n}\n.translate-y-10 {\n  --tw-translate-y: 2.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform-gpu {\n  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes shimmer-slide {\n\n  to {\n    transform: translate(calc(100cqw - 100%), 0);\n  }\n}\n.animate-shimmer-slide {\n  animation: shimmer-slide var(--speed) ease-in-out infinite alternate;\n}\n@keyframes spin-around {\n\n  0% {\n    transform: translateZ(0) rotate(0);\n  }\n\n  15%, 35% {\n    transform: translateZ(0) rotate(90deg);\n  }\n\n  65%, 85% {\n    transform: translateZ(0) rotate(270deg);\n  }\n\n  100% {\n    transform: translateZ(0) rotate(360deg);\n  }\n}\n.animate-spin-around {\n  animation: spin-around calc(var(--speed) * 2) infinite linear;\n}\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.select-none {\n  user-select: none;\n}\n.resize-none {\n  resize: none;\n}\n.auto-rows-\\[20rem\\] {\n  grid-auto-rows: 20rem;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.flex-nowrap {\n  flex-wrap: nowrap;\n}\n.place-items-center {\n  place-items: center;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-center {\n  align-items: center;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.gap-1 {\n  gap: 0.25rem;\n}\n.gap-12 {\n  gap: 3rem;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-3 {\n  gap: 0.75rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-6 {\n  gap: 1.5rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-visible {\n  overflow: visible;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.text-nowrap {\n  text-wrap: nowrap;\n}\n.rounded-2xl {\n  border-radius: 1rem;\n}\n.rounded-3xl {\n  border-radius: 1.5rem;\n}\n.rounded-\\[1\\.25rem\\] {\n  border-radius: 1.25rem;\n}\n.rounded-\\[22px\\] {\n  border-radius: 22px;\n}\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n.border {\n  border-width: 1px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-\\[0\\.75px\\] {\n  border-width: 0.75px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-l {\n  border-left-width: 1px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-blue-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.border-border {\n  border-color: hsl(var(--border));\n}\n.border-border\\/25 {\n  border-color: hsl(var(--border) / 0.25);\n}\n.border-border\\/30 {\n  border-color: hsl(var(--border) / 0.3);\n}\n.border-brand-primary\\/20 {\n  border-color: rgb(121 136 122 / 0.2);\n}\n.border-brand-primary\\/30 {\n  border-color: rgb(121 136 122 / 0.3);\n}\n.border-green-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-primary {\n  border-color: hsl(var(--primary));\n}\n.border-red-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\n.border-red-500\\/30 {\n  border-color: rgb(239 68 68 / 0.3);\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-white\\/10 {\n  border-color: rgb(255 255 255 / 0.1);\n}\n.border-white\\/20 {\n  border-color: rgb(255 255 255 / 0.2);\n}\n.border-white\\/25 {\n  border-color: rgb(255 255 255 / 0.25);\n}\n.border-white\\/30 {\n  border-color: rgb(255 255 255 / 0.3);\n}\n.border-white\\/40 {\n  border-color: rgb(255 255 255 / 0.4);\n}\n.border-yellow-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));\n}\n.bg-\\[\\#B8956A\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(184 149 106 / var(--tw-bg-opacity, 1));\n}\n.bg-\\[var\\(--aurora-bg\\)\\] {\n  background-color: var(--aurora-bg);\n}\n.bg-background {\n  background-color: hsl(var(--background));\n}\n.bg-black\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\n.bg-blue-500\\/90 {\n  background-color: rgb(59 130 246 / 0.9);\n}\n.bg-brand-neutral-charcoal\\/80 {\n  background-color: rgb(46 51 47 / 0.8);\n}\n.bg-brand-primary\\/20 {\n  background-color: rgb(121 136 122 / 0.2);\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-card\\/20 {\n  background-color: hsl(var(--card) / 0.2);\n}\n.bg-card\\/50 {\n  background-color: hsl(var(--card) / 0.5);\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-green-500\\/90 {\n  background-color: rgb(34 197 94 / 0.9);\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-primary\\/10 {\n  background-color: hsl(var(--primary) / 0.1);\n}\n.bg-primary\\/20 {\n  background-color: hsl(var(--primary) / 0.2);\n}\n.bg-red-500\\/20 {\n  background-color: rgb(239 68 68 / 0.2);\n}\n.bg-red-500\\/90 {\n  background-color: rgb(239 68 68 / 0.9);\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/15 {\n  background-color: rgb(255 255 255 / 0.15);\n}\n.bg-white\\/20 {\n  background-color: rgb(255 255 255 / 0.2);\n}\n.bg-white\\/5 {\n  background-color: rgb(255 255 255 / 0.05);\n}\n.bg-yellow-500\\/90 {\n  background-color: rgb(234 179 8 / 0.9);\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.from-brand-primary\\/20 {\n  --tw-gradient-from: rgb(121 136 122 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(121 136 122 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary\\/20 {\n  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-transparent {\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-white\\/20 {\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-brand-primary\\/10 {\n  --tw-gradient-to: rgb(121 136 122 / 0.1) var(--tw-gradient-to-position);\n}\n.to-primary\\/5 {\n  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);\n}\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.bg-\\[length\\:250\\%_100\\%\\2c auto\\] {\n  background-size: 250% 100%,auto;\n}\n.bg-clip-text {\n  background-clip: text;\n}\n.p-12 {\n  padding: 3rem;\n}\n.p-2 {\n  padding: 0.5rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-8 {\n  padding: 2rem;\n}\n.p-px {\n  padding: 1px;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-20 {\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-4 {\n  padding-bottom: 1rem;\n}\n.pb-8 {\n  padding-bottom: 2rem;\n}\n.pl-10 {\n  padding-left: 2.5rem;\n}\n.pl-3 {\n  padding-left: 0.75rem;\n}\n.pr-4 {\n  padding-right: 1rem;\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.pt-16 {\n  padding-top: 4rem;\n}\n.pt-32 {\n  padding-top: 8rem;\n}\n.pt-4 {\n  padding-top: 1rem;\n}\n.pt-6 {\n  padding-top: 1.5rem;\n}\n.pt-8 {\n  padding-top: 2rem;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-6xl {\n  font-size: 3.75rem;\n  line-height: 1;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-black {\n  font-weight: 900;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-light {\n  font-weight: 300;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.leading-\\[1\\.1\\] {\n  line-height: 1.1;\n}\n.leading-\\[5rem\\] {\n  line-height: 5rem;\n}\n.leading-none {\n  line-height: 1;\n}\n.leading-normal {\n  line-height: 1.5;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.tracking-\\[-0\\.02em\\] {\n  letter-spacing: -0.02em;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.tracking-wide {\n  letter-spacing: 0.025em;\n}\n.text-\\[\\#2D2A26\\] {\n  --tw-text-opacity: 1;\n  color: rgb(45 42 38 / var(--tw-text-opacity, 1));\n}\n.text-\\[\\#B8956A\\] {\n  --tw-text-opacity: 1;\n  color: rgb(184 149 106 / var(--tw-text-opacity, 1));\n}\n.text-brand-neutral-charcoal {\n  --tw-text-opacity: 1;\n  color: rgb(46 51 47 / var(--tw-text-opacity, 1));\n}\n.text-brand-neutral-gray {\n  --tw-text-opacity: 1;\n  color: rgb(139 154 140 / var(--tw-text-opacity, 1));\n}\n.text-brand-neutral-gray\\/60 {\n  color: rgb(139 154 140 / 0.6);\n}\n.text-brand-neutral-gray\\/70 {\n  color: rgb(139 154 140 / 0.7);\n}\n.text-brand-neutral-gray\\/80 {\n  color: rgb(139 154 140 / 0.8);\n}\n.text-brand-neutral-gray\\/90 {\n  color: rgb(139 154 140 / 0.9);\n}\n.text-brand-primary {\n  --tw-text-opacity: 1;\n  color: rgb(121 136 122 / var(--tw-text-opacity, 1));\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-current {\n  color: currentColor;\n}\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-red-200 {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n.text-red-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-slate-950 {\n  --tw-text-opacity: 1;\n  color: rgb(2 6 23 / var(--tw-text-opacity, 1));\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-white\\/70 {\n  color: rgb(255 255 255 / 0.7);\n}\n.text-yellow-500 {\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\n.underline {\n  text-decoration-line: underline;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.placeholder-white\\/50::placeholder {\n  color: rgb(255 255 255 / 0.5);\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-100 {\n  opacity: 1;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-60 {\n  opacity: 0.6;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.opacity-75 {\n  opacity: 0.75;\n}\n.opacity-90 {\n  opacity: 0.9;\n}\n.shadow-2xl {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[0_1000px_0_0_var\\(--color-neutral-white\\)_inset\\] {\n  --tw-shadow: 0 1000px 0 0 var(--color-neutral-white) inset;\n  --tw-shadow-colored: inset 0 1000px 0 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[inset_0_-8px_10px_\\#ffffff1f\\] {\n  --tw-shadow: inset 0 -8px 10px #ffffff1f;\n  --tw-shadow-colored: inset 0 -8px 10px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline {\n  outline-style: solid;\n}\n.ring {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-4 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-\\[\\#B8956A\\]\\/20 {\n  --tw-ring-color: rgb(184 149 106 / 0.2);\n}\n.ring-white\\/30 {\n  --tw-ring-color: rgb(255 255 255 / 0.3);\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-3xl {\n  --tw-blur: blur(64px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[10px\\] {\n  --tw-blur: blur(10px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[2px\\] {\n  --tw-blur: blur(2px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[var\\(--blur\\)\\] {\n  --tw-blur: blur(var(--blur));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-sm {\n  --tw-blur: blur(4px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-lg {\n  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-md {\n  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-sm {\n  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-xl {\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.duration-500 {\n  transition-duration: 500ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.will-change-transform {\n  will-change: transform;\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.slide-in-from-right-full {\n  --tw-enter-translate-x: 100%;\n}\n.duration-200 {\n  animation-duration: 200ms;\n}\n.duration-300 {\n  animation-duration: 300ms;\n}\n.duration-500 {\n  animation-duration: 500ms;\n}\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.running {\n  animation-play-state: running;\n}\n.\\[--aurora\\:repeating-linear-gradient\\(100deg\\2c var\\(--aurora-flow-1\\)_10\\%\\2c var\\(--aurora-flow-2\\)_15\\%\\2c var\\(--aurora-flow-3\\)_20\\%\\2c var\\(--aurora-flow-4\\)_25\\%\\2c var\\(--aurora-flow-5\\)_30\\%\\)\\] {\n  --aurora: repeating-linear-gradient(100deg,var(--aurora-flow-1) 10%,var(--aurora-flow-2) 15%,var(--aurora-flow-3) 20%,var(--aurora-flow-4) 25%,var(--aurora-flow-5) 30%);\n}\n.\\[--base-color\\:var\\(--global-text-shimmer-base\\)\\] {\n  --base-color: var(--global-text-shimmer-base);\n}\n.\\[--base-gradient-color\\:var\\(--global-text-shimmer-highlight\\)\\] {\n  --base-gradient-color: var(--global-text-shimmer-highlight);\n}\n.\\[--bg\\:linear-gradient\\(90deg\\2c \\#0000_calc\\(50\\%-var\\(--spread\\)\\)\\2c var\\(--base-gradient-color\\)\\2c \\#0000_calc\\(50\\%\\+var\\(--spread\\)\\)\\)\\] {\n  --bg: linear-gradient(90deg,#0000 calc(50% - var(--spread)),var(--base-gradient-color),#0000 calc(50% + var(--spread)));\n}\n.\\[--dark-gradient\\:repeating-linear-gradient\\(100deg\\2c var\\(--aurora-stripe-dark\\)_0\\%\\2c var\\(--aurora-stripe-dark\\)_7\\%\\2c transparent_10\\%\\2c transparent_12\\%\\2c var\\(--aurora-stripe-dark\\)_16\\%\\)\\] {\n  --dark-gradient: repeating-linear-gradient(100deg,var(--aurora-stripe-dark) 0%,var(--aurora-stripe-dark) 7%,transparent 10%,transparent 12%,var(--aurora-stripe-dark) 16%);\n}\n.\\[--white-gradient\\:repeating-linear-gradient\\(100deg\\2c var\\(--aurora-stripe-light\\)_0\\%\\2c var\\(--aurora-stripe-light\\)_7\\%\\2c transparent_10\\%\\2c transparent_12\\%\\2c var\\(--aurora-stripe-light\\)_16\\%\\)\\] {\n  --white-gradient: repeating-linear-gradient(100deg,var(--aurora-stripe-light) 0%,var(--aurora-stripe-light) 7%,transparent 10%,transparent 12%,var(--aurora-stripe-light) 16%);\n}\n.\\[aspect-ratio\\:1\\] {\n  aspect-ratio: 1;\n}\n.\\[background-image\\:var\\(--white-gradient\\)\\2c var\\(--aurora\\)\\] {\n  background-image: var(--white-gradient),var(--aurora);\n}\n.\\[background-position\\:50\\%_50\\%\\2c 50\\%_50\\%\\] {\n  background-position: 50% 50%,50% 50%;\n}\n.\\[background-repeat\\:no-repeat\\2c padding-box\\] {\n  background-repeat: no-repeat,padding-box;\n}\n.\\[background-size\\:300\\%\\2c _200\\%\\] {\n  background-size: 300%, 200%;\n}\n.\\[background\\:conic-gradient\\(from_calc\\(270deg-\\(var\\(--spread\\)\\*0\\.5\\)\\)\\2c transparent_0\\2c var\\(--shimmer-color\\)_var\\(--spread\\)\\2c transparent_var\\(--spread\\)\\)\\] {\n  background: conic-gradient(from calc(270deg - (var(--spread) * 0.5)),transparent 0,var(--shimmer-color) var(--spread),transparent var(--spread));\n}\n.\\[background\\:var\\(--bg\\)\\] {\n  background: var(--bg);\n}\n.\\[border-radius\\:0\\] {\n  border-radius: 0;\n}\n.\\[border-radius\\:var\\(--radius\\)\\] {\n  border-radius: var(--radius);\n}\n.\\[container-type\\:size\\] {\n  container-type: size;\n}\n.\\[inset\\:var\\(--cut\\)\\] {\n  inset: var(--cut);\n}\n.\\[mask-image\\:radial-gradient\\(ellipse_at_100\\%_0\\%\\2c black_10\\%\\2c transparent_70\\%\\)\\] {\n  mask-image: radial-gradient(ellipse at 100% 0%,black 10%,transparent 70%);\n}\n.\\[mask\\:linear-gradient\\(var\\(--color-neutral-charcoal\\)\\2c _transparent_50\\%\\)\\] {\n  mask: linear-gradient(var(--color-neutral-charcoal), transparent 50%);\n}\n.\\[mask\\:none\\] {\n  mask: none;\n}\n.\\[translate\\:0_0\\] {\n  translate: 0 0;\n}\n\n/*\n================================================================================\n| VIERLA DESIGN SYSTEM - GLOBAL STYLES (REDESIGNED)\n|\n| This file contains the complete design system for the Vierla application.\n| It's organized into sections for easy maintenance and customization:\n|\n| 1. MASTER CONTROL PANEL - Core brand colors and theme variables\n| 2. PAGE-SPECIFIC CONTROLS - Individual page customization\n| 3. GLOBAL COMPONENTS - Shared component styling\n| 4. SHADCN UI THEME - UI library integration\n| 5. BASE ELEMENT STYLES - HTML element defaults\n| 6. PAGE & COMPONENT OVERRIDES - Specific styling rules\n| 7. UTILITIES & ANIMATIONS - Helper classes and keyframes\n|\n| Each component can be individually customized per page while maintaining\n| consistency through the master control variables.\n================================================================================\n*/\n\n/*\n========================================\n| PAGE & COMPONENT-SPECIFIC CLASS OVERRIDES\n========================================\n*/\n\n/* ----- Home Page ----- */\n.page-home .shiny-button {\n  background-color: var(--home-shiny-button-bg);\n  color: var(--home-shiny-button-text);\n  --shimmer-color: var(--home-shiny-button-shimmer);\n}\n\n/* Home Page Specific Shiny Button Variants */\n.page-home .shiny-button.hero-cta {\n  background-color: var(--home-shiny-button-hero-bg);\n  color: var(--home-shiny-button-hero-text);\n  --shimmer-color: var(--home-shiny-button-hero-shimmer);\n}\n\n.page-home .shiny-button.section-cta {\n  background-color: var(--home-shiny-button-cta-bg);\n  color: var(--home-shiny-button-cta-text);\n  --shimmer-color: var(--home-shiny-button-cta-shimmer);\n}\n\n.page-home .golden-glowing-card {\n  background: var(--home-golden-card-bg);\n  border-color: var(--home-golden-card-border);\n  --glow-color: var(--home-golden-card-glow);\n}\n\n.page-home .text-shimmer {\n  --base-color: var(--home-text-shimmer-base);\n  --base-gradient-color: var(--home-text-shimmer-highlight);\n}\n\n.page-home .word-pull-up {\n  color: var(--home-word-pullup-color);\n}\n\n.page-home .marquee-animation {\n  color: var(--home-marquee-text);\n  background: var(--home-marquee-bg);\n}\n\n/* ----- Navigation Styles ----- */\n.nav-link {\n  color: var(--master-header-nav-text);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n}\n\n.nav-link:hover {\n  background: var(--master-header-nav-hover-bg);\n  color: var(--master-header-nav-text);\n}\n\n.nav-link-active {\n  background: var(--master-header-nav-active-bg);\n  color: var(--master-header-nav-text);\n  box-shadow: var(--master-header-nav-active-glow);\n}\n\n/*\n========================================\n| UTILITIES & ANIMATIONS\n========================================\n*/\n\n@keyframes aurora {\n  from {\n    background-position: 50% 50%, 50% 50%;\n  }\n  to {\n    background-position: 350% 50%, 350% 50%;\n  }\n}\n\n@keyframes beam {\n  0% {\n    transform: translateY(-100%);\n  }\n  100% {\n    transform: translateY(100%);\n  }\n}\n\n/*\n========================================\n| FIX FOR BACKGROUND ISSUE\n| This section is intentionally left blank. The problematic\n| `!important` override has been removed.\n========================================\n*/\n\n/*\n========================================\n| MOBILE & SAFARI SPECIFIC FIXES\n========================================\n*/\n@media screen and (orientation: landscape) {\n  html, body {\n    min-height: 100lvh !important;\n  }\n}\n\n@supports (padding: max(0px)) {\n  html {\n    padding-top: max(var(--safe-area-inset-top), 0px);\n    padding-left: max(var(--safe-area-inset-left), 0px);\n    padding-right: max(var(--safe-area-inset-right), 0px);\n    padding-bottom: max(var(--safe-area-inset-bottom), 0px);\n  }\n}\n\n@supports (-webkit-touch-callout: none) {\n  html, body {\n    height: -webkit-fill-available;\n  }\n}\n\n.after\\:dark::after {\n    content: var(--tw-content);\n    /* Dark mode can be refined here if needed */\n    --foreground: 120 20% 97%;\n    --card-foreground: 120 20% 97%;\n    --popover-foreground: 120 20% 97%;\n    --primary-foreground: 120 20% 97%;\n    --secondary-foreground: 120 20% 97%;\n    --muted-foreground: 120 11% 88%;\n    --accent-foreground: 120 20% 97%;\n    --destructive-foreground: 0 0% 98%;\n    --border: 20 10% 22%; /* Corresponds to mantle-800 */\n    --input: 20 10% 22%;\n    --ring: 138 9% 53%;\n  }\n\n.\\*\\:me-10 > * {\n  margin-inline-end: 2.5rem;\n}\n\n.\\*\\:block > * {\n  display: block;\n}\n\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\n\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\n\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\n\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\n\n.placeholder\\:text-\\[\\#F5F5DC\\]::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(245 245 220 / var(--tw-text-opacity, 1));\n}\n\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\n\n.before\\:absolute::before {\n  content: var(--tw-content);\n  position: absolute;\n}\n\n.before\\:aspect-square::before {\n  content: var(--tw-content);\n  aspect-ratio: 1 / 1;\n}\n\n.before\\:w-\\[200\\%\\]::before {\n  content: var(--tw-content);\n  width: 200%;\n}\n\n.before\\:rotate-\\[-90deg\\]::before {\n  content: var(--tw-content);\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.before\\:bg-\\[conic-gradient\\(from_0deg\\2c transparent_0_340deg\\2c var\\(--color-primary\\)_360deg\\)\\]::before {\n  content: var(--tw-content);\n  background-image: conic-gradient(from 0deg,transparent 0 340deg,var(--color-primary) 360deg);\n}\n\n.before\\:content-\\[\\'\\'\\]::before {\n  --tw-content: '';\n  content: var(--tw-content);\n}\n\n.before\\:\\[inset\\:0_auto_auto_50\\%\\]::before {\n  content: var(--tw-content);\n  inset: 0 auto auto 50%;\n}\n\n.before\\:\\[translate\\:-50\\%_-15\\%\\]::before {\n  content: var(--tw-content);\n  translate: -50% -15%;\n}\n\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\n\n.after\\:inset-0::after {\n  content: var(--tw-content);\n  inset: 0px;\n}\n\n.after\\:inset-\\[calc\\(-1\\*var\\(--glowingeffect-border-width\\)\\)\\]::after {\n  content: var(--tw-content);\n  inset: calc(-1 * var(--glowingeffect-border-width));\n}\n\n@keyframes aurora {\n\n  from {\n    content: var(--tw-content);\n    background-position: 50% 50%, 50% 50%;\n  }\n\n  to {\n    content: var(--tw-content);\n    background-position: 350% 50%, 350% 50%;\n  }\n}\n\n.after\\:animate-aurora::after {\n  content: var(--tw-content);\n  animation: aurora 60s linear infinite;\n}\n\n.after\\:rounded-\\[inherit\\]::after {\n  content: var(--tw-content);\n  border-radius: inherit;\n}\n\n.after\\:opacity-\\[var\\(--active\\)\\]::after {\n  content: var(--tw-content);\n  opacity: var(--active);\n}\n\n.after\\:mix-blend-difference::after {\n  content: var(--tw-content);\n  mix-blend-mode: difference;\n}\n\n.after\\:transition-opacity::after {\n  content: var(--tw-content);\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n\n.after\\:duration-300::after {\n  content: var(--tw-content);\n  transition-duration: 300ms;\n}\n\n.after\\:content-\\[\\\"\\\"\\]::after {\n  --tw-content: \"\";\n  content: var(--tw-content);\n}\n\n.after\\:duration-300::after {\n  content: var(--tw-content);\n  animation-duration: 300ms;\n}\n\n.after\\:\\[background-attachment\\:fixed\\]::after {\n  content: var(--tw-content);\n  background-attachment: fixed;\n}\n\n.after\\:\\[background-image\\:var\\(--white-gradient\\)\\2c var\\(--aurora\\)\\]::after {\n  content: var(--tw-content);\n  background-image: var(--white-gradient),var(--aurora);\n}\n\n.after\\:\\[background-size\\:200\\%\\2c _100\\%\\]::after {\n  content: var(--tw-content);\n  background-size: 200%, 100%;\n}\n\n.after\\:\\[background\\:var\\(--gradient\\)\\]::after {\n  content: var(--tw-content);\n  background: var(--gradient);\n}\n\n.after\\:\\[border\\:var\\(--glowingeffect-border-width\\)_solid_transparent\\]::after {\n  content: var(--tw-content);\n  border: var(--glowingeffect-border-width) solid transparent;\n}\n\n.after\\:\\[mask-clip\\:padding-box\\2c border-box\\]::after {\n  content: var(--tw-content);\n  mask-clip: padding-box,border-box;\n}\n\n.after\\:\\[mask-composite\\:intersect\\]::after {\n  content: var(--tw-content);\n  mask-composite: intersect;\n}\n\n.after\\:\\[mask-image\\:linear-gradient\\(\\#0000\\2c \\#0000\\)\\2c conic-gradient\\(from_calc\\(\\(var\\(--start\\)-var\\(--spread\\)\\)\\*1deg\\)\\2c \\#00000000_0deg\\2c \\#fff\\2c \\#00000000_calc\\(var\\(--spread\\)\\*2deg\\)\\)\\]::after {\n  content: var(--tw-content);\n  mask-image: linear-gradient(#0000,#0000),conic-gradient(from calc((var(--start) - var(--spread)) * 1deg),#00000000 0deg,#fff,#00000000 calc(var(--spread) * 2deg));\n}\n\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\n\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n\n.hover\\:bg-primary\\/30:hover {\n  background-color: hsl(var(--primary) / 0.3);\n}\n\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\n\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n\n.hover\\:bg-white\\/10:hover {\n  background-color: rgb(255 255 255 / 0.1);\n}\n\n.hover\\:bg-white\\/20:hover {\n  background-color: rgb(255 255 255 / 0.2);\n}\n\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n\n.hover\\:text-brand-primary:hover {\n  --tw-text-opacity: 1;\n  color: rgb(121 136 122 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n\n.hover\\:shadow:hover {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.focus\\:border-transparent:focus {\n  border-color: transparent;\n}\n\n.focus\\:border-white\\/40:focus {\n  border-color: rgb(255 255 255 / 0.4);\n}\n\n.focus\\:bg-white\\/10:focus {\n  background-color: rgb(255 255 255 / 0.1);\n}\n\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-primary:focus {\n  --tw-ring-color: hsl(var(--primary));\n}\n\n.focus\\:ring-red-400\\/30:focus {\n  --tw-ring-color: rgb(248 113 113 / 0.3);\n}\n\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus\\:ring-white\\/30:focus {\n  --tw-ring-color: rgb(255 255 255 / 0.3);\n}\n\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\n\n.active\\:translate-y-px:active {\n  --tw-translate-y: 1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n\n.group:hover .group-hover\\:w-\\[calc\\(100\\%-0\\.5rem\\)\\] {\n  width: calc(100% - 0.5rem);\n}\n\n.group:hover .group-hover\\:-translate-y-10 {\n  --tw-translate-y: -2.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-x-1 {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-y-0 {\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-75 {\n  --tw-scale-x: .75;\n  --tw-scale-y: .75;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:bg-black\\/\\[\\.03\\] {\n  background-color: rgb(0 0 0 / .03);\n}\n\n.group\\/card:hover .group-hover\\/card\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:opacity-0 {\n  opacity: 0;\n}\n\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:shadow-\\[inset_0_-6px_10px_\\#ffffff3f\\] {\n  --tw-shadow: inset 0 -6px 10px #ffffff3f;\n  --tw-shadow-colored: inset 0 -6px 10px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group:active .group-active\\:scale-95 {\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:active .group-active\\:shadow-\\[inset_0_-10px_10px_\\#ffffff3f\\] {\n  --tw-shadow: inset 0 -10px 10px #ffffff3f;\n  --tw-shadow-colored: inset 0 -10px 10px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\n\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\n\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\n\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\n\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\n\n.dark\\:bg-\\[var\\(--aurora-bg-dark\\)\\]:is(.dark *) {\n  background-color: var(--aurora-bg-dark);\n}\n\n.dark\\:bg-black:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:font-light:is(.dark *) {\n  font-weight: 300;\n}\n\n.dark\\:text-black:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:\\[--base-color\\:var\\(--global-text-shimmer-base\\)\\]:is(.dark *) {\n  --base-color: var(--global-text-shimmer-base);\n}\n\n.dark\\:\\[--base-gradient-color\\:var\\(--global-text-shimmer-highlight\\)\\]:is(.dark *) {\n  --base-gradient-color: var(--global-text-shimmer-highlight);\n}\n\n.dark\\:\\[background-image\\:var\\(--dark-gradient\\)\\2c var\\(--aurora\\)\\]:is(.dark *) {\n  background-image: var(--dark-gradient),var(--aurora);\n}\n\n.dark\\:\\[border\\:1px_solid_rgba\\(255\\2c 255\\2c 255\\2c \\.1\\)\\]:is(.dark *) {\n  border: 1px solid rgba(255,255,255,.1);\n}\n\n.dark\\:\\[box-shadow\\:0_-20px_80px_-20px_\\#ffffff1f_inset\\]:is(.dark *) {\n  box-shadow: 0 -20px 80px -20px #ffffff1f inset;\n}\n\n.after\\:dark\\:\\[background-image\\:var\\(--dark-gradient\\)\\2c var\\(--aurora\\)\\]:is(.dark *)::after {\n  content: var(--tw-content);\n  background-image: var(--dark-gradient),var(--aurora);\n}\n\n.dark\\:hover\\:shadow-\\[0_0_20px_hsl\\(var\\(--primary\\)\\/10\\%\\)\\]:hover:is(.dark *) {\n  --tw-shadow: 0 0 20px hsl(var(--primary)/10%);\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group:hover .group-hover\\:dark\\:bg-neutral-800\\/10:is(.dark *) {\n  background-color: rgb(38 38 38 / 0.1);\n}\n\n@media (min-width: 640px) {\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .sm\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .sm\\:leading-8 {\n    line-height: 2rem;\n  }\n}\n\n@media (min-width: 768px) {\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:h-12 {\n    height: 3rem;\n  }\n\n  .md\\:h-5 {\n    height: 1.25rem;\n  }\n\n  .md\\:h-6 {\n    height: 1.5rem;\n  }\n\n  .md\\:h-7 {\n    height: 1.75rem;\n  }\n\n  .md\\:w-12 {\n    width: 3rem;\n  }\n\n  .md\\:w-5 {\n    width: 1.25rem;\n  }\n\n  .md\\:w-6 {\n    width: 1.5rem;\n  }\n\n  .md\\:w-7 {\n    width: 1.75rem;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:items-center {\n    align-items: center;\n  }\n\n  .md\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .md\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .md\\:rounded-\\[1\\.5rem\\] {\n    border-radius: 1.5rem;\n  }\n\n  .md\\:p-12 {\n    padding: 3rem;\n  }\n\n  .md\\:p-3 {\n    padding: 0.75rem;\n  }\n\n  .md\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .md\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .md\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-8xl {\n    font-size: 6rem;\n    line-height: 1;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\n\n@media (min-width: 1024px) {\n\n  .lg\\:order-1 {\n    order: 1;\n  }\n\n  .lg\\:order-2 {\n    order: 2;\n  }\n\n  .lg\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .lg\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .lg\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .lg\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .lg\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .lg\\:row-start-1 {\n    grid-row-start: 1;\n  }\n\n  .lg\\:row-start-2 {\n    grid-row-start: 2;\n  }\n\n  .lg\\:row-start-3 {\n    grid-row-start: 3;\n  }\n\n  .lg\\:row-end-2 {\n    grid-row-end: 2;\n  }\n\n  .lg\\:row-end-3 {\n    grid-row-end: 3;\n  }\n\n  .lg\\:row-end-4 {\n    grid-row-end: 4;\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-rows-3 {\n    grid-template-rows: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:py-24 {\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n}\n\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\n\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;;AAQA;;;;;AAcA;;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAAA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBE;;;;;;;;;;;;;;AAoBA;;;;AAGA;;;;;;;;;AAQA;;;;AAGF;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;;AAGA;;;;;AAGA;;;;AA+BA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;AA8KA;;;;;;;;;;;;AAzJA;;;;;;;;;;AAsBA;EACE;;;;;AAKF;EACE;;;;;;;;AAQF;EACE;;;;;AAKF;;;;;;;;;;;;;;;AAgBA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAIA;;;;AAIA;;;;AAAA;;;;AAIA;;;;AAAA;;;;AAIA;;;;;AAAA;;;;;AAKA;;;;AAAA;;;;AAIA;;;;AAAA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;AAKA;;;;;AAkBA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAMF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;;;;AAIA;;;;;AAKA", "debugId": null}}]}