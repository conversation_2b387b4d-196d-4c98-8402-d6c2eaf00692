import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";

// Helper function to flatten the color palette for CSS variable generation
function flattenColorPalette(colors: any): Record<string, string> {
  const result: Record<string, string> = {};

  function flatten(obj: any, prefix = "") {
    for (const [key, value] of Object.entries(obj)) {
      const newKey = prefix ? `${prefix}-${key}` : key;
      if (typeof value === "object" && value !== null && !Array.isArray(value)) {
        flatten(value, newKey);
      } else {
        result[newKey] = value as string;
      }
    }
  }

  flatten(colors);
  return result;
}

// ========================================
// 🎨 AURORA BACKGROUND MASTER CONTROLS - New Visual Identity
// ========================================
// Light Mode Aurora Colors (WCAG Compliant)
const AURORA_LIGHT_CONTROLS = {
  mainBackground: "#f6f7f6", // Very light sage - matches current text/buttons
  mainBackgroundDark: "#e1e6e1", // Light sage for variation
  stripeLight: "rgba(52, 59, 54, 0.03)", // Very subtle dark sage stripes
  stripeDark: "rgba(46, 51, 47, 0.05)", // Dark charcoal for minimal contrast
  auroraFlow1: "rgba(139, 154, 140, 0.08)", // Medium sage - subtle flow
  auroraFlow2: "rgba(195, 204, 195, 0.12)", // Medium-light sage
  auroraFlow3: "rgba(121, 136, 122, 0.06)", // Medium-dark sage
  auroraFlow4: "rgba(225, 230, 225, 0.15)", // Light sage variation
  auroraFlow5: "rgba(95, 109, 97, 0.04)", // Dark sage - very subtle
};

// Dark Mode Aurora Colors (WCAG Compliant)
const AURORA_DARK_CONTROLS = {
  mainBackground: "#181b19", // Very dark charcoal - matches current palette
  mainBackgroundDark: "#2e332f", // Dark charcoal for variation
  stripeLight: "rgba(246, 247, 246, 0.02)", // Very subtle light stripes
  stripeDark: "rgba(52, 59, 54, 0.15)", // Charcoal sage for contrast
  auroraFlow1: "rgba(95, 109, 97, 0.12)", // Dark sage - visible but subtle
  auroraFlow2: "rgba(74, 87, 75, 0.08)", // Darker sage
  auroraFlow3: "rgba(62, 71, 63, 0.10)", // Very dark sage
  auroraFlow4: "rgba(46, 51, 47, 0.18)", // Dark charcoal variation
  auroraFlow5: "rgba(121, 136, 122, 0.06)", // Medium-dark sage - accent
};

// Aurora controls are now theme-aware through CSS variables

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./hooks/**/*.{js,ts,jsx,tsx,mdx}",
    "./lib/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        // Define 'sans' as the primary body font (Jost)
        sans: ["var(--font-jost)", "Inter", "system-ui", "sans-serif"],
        // Define 'serif' as the secondary heading font (Tai Heritage Pro)
        serif: ["var(--font-tai-heritage)", "Georgia", "serif"],
        // Define 'notable' for H1 headlines (Notable)
        notable: ["var(--font-notable)", "Impact", "Arial Black", "sans-serif"],
        // Define 'tai-heritage' for H2/H3 section titles
        "tai-heritage": ["var(--font-tai-heritage)", "Georgia", "serif"],
        // Define 'farsan' for optional accent text
        farsan: ["var(--font-farsan)", "cursive"],
      },
      spacing: {
        // 8-point grid system
        '1': 'var(--space-1)',   // 8px
        '2': 'var(--space-2)',   // 16px
        '3': 'var(--space-3)',   // 24px
        '4': 'var(--space-4)',   // 32px
        '5': 'var(--space-5)',   // 40px
        '6': 'var(--space-6)',   // 48px
        '7': 'var(--space-7)',   // 56px
        '8': 'var(--space-8)',   // 64px
        '9': 'var(--space-9)',   // 72px
        '10': 'var(--space-10)', // 80px
        '12': 'var(--space-12)', // 96px
        '16': 'var(--space-16)', // 128px
        '20': 'var(--space-20)', // 160px
        '24': 'var(--space-24)', // 192px
      },
      colors: {
        // ========================================
        // 🎨 SEMANTIC COLOR SYSTEM - New Visual Identity
        // ========================================

        // Core Brand Colors (Semantic Names)
        "brand-sage": "#7C9A85",      // Primary brand & accent color
        "brand-gold": "#B8956A",      // Primary action color (CTAs)
        "brand-beige": "#F0E6D9",     // Highlight & accent background

        // Neutral Colors (Semantic Names)
        "neutral-charcoal-dark": "#2C3137",   // Primary background
        "neutral-charcoal-light": "#333333",  // Surface & card backgrounds
        "neutral-off-white": "#F5FAF7",       // Primary text & icons

        // Extended Brand Palette
        "sage": {
          DEFAULT: "#7C9A85", // Muted Sage Green - Primary brand & accent
          50: "#F4F7F5",
          100: "#E8EFEA",
          200: "#D1DFD4",
          300: "#BACFBE",
          400: "#A3BFA8",
          500: "#7C9A85", // Main sage
          600: "#6B8A74",
          700: "#5A7963",
          800: "#496852",
          900: "#3A5441",
        },
        "gold": {
          DEFAULT: "#B8956A", // Muted Gold/Tan - Primary action color
          50: "#FAF7F2",
          100: "#F5EFE5",
          200: "#EBDFCB",
          300: "#E1CFB1",
          400: "#D7BF97",
          500: "#B8956A", // Main gold
          600: "#A6845C",
          700: "#94734E",
          800: "#826240",
          900: "#705132",
        },
        "beige": {
          DEFAULT: "#F0E6D9", // Warm Beige
          50: "#FEFCFA",
          100: "#F0E6D9", // Main beige
          200: "#E0D5C7",
          300: "#D0C5B7",
          400: "#C0B5A7",
          500: "#B0A597",
          600: "#A09587",
          700: "#908577",
          800: "#807567",
          900: "#706557",
        },
        "charcoal": {
          DEFAULT: "#2C3137", // Dark Charcoal
          50: "#F5F6F6",
          100: "#E6E8E9",
          200: "#CDD1D3",
          300: "#B4BABD",
          400: "#9BA3A7",
          500: "#828C91",
          600: "#69757B",
          700: "#505E65",
          800: "#37474F",
          900: "#333333", // Light Charcoal
          950: "#2C3137", // Dark Charcoal
        },

        // Semantic State Colors
        "success": {
          DEFAULT: "#7C9A85", // Use brand sage for success
          50: "#F4F7F5",
          100: "#E8EFEA",
          200: "#D1DFD4",
          300: "#BACFBE",
          400: "#A3BFA8",
          500: "#7C9A85",
          600: "#6B8A74",
          700: "#5A7963",
          800: "#496852",
          900: "#3A5441",
        },
        "warning": {
          DEFAULT: "#B8956A", // Use brand gold for warnings
          50: "#FAF7F2",
          100: "#F5EFE5",
          200: "#EBDFCB",
          300: "#E1CFB1",
          400: "#D7BF97",
          500: "#B8956A",
          600: "#A6845C",
          700: "#94734E",
          800: "#826240",
          900: "#705132",
        },
        "error": {
          DEFAULT: "#D97706", // Terracotta for errors
          50: "#FEF7ED",
          100: "#FDEDD3",
          200: "#FBD5A5",
          300: "#F9BC6D",
          400: "#F59E0B",
          500: "#D97706", // Main error
          600: "#C2410C",
          700: "#9A3412",
          800: "#7C2D12",
          900: "#431407",
        },

        // Legacy Compatibility Colors (Maintained for existing components)
        "mantle": {
          "50": "#F5FAF7",   // Light Off-White
          "100": "#F0E6D9",  // Warm Beige
          "200": "#E0D5C7",
          "300": "#D0C5B7",
          "400": "#7C9A85",  // Sage Green
          "500": "#6B8A74",
          "600": "#5A7963",
          "700": "#496852",
          "800": "#3A5441",
          "900": "#333333",  // Light Charcoal
          "950": "#2C3137", // Dark Charcoal
        },

        // Utility Colors
        white: "#ffffff",
        black: "#000000",
        transparent: "transparent",
        // ========================================
        // 🎨 SHADCN UI INTEGRATION - CSS Variable Based
        // ========================================
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        aurora: {
          from: { backgroundPosition: "50% 50%, 50% 50%" },
          to: { backgroundPosition: "350% 50%, 350% 50%" },
        },
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "shimmer-slide": {
          to: {
            transform: "translate(calc(100cqw - 100%), 0)",
          },
        },
        "spin-around": {
          "0%": {
            transform: "translateZ(0) rotate(0)",
          },
          "15%, 35%": {
            transform: "translateZ(0) rotate(90deg)",
          },
          "65%, 85%": {
            transform: "translateZ(0) rotate(270deg)",
          },
          "100%": {
            transform: "translateZ(0) rotate(360deg)",
          },
        },
      },
      animation: {
        aurora: "aurora 60s linear infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "shimmer-slide": "shimmer-slide var(--speed) ease-in-out infinite alternate",
        "spin-around": "spin-around calc(var(--speed) * 2) infinite linear",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    plugin(function ({ addBase, theme }) {
      // Flatten all colors to CSS variables for maximum flexibility
      const allColors = flattenColorPalette(theme("colors"));
      const newVars = Object.fromEntries(
        Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
      );

      addBase({
        ":root": {
          // All Tailwind colors as CSS variables
          ...newVars,

          // ========================================
          // 🌌 AURORA BACKGROUND MASTER CONTROLS - Light Mode Default
          // ========================================
          "--aurora-bg": AURORA_LIGHT_CONTROLS.mainBackground,
          "--aurora-bg-dark": AURORA_LIGHT_CONTROLS.mainBackgroundDark,
          "--aurora-stripe-light": AURORA_LIGHT_CONTROLS.stripeLight,
          "--aurora-stripe-dark": AURORA_LIGHT_CONTROLS.stripeDark,
          "--aurora-flow-1": AURORA_LIGHT_CONTROLS.auroraFlow1,
          "--aurora-flow-2": AURORA_LIGHT_CONTROLS.auroraFlow2,
          "--aurora-flow-3": AURORA_LIGHT_CONTROLS.auroraFlow3,
          "--aurora-flow-4": AURORA_LIGHT_CONTROLS.auroraFlow4,
          "--aurora-flow-5": AURORA_LIGHT_CONTROLS.auroraFlow5,
        },
      });
    }),
  ],
};

export default config;
