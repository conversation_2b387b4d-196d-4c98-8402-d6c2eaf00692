{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/bento-grid.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\nimport { ArrowRightIcon } from \"@radix-ui/react-icons\";\nimport Link from \"next/link\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst BentoGrid = ({\n  children,\n  className,\n}: {\n  children: ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"grid w-full auto-rows-[20rem] grid-cols-3 gap-4\",\n        // 16px gap as specified in design system\n        \"gap-4\", // 16px\n        className,\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst BentoCard = ({\n  name,\n  className,\n  background,\n  Icon,\n  description,\n  services,\n  href,\n  cta,\n}: {\n  name: string;\n  className: string;\n  background: ReactNode;\n  Icon: any;\n  description: string;\n  services?: string[];\n  href: string;\n  cta: string;\n}) => (\n  <div\n    key={name}\n    className={cn(\n      \"group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl\",\n      // New Visual Identity - Light Charcoal with subtle sage borders\n      \"bg-light-charcoal border border-sage/30 shadow-xl\",\n      // Hover effect with gold glow\n      \"hover:shadow-[0_0_20px_rgba(184,149,106,0.3)] transition-all duration-300\",\n      \"transform-gpu\",\n      className,\n    )}\n  >\n    <div>{background}</div>\n    <div className=\"pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-4 transition-all duration-300 group-hover:-translate-y-8\">\n      <Icon className=\"h-10 w-10 origin-left transform-gpu transition-all duration-300 ease-in-out group-hover:scale-75\" style={{ color: 'var(--icon-accent)' }} />\n      <h3 className=\"text-lg font-semibold text-light-off-white font-tai-heritage leading-tight\">\n        {name}\n      </h3>\n      <p className=\"max-w-lg text-warm-beige font-sans text-sm leading-relaxed\">{description}</p>\n    </div>\n\n    <div\n      className={cn(\n        \"pointer-events-none absolute bottom-0 flex w-full translate-y-12 transform-gpu flex-col p-3 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100 bg-gradient-to-t from-light-charcoal/95 to-transparent\",\n      )}\n    >\n      {services ? (\n        <div className=\"text-warm-beige/90 text-xs leading-relaxed font-sans space-y-0.5 max-h-20 overflow-hidden\">\n          {services.map((service, idx) => (\n            <div key={idx} className=\"text-left truncate\">\n              {service}\n            </div>\n          ))}\n        </div>\n      ) : (\n        <Button variant=\"ghost\" asChild size=\"sm\" className=\"pointer-events-auto\">\n          <Link href={href}>\n            {cta}\n            <ArrowRightIcon className=\"ml-2 h-4 w-4\" />\n          </Link>\n        </Button>\n      )}\n    </div>\n    <div className=\"pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-muted-gold/[.05] group-hover:dark:bg-muted-gold/10\" />\n  </div>\n);\n\nexport { BentoCard, BentoGrid };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AACA;;;;;;AAEA,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mDACA,yCAAyC;QACzC,SACA;kBAGD;;;;;;AAGP;AAEA,MAAM,YAAY,CAAC,EACjB,IAAI,EACJ,SAAS,EACT,UAAU,EACV,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,GAAG,EAUJ,iBACC,8OAAC;QAEC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sFACA,gEAAgE;QAChE,qDACA,8BAA8B;QAC9B,6EACA,iBACA;;0BAGF,8OAAC;0BAAK;;;;;;0BACN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;wBAAmG,OAAO;4BAAE,OAAO;wBAAqB;;;;;;kCACxJ,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,8OAAC;wBAAE,WAAU;kCAA8D;;;;;;;;;;;;0BAG7E,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAGD,yBACC,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,oBACtB,8OAAC;4BAAc,WAAU;sCACtB;2BADO;;;;;;;;;6EAMd,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,OAAO;oBAAC,MAAK;oBAAK,WAAU;8BAClD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM;;4BACT;0CACD,8OAAC,gLAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;;;;;;;OA1CV", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shimmer-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n}\n\nconst ShimmerButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor,\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background,\n      className,\n      children,\n      size = \"md\",\n      variant = \"primary\",\n      ...props\n    },\n    ref,\n  ) => {\n    // Size-based styling\n    const sizeClasses = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-base\",\n      lg: \"px-8 py-4 text-lg\"\n    };\n\n    // Color variants with customization support\n    const getColors = () => {\n      const baseColors = {\n        primary: {\n          bg: background || \"var(--master-button-primary-bg)\",\n          shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        },\n        secondary: {\n          bg: background || \"var(--master-button-secondary-bg)\",\n          shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        },\n        accent: {\n          bg: background || \"var(--master-brand-accent)\",\n          shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        }\n      };\n      return baseColors[variant];\n    };\n\n    const colors = getColors();\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": colors.shimmer,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": colors.bg,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          sizeClasses[size],\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* spark container */}\n        <div\n          className={cn(\n            \"-z-30 blur-[2px]\",\n            \"absolute inset-0 overflow-visible [container-type:size]\",\n          )}\n        >\n          {/* spark */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            {/* spark before */}\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n        {children}\n\n        {/* Highlight */}\n        <div\n          className={cn(\n            \"insert-0 absolute size-full\",\n            \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            // transition\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            // on hover\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            // on click\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n          )}\n        />\n\n        {/* backdrop */}\n        <div\n          className={cn(\n            \"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\",\n          )}\n        />\n      </button>\n    );\n  },\n);\n\nShimmerButton.displayName = \"ShimmerButton\";\n\nexport { ShimmerButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACpC,CACE,EACE,YAAY,EACZ,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,GAAG,OACJ,EACD;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4CAA4C;IAC5C,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,WAAW;gBACT,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,QAAQ;gBACN,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB,OAAO,OAAO;YACjC,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ,OAAO,EAAE;QACnB;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+MACA,qFACA,WAAW,CAAC,KAAK,EACjB;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oBACA;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;YAGlB;0BAGD,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+BACA,oFACA,aAAa;gBACb,yDACA,WAAW;gBACX,oDACA,WAAW;gBACX;;;;;;0BAKJ,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKV;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/get-started-button.tsx"], "sourcesContent": ["import { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface GetStartedButtonProps {\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  onClick?: () => void;\n  href?: string;\n}\n\nexport function GetStartedButton({\n  className,\n  size = \"lg\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  onClick,\n  href,\n}: GetStartedButtonProps) {\n  return (\n    <ShimmerButton\n      className={cn(\"group relative overflow-hidden\", className)}\n      size={size}\n      variant={variant}\n      shimmerColor={shimmerColor}\n      background={backgroundColor}\n      onClick={onClick}\n    >\n      <span className=\"mr-8 transition-opacity duration-500 group-hover:opacity-0\">\n        Get Started\n      </span>\n      <i className=\"absolute right-0.5 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-white/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95\">\n        <ChevronRight size={16} strokeWidth={2} aria-hidden=\"true\" className=\"text-white\" />\n      </i>\n    </ShimmerButton>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAYO,SAAS,iBAAiB,EAC/B,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,OAAO,EACP,IAAI,EACkB;IACtB,qBACE,8OAAC,sIAAA,CAAA,gBAAa;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAChD,MAAM;QACN,SAAS;QACT,cAAc;QACd,YAAY;QACZ,SAAS;;0BAET,8OAAC;gBAAK,WAAU;0BAA6D;;;;;;0BAG7E,8OAAC;gBAAE,WAAU;0BACX,cAAA,8OAAC,sNAAA,CAAA,eAAY;oBAAC,MAAM;oBAAI,aAAa;oBAAG,eAAY;oBAAO,WAAU;;;;;;;;;;;;;;;;;AAI7E", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/features/page.tsx"], "sourcesContent": ["import { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { BentoCard, BentoGrid } from \"@/components/ui/bento-grid\";\nimport { GetStartedButton } from \"@/components/ui/get-started-button\";\nimport { LayoutTemplate, FileText, Users, BarChart2, Palette } from \"lucide-react\";\n\nexport default function Features() {\n  return (\n    <div className=\"page-features min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-notable\">\n            POWERFUL FEATURES FOR YOUR BUSINESS\n          </h1>\n          <p className=\"text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-sans\">\n            Everything you need to build, manage, and grow your business in one integrated platform.\n          </p>\n        </div>\n      </section>\n\n      {/* Features Bento Grid */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <BentoGrid className=\"lg:grid-rows-3 max-w-6xl mx-auto min-h-[800px]\">\n            {[\n              {\n                Icon: LayoutTemplate,\n                name: \"Website Builder\",\n                description: \"Create stunning websites with our drag-and-drop builder. No coding required.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3\",\n              },\n              {\n                Icon: FileText,\n                name: \"Smart Invoicing\",\n                description: \"Generate professional invoices and get paid faster with automated reminders.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3\",\n              },\n              {\n                Icon: Users,\n                name: \"CRM System\",\n                description: \"Manage customer relationships and track leads through your sales pipeline.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4\",\n              },\n              {\n                Icon: BarChart2,\n                name: \"Analytics Dashboard\",\n                description: \"Get insights into your business performance with real-time analytics.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2\",\n              },\n              {\n                Icon: Palette,\n                name: \"Brand Management\",\n                description: \"Maintain consistent branding across all your business touchpoints.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4\",\n              },\n            ].map((feature) => (\n              <BentoCard key={feature.name} {...feature} />\n            ))}\n          </BentoGrid>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center\">\n            <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-light-off-white drop-shadow-lg font-tai-heritage\">\n              Ready to transform your business?\n            </h2>\n            <p className=\"max-w-[42rem] leading-normal text-warm-beige sm:text-xl sm:leading-8 drop-shadow-sm font-sans\">\n              Join beauty entrepreneurs and business owners who trust Vierla to power their business operations.\n            </p>\n            <div className=\"space-x-4\">\n              <a href=\"/apply\">\n                <GetStartedButton />\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqG;;;;;;sCAGnH,8OAAC;4BAAE,WAAU;sCAAsG;;;;;;;;;;;;;;;;;0BAOvH,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB;4BACC;gCACE,MAAM,0NAAA,CAAA,iBAAc;gCACpB,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,8MAAA,CAAA,WAAQ;gCACd,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,oMAAA,CAAA,QAAK;gCACX,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,gOAAA,CAAA,YAAS;gCACf,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,wMAAA,CAAA,UAAO;gCACb,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;yBACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC,kIAAA,CAAA,YAAS;gCAAqB,GAAG,OAAO;+BAAzB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoH;;;;;;0CAGlI,8OAAC;gCAAE,WAAU;0CAAgG;;;;;;0CAG7G,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;8CACN,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}]}