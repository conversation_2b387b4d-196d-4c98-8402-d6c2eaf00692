# NGINX Configuration for Vierla Error Pages and Maintenance Mode
# This configuration is specifically for vierla.com and should be included in the main server block

# Error page locations - these files should be placed in the web root
error_page 404 /error-404.html;
error_page 500 502 503 504 /error-500.html;

# Maintenance mode configuration
# To enable maintenance mode, create a file named 'maintenance.flag' in the web root
# To disable maintenance mode, remove the 'maintenance.flag' file

# Check for maintenance mode
location / {
    # Check if maintenance flag exists
    if (-f $document_root/maintenance.flag) {
        return 503;
    }
    
    # Normal proxy pass to Next.js application
    try_files $uri $uri/ @nextjs;
}

# Maintenance page configuration
location = /maintenance.html {
    internal;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# Error page configurations
location = /error-404.html {
    internal;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

location = /error-500.html {
    internal;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# Custom 503 error page for maintenance mode
error_page 503 @maintenance;
location @maintenance {
    rewrite ^(.*)$ /maintenance.html break;
    root /var/www/vierla.com/public;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
    add_header Retry-After 3600;  # Suggest retry after 1 hour
}

# Proxy configuration for Next.js application
location @nextjs {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    
    # Error handling
    proxy_intercept_errors on;
}

# Prevent access to maintenance flag file
location = /maintenance.flag {
    deny all;
    return 404;
}

# Security headers for error pages
location ~* \.(html)$ {
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}

# Logging configuration for error pages
access_log /var/log/nginx/vierla-access.log;
error_log /var/log/nginx/vierla-error.log warn;
