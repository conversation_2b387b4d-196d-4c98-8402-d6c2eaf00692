"use client";
import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export const BackgroundBeams = ({ className }: { className?: string }) => {
  return (
    <div
      className={cn(
        "absolute top-0 left-0 w-full h-full -z-10",
        className
      )}
    >
      <div className="relative w-full h-full overflow-hidden">
        <div className="absolute inset-0 bg-zinc-900"></div>
        <div className="absolute h-full w-full">
          {/* Placeholder for beam elements */}
          {/* This component often requires more complex SVG/div structures for the beams themselves. */}
          {/* The following is a simplified representation. Refer to the source for the full SVG implementation. */}
          <div className="absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_8s_linear_infinite]"></div>
          <div className="absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_10s_linear_infinite_2s_]"></div>
          <div className="absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_9s_linear_infinite_1s_]"></div>
        </div>
      </div>
    </div>
  );
};
