import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import ShinyButton from "@/components/ui/shiny-button";
import { Users, Target, Lightbulb, Heart } from "lucide-react";

export default function About() {
  return (
    <div className="page-about min-h-screen relative overflow-hidden">
      <AuroraBackgroundLayer />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-notable">
            WE'RE ON A MISSION TO EMPOWER ENTREPRENEURS
          </h1>
          <p className="text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-sans">
            Building the future of business operations, one entrepreneur at a time.
          </p>
        </div>
      </section>

      {/* Our Mission Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid gap-8 lg:grid-cols-2 items-center">
              <div className="space-y-4">
                <GoldenGlowingCardContainer>
                  <Card className="bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl p-8">
                    <div className="flex items-center space-x-2 mb-6">
                      <Target className="h-8 w-8 text-sage drop-shadow-lg" />
                      <h2 className="text-3xl font-bold text-light-off-white drop-shadow-lg font-tai-heritage">Our Mission</h2>
                    </div>
                    <p className="text-lg text-warm-beige leading-relaxed drop-shadow-sm mb-4 font-sans">
                      We started Vierla because we saw too many small business owners and freelancers struggling to manage a dozen different software subscriptions. It's costly, complex, and time-consuming. Our mission is to consolidate all the essential tools into a single, intelligent, and affordable platform, giving entrepreneurs their time back so they can focus on what they do best: growing their business.
                    </p>
                    <p className="text-lg text-warm-beige leading-relaxed drop-shadow-sm font-sans">
                      Every feature we build is designed with the modern entrepreneur in mind - from the solo freelancer just starting out to the growing agency scaling their operations. We believe that powerful business tools shouldn't require a technical degree or a massive budget to use effectively.
                    </p>
                  </Card>
                </GoldenGlowingCardContainer>
              </div>
              <div className="flex items-center justify-center">
                <div className="w-20 h-20 mx-auto rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30">
                  <Heart className="w-10 h-10 text-sage drop-shadow-lg" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Vision Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24">
        <div className="mx-auto max-w-4xl">
          <div className="grid gap-8 lg:grid-cols-2 items-center">
            <div className="flex items-center justify-center lg:order-1">
              <div className="w-20 h-20 mx-auto rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30">
                <Lightbulb className="w-10 h-10 text-sage drop-shadow-lg" />
              </div>
            </div>
            <div className="space-y-4 lg:order-2">
              <GoldenGlowingCardContainer>
                <Card className="bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl p-8">
                  <div className="flex items-center space-x-2 mb-6">
                    <Lightbulb className="h-8 w-8 text-sage drop-shadow-lg" />
                    <h2 className="text-3xl font-bold text-light-off-white drop-shadow-lg font-tai-heritage">Our Vision</h2>
                  </div>
                  <p className="text-lg text-warm-beige leading-relaxed drop-shadow-sm mb-4 font-sans">
                    We envision a future where starting and running a business is radically simpler. By leveraging the power of AI, we aim to automate administrative busywork and provide powerful insights that were once only available to large corporations. We're building the operating system for the next generation of business.
                  </p>
                  <p className="text-lg text-warm-beige leading-relaxed drop-shadow-sm font-sans">
                    Our vision extends beyond just software - we're creating an ecosystem where entrepreneurs can thrive, connect, and grow together. We believe that when we remove the barriers to business success, we unlock human potential and drive innovation across every industry.
                  </p>
                </Card>
              </GoldenGlowingCardContainer>
            </div>
          </div>
        </div>
      </section>

      {/* Meet the Team Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Users className="h-8 w-8 text-sage drop-shadow-lg" />
              <h2 className="text-3xl font-bold text-light-off-white drop-shadow-lg font-tai-heritage">Meet the Team</h2>
            </div>
            <div className="w-full h-px bg-gradient-to-r from-transparent via-sage/20 to-transparent mb-6"></div>
            <p className="text-lg text-warm-beige max-w-2xl mx-auto drop-shadow-sm font-sans">
              We're a passionate group of entrepreneurs, developers, and designers united by our mission to simplify business operations.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 justify-center">
            {[1, 2, 3, 4, 5, 6].map((member) => (
              <GoldenGlowingCardContainer key={member}>
                <Card className="text-center bg-light-charcoal backdrop-blur-md border border-sage/20 shadow-xl rounded-2xl">
                  <CardHeader className="text-center">
                    <div className="w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-4 bg-sage/20 border-2 border-sage/30">
                      <Users className="h-12 w-12 text-sage drop-shadow-lg" />
                    </div>
                    <CardTitle className="text-light-off-white drop-shadow-lg text-center font-tai-heritage">Team Member {member}</CardTitle>
                    <CardDescription className="text-sage drop-shadow-sm text-center font-sans">Position Title</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-sm text-warm-beige drop-shadow-sm text-center font-sans">
                      Brief bio and background information will be added here.
                    </p>
                  </CardContent>
                </Card>
              </GoldenGlowingCardContainer>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto">
        <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
          <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-6"></div>
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-light-off-white drop-shadow-lg text-center font-tai-heritage">
            Ready to join our mission?
          </h2>
          <p className="max-w-[42rem] leading-normal text-warm-beige sm:text-xl sm:leading-8 drop-shadow-sm text-center font-sans">
            Be part of the future of business operations. Start your journey with Vierla today.
          </p>
          <div className="flex justify-center">
            <ShinyButton size="lg" className="px-8 py-4 text-lg">
              <a href="/apply">Get Started</a>
            </ShinyButton>
          </div>
          <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mt-6"></div>
        </div>
      </section>

    </div>
  );
}
