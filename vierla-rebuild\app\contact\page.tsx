"use client"
import { useState } from 'react'
import Link from "next/link"
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import ShinyButton from "@/components/ui/shiny-button";
import { ShimmerButton } from "@/components/ui/shimmer-button";
import { useToast } from "@/components/ui/toast";
import { validateForm, contactFormSchema, sanitizeInput, rateLimiter } from "@/lib/validation";
import { DropdownSelect, type DropdownOption } from "@/components/ui/dropdown-select";

export default function ContactPage() {
  const { addToast } = useToast();

  // Contact type options for dropdown
  const contactTypeOptions: DropdownOption[] = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'customer', label: 'Customer Support' },
    { value: 'professional', label: 'Professional Services' },
    { value: 'partnership', label: 'Partnership Opportunity' },
    { value: 'support', label: 'Technical Support' }
  ];

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')
    setValidationErrors({})

    // Sanitize input data
    const sanitizedData = {
      name: sanitizeInput(formData.name),
      email: sanitizeInput(formData.email),
      subject: sanitizeInput(formData.subject),
      message: sanitizeInput(formData.message),
      type: formData.type
    }

    // Validate form data
    const validation = validateForm(sanitizedData, contactFormSchema)
    if (!validation.isValid) {
      setValidationErrors(validation.errors)
      setIsSubmitting(false)
      addToast({
        type: 'error',
        title: 'Validation Error',
        description: 'Please fix the errors below and try again.'
      })
      return
    }

    // Rate limiting check
    const userIdentifier = sanitizedData.email || 'anonymous'
    if (!rateLimiter.isAllowed(userIdentifier, 3, 15 * 60 * 1000)) {
      const remainingTime = Math.ceil(rateLimiter.getRemainingTime(userIdentifier, 15 * 60 * 1000) / 1000 / 60)
      setIsSubmitting(false)
      addToast({
        type: 'error',
        title: 'Too Many Attempts',
        description: `Please wait ${remainingTime} minutes before submitting again.`
      })
      return
    }

    try {
      // Submit to backend API with fallback to localStorage
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...sanitizedData,
          timestamp: new Date().toISOString()
        }),
      })

      const result = await response.json()

      if (result.success) {
        setIsSubmitted(true)
        setFormData({ name: '', email: '', subject: '', message: '', type: 'general' })
        addToast({
          type: 'success',
          title: 'Message Sent!',
          description: 'Thank you for contacting us. We\'ll get back to you soon.'
        })
      } else {
        setError(result.error || 'Something went wrong. Please try again.')
        addToast({
          type: 'error',
          title: 'Submission Failed',
          description: result.error || 'Something went wrong. Please try again.'
        })
      }
    } catch (err) {
      // Fallback to localStorage
      const existingContacts = JSON.parse(localStorage.getItem('vierla-contacts') || '[]')
      existingContacts.push({
        ...formData,
        timestamp: new Date().toISOString()
      })
      localStorage.setItem('vierla-contacts', JSON.stringify(existingContacts))
      setIsSubmitted(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="page-contact min-h-screen relative overflow-hidden">
      <AuroraBackgroundLayer />

      {/* Main Content */}
      <main className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-black text-light-off-white mb-6 drop-shadow-lg font-notable">
              CONTACT US
            </h1>
            <p className="text-xl text-warm-beige max-w-2xl mx-auto drop-shadow-sm font-sans">
              Have questions about our services? Want to join our platform as a beauty professional? We'd love to hear from you.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-light-charcoal backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-sage/30">
              <h2 className="text-2xl font-bold text-light-off-white mb-6 drop-shadow-lg font-tai-heritage">
                Send us a message
              </h2>
              
              {isSubmitted ? (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 bg-primary/20">
                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-light-off-white mb-2 drop-shadow-lg font-tai-heritage">Message Sent!</h3>
                  <p className="text-warm-beige drop-shadow-sm font-sans">Thank you for reaching out. We'll get back to you within 24 hours.</p>
                </div>
              ) : (
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans">Name *</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm font-sans"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans">Email *</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm font-sans"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <DropdownSelect
                      label="Contact Type"
                      options={contactTypeOptions}
                      value={formData.type}
                      onChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                      error={validationErrors.type}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans">Subject *</label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm font-sans"
                      placeholder="What's this about?"
                    />
                  </div>

                  <div>
                    <label className="block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans">Message *</label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm resize-none font-sans"
                      placeholder="Tell us more about your inquiry..."
                    />
                  </div>

                  {error && (
                    <div className="p-4 rounded-xl bg-red-500/20 border border-red-500/30">
                      <p className="text-red-200 text-sm drop-shadow-sm">{error}</p>
                    </div>
                  )}

                  <ShimmerButton
                    type="submit"
                    size="lg"
                    background="#B8956A"
                    shimmerColor="#E5D4A1"
                    className="w-full py-4 text-lg font-medium text-[#2D2A26]"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Sending..." : "Send Message"}
                  </ShimmerButton>
                </form>
              )}
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div className="bg-light-charcoal backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-sage/30">
                <h3 className="text-xl font-bold text-light-off-white mb-6 drop-shadow-lg font-tai-heritage">
                  Get in Touch
                </h3>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-sage/20 border border-sage/30">
                      <svg className="w-6 h-6 text-sage drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-light-off-white mb-1 drop-shadow-lg font-tai-heritage">Email</h3>
                      <p className="text-warm-beige drop-shadow-sm font-sans"><EMAIL></p>
                      <p className="text-warm-beige/60 text-sm drop-shadow-sm font-sans">We typically respond within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-sage/20 border border-sage/30">
                      <svg className="w-6 h-6 text-sage drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-light-off-white mb-1 drop-shadow-lg font-tai-heritage">Location</h3>
                      <p className="text-warm-beige drop-shadow-sm font-sans">Toronto & Ottawa, Ontario</p>
                      <p className="text-warm-beige/60 text-sm drop-shadow-sm font-sans">Serving the Greater Toronto and Ottawa areas</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-sage/20 border border-sage/30">
                      <svg className="w-6 h-6 text-sage drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-light-off-white mb-1 drop-shadow-lg font-tai-heritage">Response Time</h3>
                      <p className="text-warm-beige drop-shadow-sm font-sans">Within 24 hours</p>
                      <p className="text-warm-beige/60 text-sm drop-shadow-sm font-sans">Monday to Friday, 9 AM - 6 PM EST</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-light-charcoal backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-sage/30">
                <h3 className="text-xl font-bold text-light-off-white mb-4 drop-shadow-lg font-tai-heritage">
                  For Beauty Professionals
                </h3>
                <p className="text-warm-beige mb-4 drop-shadow-sm font-sans">
                  Interested in joining our platform? We're always looking for talented, licensed beauty professionals.
                </p>
                <Link href="/apply">
                  <ShimmerButton
                    size="md"
                    background="#B8956A"
                    shimmerColor="#E5D4A1"
                    className="w-full px-4 py-3 text-base font-medium text-[#2D2A26]"
                  >
                    Apply Now
                  </ShimmerButton>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
