#!/bin/bash

# Vierla Deployment Verification Script
# This script verifies that the Vierla application is deployed correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Check if running from correct directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the project root directory."
    exit 1
fi

print_header "Vierla Deployment Verification"

# 1. Check Docker and Docker Compose
print_header "Checking Prerequisites"

if command -v docker &> /dev/null; then
    print_success "Docker is installed: $(docker --version)"
else
    print_error "Docker is not installed"
    exit 1
fi

if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
    print_success "Docker Compose v1 is available"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
    print_success "Docker Compose v2 is available"
else
    print_error "Docker Compose is not available"
    exit 1
fi

# 2. Check external network
print_header "Checking Docker Networks"

if docker network inspect web &> /dev/null; then
    print_success "External network 'web' exists"
else
    print_warning "External network 'web' does not exist"
    print_info "Creating network..."
    docker network create web
    print_success "Created external network 'web'"
fi

# 3. Check container status
print_header "Checking Container Status"

if $DOCKER_COMPOSE_CMD ps | grep -q "vierla-web"; then
    CONTAINER_STATUS=$($DOCKER_COMPOSE_CMD ps --format "table {{.Service}}\t{{.State}}\t{{.Status}}" | grep vierla-web)
    echo "Container Status: $CONTAINER_STATUS"
    
    if $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
        print_success "Container is running"
    else
        print_error "Container is not running"
        print_info "Recent logs:"
        $DOCKER_COMPOSE_CMD logs --tail=10 vierla-web
        exit 1
    fi
else
    print_error "Container 'vierla-web' not found"
    print_info "Try running: $DOCKER_COMPOSE_CMD up -d"
    exit 1
fi

# 4. Check health status
print_header "Checking Health Status"

HEALTH_STATUS=$(docker inspect vierla-web --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-healthcheck")

case $HEALTH_STATUS in
    "healthy")
        print_success "Container is healthy"
        ;;
    "unhealthy")
        print_error "Container is unhealthy"
        print_info "Health check logs:"
        docker inspect vierla-web --format='{{range .State.Health.Log}}{{.Output}}{{end}}'
        ;;
    "starting")
        print_warning "Container is still starting (health check in progress)"
        ;;
    "no-healthcheck")
        print_warning "No health check configured"
        ;;
    *)
        print_warning "Unknown health status: $HEALTH_STATUS"
        ;;
esac

# 5. Test internal connectivity
print_header "Testing Internal Connectivity"

if docker exec vierla-web curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
    print_success "Health endpoint is accessible from inside container"
    
    # Get health data
    HEALTH_DATA=$(docker exec vierla-web curl -s http://localhost:3000/api/health)
    echo "Health Data: $HEALTH_DATA"
else
    print_error "Health endpoint is not accessible from inside container"
    print_info "Trying main page..."
    if docker exec vierla-web curl -f -s http://localhost:3000/ > /dev/null 2>&1; then
        print_warning "Main page is accessible but health endpoint is not"
    else
        print_error "Application is not responding at all"
    fi
fi

# 6. Test external connectivity (if port is bound)
print_header "Testing External Connectivity"

if curl -f -s http://127.0.0.1:3000/api/health > /dev/null 2>&1; then
    print_success "Application is accessible from host on port 3000"
else
    print_warning "Application is not accessible from host on port 3000"
    print_info "This is expected if the port is only bound to localhost for nginx proxy"
fi

# 7. Check nginx configuration (if nginx is running)
print_header "Checking Nginx Configuration"

if command -v nginx &> /dev/null && systemctl is-active --quiet nginx; then
    print_success "Nginx is running"
    
    if nginx -t &> /dev/null; then
        print_success "Nginx configuration is valid"
    else
        print_error "Nginx configuration has errors"
        nginx -t
    fi
    
    # Check if vierla config exists
    if [ -f "/etc/nginx/sites-enabled/vierla.conf" ] || [ -f "/etc/nginx/conf.d/vierla.conf" ]; then
        print_success "Vierla nginx configuration found"
    else
        print_warning "Vierla nginx configuration not found in standard locations"
    fi
else
    print_info "Nginx is not running on this host (may be running in Docker)"
fi

# 8. Check logs for errors
print_header "Checking Recent Logs"

ERROR_COUNT=$($DOCKER_COMPOSE_CMD logs --tail=50 vierla-web | grep -i error | wc -l)
if [ "$ERROR_COUNT" -eq 0 ]; then
    print_success "No errors found in recent logs"
else
    print_warning "Found $ERROR_COUNT error(s) in recent logs"
    print_info "Recent errors:"
    $DOCKER_COMPOSE_CMD logs --tail=50 vierla-web | grep -i error | tail -5
fi

# 9. Check disk space
print_header "Checking System Resources"

DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    print_success "Disk usage is acceptable ($DISK_USAGE%)"
else
    print_warning "Disk usage is high ($DISK_USAGE%)"
fi

# Check Docker disk usage
DOCKER_DISK=$(docker system df --format "table {{.Type}}\t{{.Size}}" | grep -v TYPE)
print_info "Docker disk usage:"
echo "$DOCKER_DISK"

# 10. Summary
print_header "Verification Summary"

if [ "$HEALTH_STATUS" = "healthy" ] && docker exec vierla-web curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
    print_success "✅ Deployment verification PASSED"
    print_info "Your Vierla application appears to be deployed correctly!"
    print_info "Next steps:"
    echo "  - Configure your domain DNS to point to this server"
    echo "  - Set up SSL certificates with Let's Encrypt"
    echo "  - Configure nginx reverse proxy"
    echo "  - Test external access via your domain"
else
    print_error "❌ Deployment verification FAILED"
    print_info "Issues found that need attention:"
    echo "  - Check container health status"
    echo "  - Review application logs"
    echo "  - Verify configuration files"
    echo "  - See docs/TROUBLESHOOTING.md for detailed help"
fi

print_info "\nFor detailed troubleshooting, run:"
echo "  docker compose logs -f vierla-web"
echo "  docker inspect vierla-web"
echo "  See docs/TROUBLESHOOTING.md"
