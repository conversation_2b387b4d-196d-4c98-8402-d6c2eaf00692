"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GetStartedButton } from "@/components/ui/get-started-button";
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>ger, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { HeartIcon } from "@/components/ui/heart-icon";
import { ThemeSwitcher } from "@/components/ui/theme-switcher";

export const Navbar = React.memo(function Navbar() {
  const pathname = usePathname();

  // Simple function to check if a link is active
  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <header className="flex w-full items-center justify-between px-4 md:px-6 absolute top-0 left-0 z-50 border-b" style={{
      height: 'var(--header-height, 5rem)', /* Mobile: 3.33rem, Desktop: 5rem */
      background: 'var(--master-header-background)',
      borderColor: 'var(--master-header-border)',
      backdropFilter: `blur(var(--master-header-backdrop-blur))`
    }}>
      <Link href="/" className="flex items-center gap-3 group" prefetch={false}>
        <div className="backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-all duration-300" style={{
          width: 'var(--header-logo-size, 3rem)', /* Mobile: 2rem, Desktop: 3rem */
          height: 'var(--header-logo-size, 3rem)', /* Mobile: 2rem, Desktop: 3rem */
          background: 'var(--master-header-logo-bg)',
          borderColor: 'var(--master-header-logo-border)',
          border: '1px solid'
        }}>
          <HeartIcon className="w-5 h-5 md:w-7 md:h-7" style={{ color: 'var(--master-header-logo-icon)' }} />
        </div>
        <span className="font-bold drop-shadow-lg font-playfair-display" style={{
          fontSize: 'var(--header-nav-font-size, 2rem)', /* Mobile: 0.75rem, Desktop: 2rem */
          color: 'var(--master-header-brand-text)'
        }}>Vierla</span>
      </Link>
      <div className="hidden items-center gap-2 text-sm font-medium md:flex">
        <nav className="flex items-center gap-4 mr-2">
          <Link
            href="/"
            className={`nav-link limelight-nav ${isActive('/') ? 'nav-link-active' : ''}`}
          >
            Home
          </Link>
          <Link
            href="/features"
            className={`nav-link limelight-nav ${isActive('/features') ? 'nav-link-active' : ''}`}
          >
            Features
          </Link>
          <Link
            href="/pricing"
            className={`nav-link limelight-nav ${isActive('/pricing') ? 'nav-link-active' : ''}`}
          >
            Pricing
          </Link>
          <Link
            href="/about"
            className={`nav-link ${isActive('/about') ? 'nav-link-active' : ''}`}
          >
            About
          </Link>
          <Link
            href="/contact"
            className={`nav-link ${isActive('/contact') ? 'nav-link-active' : ''}`}
          >
            Contact
          </Link>
        </nav>

        {/* Theme Switcher */}
        <ThemeSwitcher />

        {/* Get Started Button */}
        <Link href="/apply">
          <GetStartedButton size="sm" />
        </Link>
      </div>
      {/* Mobile Menu and Theme Switcher */}
      <div className="flex items-center gap-3 md:hidden">
        <ThemeSwitcher />
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="bg-transparent hover:bg-brand-sage/10 transition-colors border-0">
              <Menu className="h-6 w-6 text-neutral-off-white" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
        <SheetContent side="right" className="bg-neutral-charcoal-dark/95 backdrop-blur-xl border-brand-sage/20 w-[300px] sm:w-[350px]">
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <div className="flex flex-col space-y-6 mt-8">
            {/* Mobile Logo */}
            <div className="flex items-center gap-3 pb-4 border-b border-brand-sage/20">
              <HeartIcon className="w-8 h-8 text-brand-sage" />
              <span className="text-2xl font-bold text-neutral-off-white font-tai-heritage">Vierla</span>
            </div>

            {/* Navigation Links */}
            <div className="flex flex-col space-y-4">
              <Link
                href="/"
                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/')
                    ? 'text-brand-gold bg-brand-gold/10'
                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'
                }`}
              >
                Home
              </Link>
              <Link
                href="/features"
                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/features')
                    ? 'text-brand-gold bg-brand-gold/10'
                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'
                }`}
              >
                Features
              </Link>
              <Link
                href="/pricing"
                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/pricing')
                    ? 'text-brand-gold bg-brand-gold/10'
                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'
                }`}
              >
                Pricing
              </Link>
              <Link
                href="/about"
                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/about')
                    ? 'text-brand-gold bg-brand-gold/10'
                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'
                }`}
              >
                About
              </Link>
              <Link
                href="/contact"
                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/contact')
                    ? 'text-brand-gold bg-brand-gold/10'
                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'
                }`}
              >
                Contact
              </Link>
            </div>

            {/* CTA Button */}
            <div className="pt-6 border-t border-brand-sage/20">
              <Link href="/apply" className="block">
                <GetStartedButton className="w-full text-lg py-3" />
              </Link>
            </div>
          </div>
        </SheetContent>
        </Sheet>
      </div>
    </header>
  );
});
