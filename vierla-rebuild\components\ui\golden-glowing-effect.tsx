"use client";

import { memo, useCallback, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";

interface GoldenGlowingEffectProps {
  blur?: number;
  inactiveZone?: number;
  proximity?: number;
  spread?: number;
  glow?: boolean;
  className?: string;
  disabled?: boolean;
  movementDuration?: number;
  borderWidth?: number;
}

const GoldenGlowingEffect = memo(
  ({
    blur = 0,
    inactiveZone = 0.7,
    proximity = 0,
    spread = 20,
    glow = false,
    className,
    movementDuration = 2,
    borderWidth = 1,
    disabled = true,
  }: GoldenGlowingEffectProps) => {
    const containerRef = useRef<HTMLDivElement>(null);

    return (
      <div
        ref={containerRef}
        className={cn(
          "pointer-events-none absolute inset-0 overflow-hidden rounded-[inherit]",
          className
        )}
      >
        {/* Static glow effect using CSS */}
        <div
          className="absolute inset-0 rounded-[inherit] opacity-50"
          style={{
            background: `radial-gradient(circle at 50% 50%, var(--color-primary-shadow) 0%, transparent 70%)`,
            filter: `blur(${blur}px)`,
          }}
        />
        
        {/* Border glow */}
        <div
          className="absolute inset-0 rounded-[inherit]"
          style={{
            background: `linear-gradient(90deg, transparent, var(--color-primary), transparent)`,
            mask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,
            maskComposite: 'xor',
            padding: `${borderWidth}px`,
          }}
        />
      </div>
    );
  }
);

GoldenGlowingEffect.displayName = "GoldenGlowingEffect";

export { GoldenGlowingEffect };
