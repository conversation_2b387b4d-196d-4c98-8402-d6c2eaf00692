"use client"
import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"
import { Footer } from "@/components/ui/footer"

export default function TermsOfService() {
  return (
    <div className="min-h-screen relative overflow-hidden bg-transparent">


      {/* Header */}
      <header className="relative z-10 w-full px-4 py-6 border-b border-white/20">
        <div className="max-w-6xl mx-auto">
          <nav className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
                <HeartIcon className="w-7 h-7 text-white" />
              </div>
              <span className="text-3xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
            </Link>
            <div className="flex items-center space-x-8">
              <Link href="/" className="text-white/90 hover:text-white transition-colors">Home</Link>
              <Link href="/about" className="text-white/90 hover:text-white transition-colors">About</Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-16" style={{background: 'linear-gradient(to bottom right, #364035, #8B9A8C, #364035)', backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat'}}>
        <div className="max-w-4xl mx-auto">
          <div className="p-8 md:p-12">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-8" style={{fontFamily: 'Playfair Display, serif'}}>
              Terms of Service
            </h1>
            
            <div className="prose prose-lg prose-invert max-w-none">
              <p className="text-white/90 text-lg mb-6">
                <strong>Effective Date:</strong> January 2025
              </p>
              
              <p className="text-white/90 mb-6">
                Welcome to Vierla. These Terms of Service ("Terms") govern your use of our website www.vierla.com and our beauty services platform. By accessing or using our services, you agree to be bound by these Terms.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">1. Service Description</h2>
              <p className="text-white/90 mb-6">
                Vierla is a platform that connects customers with vetted beauty professionals for in-home services including hair styling, makeup, nail care, and other beauty treatments. We facilitate bookings but do not directly provide beauty services.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">2. User Accounts</h2>
              <p className="text-white/90 mb-4">To use our services, you must:</p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Be at least 18 years old or have parental consent</li>
                <li>• Provide accurate and complete information</li>
                <li>• Maintain the security of your account credentials</li>
                <li>• Notify us immediately of any unauthorized use</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">3. Booking and Payment</h2>
              <p className="text-white/90 mb-4">When you book services through Vierla:</p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• You enter into a contract with the beauty professional</li>
                <li>• Payment is processed securely through our platform</li>
                <li>• Cancellation policies vary by professional</li>
                <li>• Refunds are subject to our refund policy</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">4. Professional Standards</h2>
              <p className="text-white/90 mb-4">All beauty professionals on our platform must:</p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Hold valid licenses and certifications</li>
                <li>• Maintain professional liability insurance</li>
                <li>• Follow health and safety protocols</li>
                <li>• Provide services as described</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">5. User Conduct</h2>
              <p className="text-white/90 mb-4">You agree not to:</p>
              <ul className="text-white/90 mb-6 space-y-2">
                <li>• Use the platform for illegal activities</li>
                <li>• Harass or discriminate against professionals or other users</li>
                <li>• Provide false information or reviews</li>
                <li>• Attempt to circumvent our platform for direct bookings</li>
              </ul>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">6. Liability and Disclaimers</h2>
              <p className="text-white/90 mb-6">
                Vierla acts as an intermediary platform. While we vet our professionals, we cannot guarantee the quality of services. Beauty professionals are independent contractors responsible for their own services, insurance, and compliance with local regulations.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">7. Intellectual Property</h2>
              <p className="text-white/90 mb-6">
                All content on our platform, including logos, text, images, and software, is owned by Vierla or our licensors and protected by intellectual property laws.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">8. Privacy</h2>
              <p className="text-white/90 mb-6">
                Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">9. Termination</h2>
              <p className="text-white/90 mb-6">
                We may suspend or terminate your account for violations of these Terms or for any reason with appropriate notice. You may close your account at any time.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">10. Dispute Resolution</h2>
              <p className="text-white/90 mb-6">
                Any disputes arising from these Terms will be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">11. Changes to Terms</h2>
              <p className="text-white/90 mb-6">
                We may update these Terms periodically. Continued use of our services after changes constitutes acceptance of the new Terms.
              </p>

              <h2 className="text-2xl font-bold text-white mt-8 mb-4">Contact Information</h2>
              <p className="text-white/90 mb-6">
                For questions about these Terms, contact us at:
                <br />
                Email: <EMAIL>
                {/* <br />
                Address: [To be updated with business address] */}
              </p>

              <p className="text-white/90 text-sm mt-8">
                These Terms of Service constitute the entire agreement between you and Vierla regarding your use of our services.
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />


    </div>
  )
}
