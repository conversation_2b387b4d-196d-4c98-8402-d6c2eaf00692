import Script from 'next/script'

interface LocalSEOProps {
  page?: 'home' | 'apply' | 'privacy'
}

export function LocalSEO({ page = 'home' }: LocalSEOProps) {
  const baseSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Vier<PERSON>",
    "description": "Premium beauty services marketplace connecting customers with top-vetted hair stylists, makeup artists, and nail technicians in Toronto and Ottawa.",
    "url": "https://vierla.com",
    "logo": "https://vierla.com/logo.svg",
    "image": "https://vierla.com/og-image.svg",
    "telephone": "******-XXX-XXXX",
    "email": "<EMAIL>",
    "foundingDate": "2025",
    "founder": {
      "@type": "Organization",
      "name": "Vierla Inc."
    },
    "address": [
      {
        "@type": "PostalAddress",
        "addressLocality": "Toronto",
        "addressRegion": "ON",
        "addressCountry": "CA",
        "streetAddress": "Downtown Toronto"
      },
      {
        "@type": "PostalAddress",
        "addressLocality": "Ottawa",
        "addressRegion": "ON", 
        "addressCountry": "CA",
        "streetAddress": "Downtown Ottawa"
      }
    ],
    "areaServed": [
      {
        "@type": "City",
        "name": "Toronto",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      },
      {
        "@type": "City", 
        "name": "Ottawa",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      }
    ],
    "serviceType": [
      "Hair Styling",
      "Hair Cutting", 
      "Hair Coloring",
      "Makeup Application",
      "Bridal Makeup",
      "Manicures",
      "Pedicures",
      "Nail Art",
      "Eyebrow Services",
      "Eyelash Extensions",
      "Beauty Services"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Beauty Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Hair Styling Services",
            "description": "Professional hair styling, cutting, and coloring services"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Makeup Services",
            "description": "Professional makeup application for all occasions"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Nail Services",
            "description": "Manicures, pedicures, and nail art services"
          }
        }
      ]
    },
    "openingHours": "Mo-Su 08:00-20:00",
    "paymentAccepted": ["Credit Card", "Debit Card", "Online Payment"],
    "currenciesAccepted": "CAD",
    "priceRange": "$$",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "150",
      "bestRating": "5",
      "worstRating": "1"
    },
    "sameAs": [
      "https://www.instagram.com/vierla_beauty",
      "https://www.facebook.com/vierla", 
      "https://www.linkedin.com/company/vierla"
    ]
  }

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Vierla Inc.",
    "alternateName": "Vierla",
    "url": "https://vierla.com",
    "logo": "https://vierla.com/logo.svg",
    "description": "Canada's premier beauty services marketplace connecting customers with verified professionals.",
    "foundingDate": "2025",
    "foundingLocation": {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Toronto",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      }
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-XXX-XXXX",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English", "French"]
    }
  }

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Vierla",
    "url": "https://vierla.com",
    "description": "Book top-vetted beauty professionals in Toronto & Ottawa. Hair styling, makeup, nails, and more.",
    "publisher": {
      "@type": "Organization",
      "name": "Vierla Inc."
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://vierla.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  }

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Beauty Services Marketplace",
    "description": "Connect with verified beauty professionals for hair, makeup, and nail services in Toronto and Ottawa.",
    "provider": {
      "@type": "LocalBusiness",
      "name": "Vierla"
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Toronto",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      },
      {
        "@type": "City",
        "name": "Ottawa", 
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      }
    ],
    "serviceType": "Beauty Services",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Beauty Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Hair Services Toronto",
            "description": "Professional hair styling, cutting, and coloring in Toronto"
          },
          "areaServed": {
            "@type": "City",
            "name": "Toronto"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Hair Services Ottawa",
            "description": "Professional hair styling, cutting, and coloring in Ottawa"
          },
          "areaServed": {
            "@type": "City",
            "name": "Ottawa"
          }
        }
      ]
    }
  }

  return (
    <>
      <Script
        id="local-business-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(baseSchema)
        }}
      />
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
      <Script
        id="service-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />
    </>
  )
}
