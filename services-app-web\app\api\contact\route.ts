import { NextRequest, NextResponse } from 'next/server'
import { sendEmail, createContactFormNotificationEmail } from '@/lib/email'

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  type: string
  timestamp: string
}

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json()
    
    // Validate required fields
    const requiredFields = ['name', 'email', 'subject', 'message', 'type']
    const missingFields = requiredFields.filter(field => !body[field as keyof ContactFormData])
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      )
    }

    // Validate contact type
    const validTypes = ['general', 'customer', 'professional', 'partnership', 'support']
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid contact type'
        },
        { status: 400 }
      )
    }

    // Log the contact form submission
    console.log('Contact form received:', {
      name: body.name,
      email: body.email,
      subject: body.subject,
      type: body.type,
      messageLength: body.message.length,
      timestamp: body.timestamp,
      ip: request.ip || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    // Send email notification to support team
    try {
      const emailHtml = createContactFormNotificationEmail(body)
      await sendEmail({
        to: '<EMAIL>',
        subject: `New Contact Form: ${body.subject}`,
        html: emailHtml
      })
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError)
      // Continue processing even if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Contact form submitted successfully',
      data: {
        id: `contact_${Date.now()}`, // In real app, use proper ID generation
        timestamp: new Date().toISOString(),
        type: body.type
      }
    })

  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
