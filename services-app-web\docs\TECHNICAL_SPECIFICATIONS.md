# 🔧 Technical Specifications - Vierla Platform

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Status**: Production Ready  

---

## 📋 **System Overview**

Vierla is a modern web platform connecting beauty professionals with customers in Toronto and Ottawa. The platform supports both in-home services and studio visits, featuring professional applications, contact management, and comprehensive analytics.

---

## 🏗️ **Architecture**

### **Frontend Architecture**
- **Framework**: Next.js 15.2.4 (React-based)
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React hooks and context
- **Routing**: Next.js App Router
- **Build Tool**: Next.js built-in bundler

### **Backend Architecture**
- **Runtime**: Node.js 18+
- **API**: Next.js API Routes (serverless functions)
- **Database**: PostgreSQL 14+ with connection pooling
- **Email**: SMTP integration (SendGrid/AWS SES)
- **File Storage**: Local filesystem with cloud backup

### **Infrastructure**
- **Hosting**: Cloud-based deployment (AWS/GCP/Azure)
- **CDN**: CloudFlare for static assets
- **SSL**: Let's Encrypt certificates
- **Monitoring**: Custom health checks and analytics

---

## 🎨 **Design System**

### **Color Palette**
```css
/* Primary Colors */
--primary-dark: #364035;     /* Deep forest green */
--primary-light: #8B9A8C;    /* Sage green */
--accent-gold: #B8956A;      /* Warm gold */
--text-light: #F4F1E8;       /* Cream white */
--text-dark: #2D2A26;        /* Dark brown */

/* Gradients */
background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
```

### **Typography**
- **Primary Font**: Playfair Display (serif) - Headlines
- **Secondary Font**: Manrope (sans-serif) - Body text
- **Font Loading**: Optimized with `font-display: swap`

### **Responsive Design**
- **Mobile First**: Optimized for mobile devices
- **Breakpoints**: Tailwind CSS standard breakpoints
- **Safe Areas**: iOS notch and safe area support

---

## 📱 **Mobile Optimization**

### **iOS Safari Fixes**
```html
<!-- Viewport Configuration -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
<meta name="theme-color" content="#364035" />
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
```

```css
/* Safe Area Support */
html {
  padding-top: max(env(safe-area-inset-top), 0px);
  background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
}

/* Pull-to-refresh fix */
body::before {
  content: '';
  position: fixed;
  top: -100vh;
  background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
}
```

---

## 🔌 **API Specifications**

### **Endpoints**

#### **Health Check**
```
GET /api/health
Response: { success: boolean, data: { status: string, timestamp: string } }
```

#### **Email Signup**
```
POST /api/email-signup
Body: { email: string, source: string, timestamp: string }
Response: { success: boolean, message: string, data: object }
```

#### **Contact Form**
```
POST /api/contact
Body: { name: string, email: string, subject: string, message: string, type: string }
Response: { success: boolean, message: string, data: object }
```

#### **Professional Application**
```
POST /api/professional-application
Body: { firstName: string, lastName: string, email: string, ... }
Response: { success: boolean, message: string, data: object }
```

#### **Analytics**
```
POST /api/analytics
Body: { event: string, data: object, timestamp: string, url: string }
Response: { success: boolean, message: string, data: object }
```

### **Error Handling**
- **Validation Errors**: 400 status with detailed error messages
- **Server Errors**: 500 status with generic error message
- **Rate Limiting**: 429 status when limits exceeded
- **Fallback**: localStorage backup when API unavailable

---

## 🗄️ **Database Schema**

### **Email Signups**
```sql
CREATE TABLE email_signups (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    source VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    metadata JSONB
);
```

### **Contact Forms**
```sql
CREATE TABLE contact_forms (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);
```

### **Professional Applications**
```sql
CREATE TABLE professional_applications (
    id SERIAL PRIMARY KEY,
    application_id VARCHAR(100) UNIQUE NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    services JSONB NOT NULL,
    experience VARCHAR(50) NOT NULL,
    license_number VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending_review',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);
```

---

## 🔒 **Security Implementation**

### **Security Headers**
```javascript
// middleware.ts
const securityHeaders = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline';",
  'X-Frame-Options': 'SAMEORIGIN',
  'X-Content-Type-Options': 'nosniff',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-XSS-Protection': '1; mode=block'
};
```

### **Input Validation**
- **Email Validation**: RFC 5322 compliant regex
- **Phone Validation**: International format support
- **SQL Injection Prevention**: Parameterized queries
- **XSS Prevention**: Input sanitization and CSP headers

### **Rate Limiting**
- **API Endpoints**: 100 requests per 15 minutes per IP
- **Form Submissions**: 5 submissions per hour per IP
- **Health Checks**: Unlimited for monitoring

---

## 📊 **Analytics & Monitoring**

### **Performance Monitoring**
```javascript
// Core Web Vitals tracking
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    // Track LCP, FID, CLS
    trackMetric(entry.name, entry.value);
  }
});
observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
```

### **Custom Analytics**
- **Page Views**: Automatic tracking with pathname
- **User Interactions**: Form submissions, button clicks
- **Performance Metrics**: Load times, error rates
- **A/B Testing**: Multi-variant testing framework

### **Health Monitoring**
- **Endpoint Health**: Automated health checks every 30 seconds
- **Database Health**: Connection pool monitoring
- **Error Tracking**: Automatic error logging and alerting

---

## 🚀 **Performance Optimization**

### **Frontend Optimization**
- **Code Splitting**: Automatic with Next.js
- **Image Optimization**: Next.js Image component with WebP support
- **Font Loading**: Preload critical fonts with display: swap
- **CSS Optimization**: Tailwind CSS purging and minification

### **Backend Optimization**
- **Database Indexing**: Optimized indexes on frequently queried columns
- **Connection Pooling**: PostgreSQL connection pooling
- **Caching**: Redis for session and data caching
- **Compression**: Gzip/Brotli response compression

### **Performance Targets**
- **First Contentful Paint**: < 1.8 seconds
- **Largest Contentful Paint**: < 2.5 seconds
- **Cumulative Layout Shift**: < 0.1
- **Time to Interactive**: < 3 seconds

---

## 🔧 **Development Tools**

### **Testing Framework**
```javascript
// Built-in testing suite
const testResults = [
  { name: 'Backend Health Check', status: 'success' },
  { name: 'Email Signup API', status: 'success' },
  { name: 'Contact Form API', status: 'success' },
  { name: 'Professional Application API', status: 'success' },
  { name: 'Mobile Safari CSS', status: 'success' },
  { name: 'Analytics Tracking', status: 'success' }
];
```

### **Development Components**
- **Performance Dashboard**: Real-time metrics in development
- **Backend Status Indicator**: API connectivity monitoring
- **Testing Suite**: Automated functionality testing
- **A/B Testing Framework**: Multi-variant testing

---

## 📦 **Dependencies**

### **Core Dependencies**
```json
{
  "next": "15.2.4",
  "react": "^18.0.0",
  "tailwindcss": "^3.0.0",
  "typescript": "^5.0.0"
}
```

### **Database Dependencies**
```json
{
  "pg": "^8.0.0",
  "pg-pool": "^3.0.0"
}
```

### **Development Dependencies**
```json
{
  "@types/node": "^20.0.0",
  "@types/react": "^18.0.0",
  "eslint": "^8.0.0",
  "prettier": "^3.0.0"
}
```

---

## 🌐 **Browser Support**

### **Supported Browsers**
- **Chrome**: 90+ (Desktop & Mobile)
- **Safari**: 14+ (Desktop & Mobile)
- **Firefox**: 88+ (Desktop & Mobile)
- **Edge**: 90+ (Desktop)

### **Mobile Support**
- **iOS Safari**: 14+ (iPhone, iPad)
- **Chrome Mobile**: 90+ (Android)
- **Samsung Internet**: 14+ (Android)

---

## 📋 **Environment Configuration**

### **Required Environment Variables**
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/vierla

# Email
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your_api_key
FROM_EMAIL=<EMAIL>

# Security
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_FB_PIXEL_ID=000000000000000
```

---

## 🔄 **Deployment Process**

### **Build Process**
```bash
# Install dependencies
npm install

# Build for production
npm run build

# Start production server
npm start
```

### **Docker Deployment**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

---

## 📞 **Support Information**

### **Technical Contacts**
- **Primary Support**: <EMAIL>
- **Emergency**: Follow escalation procedures
- **Documentation**: This technical specification

### **Maintenance Schedule**
- **Updates**: Monthly security patches
- **Backups**: Daily automated backups
- **Monitoring**: 24/7 automated monitoring

---

**This document serves as the complete technical reference for the Vierla platform. For operational procedures, see the Operations Manual.**
