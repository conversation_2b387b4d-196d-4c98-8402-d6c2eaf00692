# Deployment Guide for Vierla Web App on Debian with <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Let’s Encrypt

This document outlines, step by step, how to deploy the Vierla web application alongside GitLab on a single Debian server using Docker Compose, Nginx as a reverse proxy, and Let’s Encrypt for TLS. It includes all changes made, debugging steps, and final configurations. You can commit this file into your GitLab repository as DEPLOYMENT.md.

## Table of Contents

- Overview
- Prerequisites
- Directory Layout
- Cloning the Application
- Docker Compose Configuration
- Dockerfile for Vierla Web App
- Building and Deploying the Container
- Healthcheck Setup and Debugging
- Nginx Reverse Proxy Configuration
- DNS Configuration
- Testing HTTP Connectivity
- Let’s Encrypt SSL Certificate Issuance
- Verifying HTTPS
- Common Issues and Resolutions
- Conclusion

## Overview

- We will host:
- GitLab: running in its own Docker container

Vierla Web App: a Next.js standalone build served from a Docker container

Nginx listens on ports 80/443, routes requests by hostname (SNI/Host header), and terminates T<PERSON> with Let’s Encrypt certificates.

## Prerequisites

Debian Server (e.g., Debian 12) with public IP

Docker & Docker Compose installed

Nginx installed on the host

Domain names:

gitlab.icecrown.ca pointing to your server

vierla.com (and www.vierla.com) pointing to your server

Ports 80 and 443 forwarded from your router to the Debian host

## Directory Layout

/opt/vierla-web-app/
├── services-app-web/
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── deploy.sh
│   └── … (app source, package.json, etc.)
└── nginx/
    └── conf.d/
        ├── gitlab.conf
        └── vierla.conf

## Cloning the Application

On your Debian server:

cd /opt
git clone https://gitlab.icecrown.ca/yourgroup/vierla-web-app.git vierla-web-app

Your app will reside under /opt/vierla-web-app/services-app-web.

## Docker Compose Configuration

File: /opt/vierla-web-app/services-app-web/docker-compose.yml

version: '3.8'
services:
  vierla-web:
    build:
      context: .
      dockerfile: Dockerfile
    image: services-app-web-vierla-web
    container_name: vierla-web
    restart: unless-stopped
    ports:
      - '127.0.0.1:3000:3000'
    networks:
      - web

networks:
  web:
    external: true

Binds port 3000 only on localhost.

Joins the external web network for Nginx proxying.

## Dockerfile for Vierla Web App

File: /opt/vierla-web-app/services-app-web/Dockerfile

# 1. Base image
FROM node:18-alpine AS base
WORKDIR /app

# 2. Dependencies stage
FROM base AS deps
RUN apk add --no-cache libc6-compat
COPY package.json package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm i --frozen-lockfile; \
  elif [ -f package-lock.json ]; then \
    npm ci --legacy-peer-deps; \
  else \
    echo "Lockfile not found." && exit 1; \
  fi

# 3. Builder stage
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
ENV NEXT_TELEMETRY_DISABLED=1
RUN \
  if [ -f pnpm-lock.yaml ]; then \
    corepack enable pnpm && pnpm run build; \
  elif [ -f package-lock.json ]; then \
    npm run build; \
  else \
    echo "Lockfile not found." && exit 1; \
  fi

# 4. Runner stage (production)
FROM node:18-alpine AS runner
WORKDIR /app

# Install curl for HEALTHCHECK
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup --system --gid 1001 nodejs \
 && adduser  --system --uid 1001 nextjs

# Copy static assets
COPY --from=builder /app/public ./public

# Copy standalone output including dependencies
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone/ ./

# Ensure .next directory exists with correct permissions
RUN mkdir -p .next && chown nextjs:nodejs .next

USER nextjs

# Environment for Next.js binding
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0

EXPOSE 3000

# Container healthcheck - use dedicated health endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the server
CMD ["node", "server.js"]

Building and Deploying the Container

cd /opt/vierla-web-app/services-app-web

# 1. Stop existing container
docker-compose down

# 2. Build anew without cache
docker-compose build --no-cache vierla-web

# 3. Start in detached mode
docker-compose up -d vierla-web

## Healthcheck Setup and Debugging

Verify curl is in the container

docker exec vierla-web which curl

Check container health

docker ps --filter name=vierla-web --format "table {{.Names}}\t{{.Status}}"

Wait ~30 seconds for it to become (healthy).

Manual test if still unhealthy

docker exec -it vierla-web sh -c "curl -f http://localhost:3000/api/health -v"

Inspect health logs

docker inspect vierla-web \
  --format='{{json .State.Health.Log}}' | jq .

## Nginx Reverse Proxy Configuration

Place these in /etc/nginx/conf.d/.

GitLab vhost (gitlab.conf)

server {
    listen 80;
    server_name gitlab.icecrown.ca;
    location / {
        proxy_pass http://127.0.0.1:8080;  # GitLab container’s HTTP port
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

## Vierla vhost (vierla.conf)

# 1. Redirect HTTP → HTTPS
server {
    listen 80;
    server_name vierla.com www.vierla.com;
    return 301 https://$host$request_uri;
}

# 2. HTTPS server block
server {
    listen 443 ssl http2;
    server_name vierla.com www.vierla.com;

    ssl_certificate     /etc/letsencrypt/live/vierla.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/vierla.com/privkey.pem;
    include             /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam         /etc/letsencrypt/ssl-dhparams.pem;

    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    access_log  /var/log/nginx/vierla.access.log  combined;
    error_log   /var/log/nginx/vierla.error.log  warn;

    location / {
        proxy_pass         http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_set_header   Upgrade $http_upgrade;
        proxy_set_header   Connection "upgrade";
    }

    location /stats {
        alias /var/www/goaccess/html;
        index index.html;
    }
}

After editing, run:

sudo nginx -t
sudo systemctl reload nginx

## DNS Configuration

On your DNS provider, set A records:

vierla.com → your.server.ip

www.vierla.com → your.server.ip

For GitLab:

gitlab.icecrown.ca → your.server.ip

## Testing HTTP Connectivity

From the server:

curl -I http://127.0.0.1:3000
curl -I http://127.0.0.1 -H "Host: vierla.com"

From an external machine:

curl -I http://vierla.com

Expect 200 OK or 301 → https://vierla.com if redirect is in place.

## Let’s Encrypt SSL Certificate Issuance

Install Certbot:

sudo apt update
sudo apt install certbot python3-certbot-nginx

Obtain and configure certificates:

sudo certbot --nginx \
  -d vierla.com \
  -d www.vierla.com

Certbot will:

Validate domain via HTTP challenge

Obtain certificates under /etc/letsencrypt/live/vierla.com/

Inject SSL config into vierla.conf

## Verifying HTTPS

Local test

curl -I https://127.0.0.1 -H "Host: vierla.com"

Should return HTTP/2 200 with valid certificate.

External test

curl -I https://vierla.com

Browser should show secure lock icon and no warnings.

## Common Issues and Resolutions

Container remains “unhealthy”

Ensure curl is installed in the runner image.

Confirm the app binds to 0.0.0.0:3000, not a Docker-assigned IP.

Adjust HEALTHCHECK to target http://0.0.0.0:3000/.

504 Gateway Time-out

Verify container is healthy and listening:

curl -I http://127.0.0.1:3000

Check Nginx proxy settings and that proxy_pass matches your bind address.

ERR_CERT_COMMON_NAME_INVALID

Ensure server_name matches the certificate’s domains.

Run sudo certbot install --cert-name vierla.com --nginx after adding www alias to server_name.

Certbot “no matching server block”

Add www.vierla.com to server_name in both HTTP and HTTPS blocks.

Reload Nginx and re-run Certbot’s install command.

## Conclusion

You now have:

A Next.js–based Vierla web app running in Docker with a proper healthcheck.

Nginx reverse proxy routing traffic by hostname.

Automatic Let’s Encrypt SSL certificates for vierla.com and www.vierla.com.

A documented, repeatable process ready for inclusion in your GitLab repository.

Feel free to adapt paths, ports, and domain names to your environment.