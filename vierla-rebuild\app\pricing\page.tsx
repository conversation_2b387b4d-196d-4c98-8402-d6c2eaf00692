import { <PERSON><PERSON> } from "@/components/ui/button";
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { Badge } from "@/components/ui/badge";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import ShinyButton from "@/components/ui/shiny-button";
import { Check, Star } from "lucide-react";
import Link from "next/link";

export default function Pricing() {
  const plans = [
    {
      name: "Individual",
      description: "Perfect for solo beauty professionals",
      price: "Free",
      features: [
        "1 Service Listing",
        "Basic Website Builder",
        "5 Bookings/month",
        "Email Support",
        "Basic Templates",
        "Payment Processing"
      ],
      popular: false,
      cta: "Get Started Free"
    },
    {
      name: "Medium Business",
      description: "Most popular for growing beauty businesses",
      price: "$29/month",
      features: [
        "Unlimited Service Listings",
        "Full Website Builder",
        "Unlimited Bookings",
        "Integrated CRM",
        "Advanced Analytics",
        "Priority Support",
        "Custom Domain",
        "Premium Templates",
        "Team Management (up to 5)"
      ],
      popular: true,
      cta: "Start Pro Trial"
    },
    {
      name: "Large Business",
      description: "For established beauty enterprises",
      price: "$79/month",
      features: [
        "Everything in Medium Business",
        "Unlimited Team Members",
        "White-label Options",
        "API Access",
        "Dedicated Account Manager",
        "Custom Integrations",
        "Advanced Automation",
        "Multi-location Support"
      ],
      popular: false,
      cta: "Contact Sales"
    }
  ];

  return (
    <div className="page-pricing min-h-screen relative overflow-hidden">
      <AuroraBackgroundLayer />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-notable">
            SIMPLE, TRANSPARENT PRICING
          </h1>
          <p className="text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-sans">
            Choose the plan that fits your business needs. Start free and scale as you grow.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="grid gap-6 lg:grid-cols-3 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <div key={index} className="relative h-full">
                {/* Card container with hover group */}
                <div className="relative h-full group/card">
                  {/* Golden glow effect only on card hover, excluding button area */}
                  <div className="absolute -inset-0.5 rounded-lg opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 blur-sm" style={{ background: 'linear-gradient(to right, var(--golden-glow-color, #B8956A)/30%, var(--golden-glow-color, #B8956A)/60%, var(--golden-glow-color, #B8956A)/30%)' }} />

                  <div className={`relative h-full bg-light-charcoal backdrop-blur-md shadow-xl rounded-2xl overflow-hidden ${plan.popular ? 'border-2 border-muted-gold' : 'border border-sage/30'}`}>
                    <div className="relative h-full flex flex-col p-6">
                      {plan.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-muted-gold text-dark-charcoal font-sans">Most Popular</Badge>
                        </div>
                      )}
                      <div className="text-center pb-8">
                        <h3 className="text-2xl font-bold text-light-off-white drop-shadow-lg mb-2 font-tai-heritage">{plan.name}</h3>
                        <p className="text-warm-beige drop-shadow-sm mb-4 font-sans">{plan.description}</p>
                        <div className="mt-4">
                          <span className="text-4xl font-black text-muted-gold drop-shadow-lg font-tai-heritage">{plan.price}</span>
                        </div>
                      </div>
                      <div className="flex-grow flex flex-col">
                        <ul className="space-y-3 mb-8 flex-grow">
                          {plan.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-warm-beige drop-shadow-sm font-sans">
                              <Check className="h-4 w-4 text-sage mr-3 flex-shrink-0 drop-shadow-sm" />
                              <span className="text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                        {/* Button area with separate hover group */}
                        <div className="mt-auto flex justify-center group/button">
                          <Link href={plan.name === "Large Business" ? "/contact" : "/apply"} className="w-full">
                            <ShinyButton
                              size="sm"
                              className="w-full max-w-[180px] mx-auto bg-primary/20 border-white/40 hover:bg-primary/30 drop-shadow-sm text-sm whitespace-nowrap"
                            >
                              {plan.cta}
                            </ShinyButton>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
            <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-light-off-white drop-shadow-lg font-tai-heritage">
              Ready to get started?
            </h2>
            <p className="max-w-[42rem] leading-normal text-warm-beige sm:text-xl sm:leading-8 drop-shadow-sm font-sans">
              Join thousands of entrepreneurs who trust Vierla to power their business.
            </p>
            <div className="space-x-4">
              <ShinyButton asChild>
                <a href="/apply">Start Free Trial</a>
              </ShinyButton>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
