'use client'

import Link from "next/link"
import { HeartIcon } from "@/components/ui/heart-icon"

interface FooterProps {
  variant?: 'home' | 'simple'
}

export function Footer({ variant = 'simple' }: FooterProps) {
  if (variant === 'home') {
    return (
      <footer className="relative z-10 text-white py-16 border-t" style={{backgroundColor: 'rgba(54, 64, 53, 0.9)', borderColor: '#F4F1E8'}}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 rounded-2xl flex items-center justify-center border-2" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', borderColor: '#F4F1E8'}}>
                  <HeartIcon className="w-6 h-6 drop-shadow-sm" style={{color: '#F4F1E8'}} />
                </div>
                <span className="text-3xl font-bold drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
              </div>
              <p className="text-white/80 drop-shadow-sm leading-relaxed">
                Self-Care, Simplified. Connecting you with top beauty professionals in Toronto & Ottawa.
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-6 text-xl text-white drop-shadow-sm" style={{fontFamily: 'Playfair Display, serif'}}>Company</h4>
              <ul className="space-y-3 text-white/80">
                <li>
                  <Link href="/about" className="transition-colors" style={{color: '#F5F0E1'}} onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#B8956A'} onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#F5F0E1'}>
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="transition-colors" style={{color: '#F4F1E8'}} onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#B8956A'} onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#F4F1E8'}>
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="transition-colors" style={{color: '#F4F1E8'}} onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#B8956A'} onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#F4F1E8'}>
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="transition-colors" style={{color: '#F4F1E8'}} onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#B8956A'} onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#F4F1E8'}>
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-8 text-center" style={{borderTop: '1px solid #F5F0E1'}}>
            <p className="text-white/80 drop-shadow-sm">
              &copy; 2025 Vierla - Your Self-Care, Simplified. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    )
  }

  // Simple footer for all other pages
  return (
    <footer className="relative z-10 bg-black/20 backdrop-blur-md text-white py-16 border-t border-white/20">
      <div className="container mx-auto px-4">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center border border-white/30">
              <HeartIcon className="w-6 h-6 text-white drop-shadow-sm" />
            </div>
            <span className="text-2xl font-bold text-white drop-shadow-lg" style={{fontFamily: 'Playfair Display, serif'}}>Vierla</span>
          </div>
          <p className="text-white/80 drop-shadow-sm">
            &copy; 2025 Vierla - Your Self-Care, Simplified. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
