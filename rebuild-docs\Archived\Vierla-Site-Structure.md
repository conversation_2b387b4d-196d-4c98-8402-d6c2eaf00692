

# **Vierla.com: Site Structure & Content Guide**

This document outlines the information architecture (IA), navigation, and content for each page of the rebuilt Vierla website.

---

### **1\. Global Navigation Structure**

The main navigation bar will be present on all pages and contain the following links:

* **Features** \-\> /features  
* **Pricing** \-\> /pricing (Note: To be built in a future iteration)  
* **About** \-\> /about (Note: To be built in a future iteration)  
* **Login** (Button, secondary style) \-\> /login  
* **Get Started** (Button, primary style using <PERSON><PERSON>) \-\> /apply

The footer will contain an expanded sitemap:

* **Product:** Features, Pricing, Updates, API  
* **Company:** About Us, Blog, Careers, Contact  
* **Legal:** Privacy Policy, Terms of Service

---

### **2\. Page Content: Homepage (/)**

**Objective:** Immediately convey the "AI-Powered All-in-One Platform" value proposition, build trust, and guide users to explore features or sign up.

* **Hero Section:**  
  * **Headline:** The AI-Powered Platform for Modern Business.  
  * **Sub-headline:** Stop juggling tools. Vierla integrates your website, invoicing, CRM, and analytics into one intelligent dashboard.  
  * **Primary CTA:** Shiny Button with text "Get Started Free".  
* **Social Proof (Logos):**  
  * (Placeholder for future client logos) Section Title: "Trusted by forward-thinking companies worldwide"  
* **Features Overview (Bento Grid):**  
  * A grid of 4 main cards, each with a Glowing Effect.  
  * **Card 1 (Website Builder):**  
    * Icon: LayoutTemplate from Lucide React  
    * Title: AI Website Builder  
    * Description: Launch a stunning, professional website in minutes. No code required.  
  * **Card 2 (Invoicing):**  
    * Icon: FileText from Lucide React  
    * Title: Smart Invoicing  
    * Description: Create and send professional invoices that get you paid faster.  
  * **Card 3 (CRM):**  
    * Icon: Users from Lucide React  
    * Title: Integrated CRM  
    * Description: Manage customer relationships and track your sales pipeline effortlessly.  
  * **Card 4 (Analytics):**  
    * Icon: BarChart2 from Lucide React  
    * Title: Actionable Analytics  
    * Description: Understand your business performance with easy-to-read dashboards.  
* **Final CTA Section:**  
  * **Headline:** Ready to simplify your business?  
  * **Description:** Join thousands of entrepreneurs building their future on Vierla.  
  * **Primary CTA:** Shiny Button with text "Sign Up Now".

---

### **3\. Page Content: Features (/features)**

**Objective:** Provide a more detailed look at each tool in the Vierla suite.

* **Structure:** A page with a main heading "A Closer Look at the Vierla Suite" and a Tabs component.  
* **Tabs:**  
  * Tab 1: **AI Website Builder**  
    * Content: Detail the features like drag-and-drop editor, responsive templates, AI content generation, and custom domains. Use a combination of text and placeholder images/mockups.  
  * Tab 2: **Smart Invoicing**  
    * Content: Detail features like recurring invoices, payment reminders, multiple currency support, and expense tracking.  
  * Tab 3: **Integrated CRM**  
    * Content: Detail features like contact management, deal tracking, sales funnels, and task automation.  
  * Tab 4: **Actionable Analytics**  
    * Content: Detail features like real-time dashboards, traffic sources, conversion tracking, and custom reports.

---

### **4\. Page Content: Apply (/apply)**

**Objective:** Capture new user sign-ups through a frictionless, multi-step form.

* **Structure:** A centered, clean page with the Multistep Form component.  
* **Form Steps:**  
  * **Step 1: Account Details**  
    * Fields: Full Name, Email Address, Password.  
  * **Step 2: Company Information**  
    * Fields: Company Name, Company Size (Select dropdown), Industry (Select dropdown).  
  * **Step 3: Finalize**  
    * Content: "Agree to Terms and Conditions" checkbox.  
    * Submit Button: "Create My Account".