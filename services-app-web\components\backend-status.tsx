"use client"
import { useState, useEffect } from 'react'
import { getBackendStatus, exportLocalData, clearLocalData } from '@/lib/api'

interface BackendStatus {
  available: boolean
  version?: string
  uptime?: number
  lastChecked?: number
}

export function BackendStatusIndicator() {
  const [status, setStatus] = useState<BackendStatus>({ available: false })
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return

    const checkStatus = async () => {
      const backendStatus = await getBackendStatus()
      setStatus({
        ...backendStatus,
        lastChecked: Date.now()
      })
    }

    // Check immediately
    checkStatus()

    // Check every 30 seconds
    const interval = setInterval(checkStatus, 30000)

    return () => clearInterval(interval)
  }, [])

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const getLocalDataCounts = () => {
    const data = exportLocalData()
    return {
      emailSignups: data.emailSignups.length,
      contacts: data.contacts.length,
      applications: data.applications.length,
      events: data.events.length,
      pageViews: data.pageViews.length
    }
  }

  const localCounts = getLocalDataCounts()
  const hasLocalData = Object.values(localCounts).some(count => count > 0)

  return (
    <>
      {/* Status Indicator */}
      <div
        className="fixed top-4 right-4 z-50 cursor-pointer"
        onClick={() => setIsVisible(!isVisible)}
      >
        <div className={`w-4 h-4 rounded-full ${status.available ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
      </div>

      {/* Status Panel */}
      {isVisible && (
        <div className="fixed top-12 right-4 bg-black/90 text-white p-4 rounded-lg text-xs max-w-sm z-50">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-bold">Backend Status</h4>
            <button
              onClick={() => setIsVisible(false)}
              className="text-white/60 hover:text-white"
            >
              ✕
            </button>
          </div>

          {/* Connection Status */}
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <div className={`w-2 h-2 rounded-full ${status.available ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="font-semibold">
                {status.available ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            {status.available && (
              <div className="space-y-1 text-white/80">
                {status.version && (
                  <div>Version: {status.version}</div>
                )}
                {status.uptime && (
                  <div>Uptime: {Math.round(status.uptime / 1000)}s</div>
                )}
              </div>
            )}
            
            {status.lastChecked && (
              <div className="text-white/60 text-xs mt-1">
                Last checked: {new Date(status.lastChecked).toLocaleTimeString()}
              </div>
            )}
          </div>

          {/* Local Data Status */}
          <div className="mb-4">
            <h5 className="font-semibold mb-2">Local Data</h5>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Email Signups:</span>
                <span className={localCounts.emailSignups > 0 ? 'text-yellow-400' : 'text-white/60'}>
                  {localCounts.emailSignups}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Contacts:</span>
                <span className={localCounts.contacts > 0 ? 'text-yellow-400' : 'text-white/60'}>
                  {localCounts.contacts}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Applications:</span>
                <span className={localCounts.applications > 0 ? 'text-yellow-400' : 'text-white/60'}>
                  {localCounts.applications}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Events:</span>
                <span className={localCounts.events > 0 ? 'text-yellow-400' : 'text-white/60'}>
                  {localCounts.events}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Page Views:</span>
                <span className={localCounts.pageViews > 0 ? 'text-yellow-400' : 'text-white/60'}>
                  {localCounts.pageViews}
                </span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="space-y-2">
            {hasLocalData && (
              <div className="bg-yellow-900/30 border border-yellow-600/30 rounded p-2">
                <div className="text-yellow-400 text-xs mb-2">
                  ⚠️ Local data detected. This data will be lost if not synced to backend.
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      const data = exportLocalData()
                      navigator.clipboard?.writeText(JSON.stringify(data, null, 2))
                      console.log('Local data exported:', data)
                    }}
                    className="px-2 py-1 bg-yellow-600 rounded text-xs"
                  >
                    Export
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('Clear all local data? This cannot be undone.')) {
                        clearLocalData()
                        window.location.reload()
                      }
                    }}
                    className="px-2 py-1 bg-red-600 rounded text-xs"
                  >
                    Clear
                  </button>
                </div>
              </div>
            )}

            <div className="text-white/60 text-xs">
              {status.available 
                ? 'All form submissions are being sent to the backend.'
                : 'Forms are saving to localStorage as fallback.'}
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Backend health check hook
export function useBackendHealth() {
  const [isHealthy, setIsHealthy] = useState(false)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkHealth = async () => {
      setIsChecking(true)
      const status = await getBackendStatus()
      setIsHealthy(status.available)
      setIsChecking(false)
    }

    checkHealth()
    const interval = setInterval(checkHealth, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [])

  return { isHealthy, isChecking }
}
