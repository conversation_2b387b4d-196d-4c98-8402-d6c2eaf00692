"use client"
import { useState, useEffect } from 'react'
import { trackEvent } from '@/components/analytics'

// A/B Test configuration
interface ABTestConfig {
  testName: string
  variants: string[]
  weights?: number[]
  enabled: boolean
}

const AB_TESTS: Record<string, ABTestConfig> = {
  hero_cta: {
    testName: 'Hero CTA Button',
    variants: ['join_waitlist', 'get_early_access', 'notify_me'],
    weights: [0.33, 0.33, 0.34],
    enabled: true
  },
  professional_cta: {
    testName: 'Professional CTA',
    variants: ['learn_more', 'apply_now', 'join_platform'],
    weights: [0.33, 0.33, 0.34],
    enabled: true
  },
  email_form_copy: {
    testName: 'Email Form Copy',
    variants: ['waitlist', 'early_access', 'launch_notification'],
    weights: [0.33, 0.33, 0.34],
    enabled: true
  }
}

// Hook for A/B testing
export function useABTest(testName: string): string {
  const [variant, setVariant] = useState<string>('')

  useEffect(() => {
    const test = AB_TESTS[testName]
    if (!test || !test.enabled) {
      setVariant(test?.variants[0] || 'default')
      return
    }

    // Check if user already has a variant assigned
    const storageKey = `ab_test_${testName}`
    const existingVariant = localStorage.getItem(storageKey)
    
    if (existingVariant && test.variants.includes(existingVariant)) {
      setVariant(existingVariant)
      return
    }

    // Assign new variant based on weights
    const weights = test.weights || test.variants.map(() => 1 / test.variants.length)
    const random = Math.random()
    let cumulativeWeight = 0
    
    for (let i = 0; i < test.variants.length; i++) {
      cumulativeWeight += weights[i]
      if (random <= cumulativeWeight) {
        const selectedVariant = test.variants[i]
        setVariant(selectedVariant)
        localStorage.setItem(storageKey, selectedVariant)
        
        // Track variant assignment
        trackEvent('ab_test_assigned', {
          test_name: testName,
          variant: selectedVariant,
          event_category: 'ab_testing'
        })
        
        break
      }
    }
  }, [testName])

  return variant
}

// Hero CTA variants
export function HeroCTAButton() {
  const variant = useABTest('hero_cta')

  const handleClick = () => {
    trackEvent('hero_cta_click', {
      variant,
      event_category: 'ab_testing'
    })
  }

  const getButtonText = () => {
    switch (variant) {
      case 'join_waitlist':
        return 'Join Professional Waitlist'
      case 'get_early_access':
        return 'Get Early Professional Access'
      case 'notify_me':
        return 'Notify Me at Launch'
      default:
        return 'Join Professional Waitlist'
    }
  }

  const getButtonSubtext = () => {
    switch (variant) {
      case 'join_waitlist':
        return 'Be first to join as a professional'
      case 'get_early_access':
        return 'Exclusive professional preview access'
      case 'notify_me':
        return 'Get professional launch notification'
      default:
        return 'Be first to join as a professional'
    }
  }

  return (
    <div className="text-center">
      <button
        onClick={handleClick}
        className="px-8 py-4 rounded-full font-medium transition-all duration-300 hover:scale-105 mb-2"
        style={{backgroundColor: '#B8956A', color: '#2D2A26'}}
      >
        {getButtonText()}
      </button>
      <p className="text-white/70 text-sm">
        {getButtonSubtext()}
      </p>
    </div>
  )
}

// Professional CTA variants
export function ProfessionalCTAButton() {
  const variant = useABTest('professional_cta')

  const handleClick = () => {
    trackEvent('professional_cta_click', {
      variant,
      event_category: 'ab_testing'
    })
  }

  const getButtonText = () => {
    switch (variant) {
      case 'learn_more':
        return 'Learn About Joining Our Platform'
      case 'apply_now':
        return 'Apply to Join Vierla'
      case 'join_platform':
        return 'Join Our Professional Network'
      default:
        return 'Learn About Joining Our Platform'
    }
  }

  const getButtonIcon = () => {
    switch (variant) {
      case 'apply_now':
        return (
          <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
      case 'join_platform':
        return (
          <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        )
      default:
        return (
          <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        )
    }
  }

  return (
    <button
      onClick={handleClick}
      className="inline-flex items-center px-6 py-3 rounded-full border-2 text-white hover:bg-white/10 transition-all duration-300"
      style={{borderColor: '#F4F1E8', color: '#F4F1E8'}}
    >
      {getButtonText()}
      {getButtonIcon()}
    </button>
  )
}

// Email form copy variants
export function EmailFormCopy() {
  const variant = useABTest('email_form_copy')

  const getCopy = () => {
    switch (variant) {
      case 'waitlist':
        return {
          title: 'Join Our Waitlist',
          subtitle: 'Be the first to know when Vierla launches in your area. Get exclusive early access and special offers.',
          placeholder: 'Enter your email address',
          button: 'Join Waitlist'
        }
      case 'early_access':
        return {
          title: 'Get Early Access',
          subtitle: 'Secure your spot for exclusive early access to Vierla. Limited spots available for beta testing.',
          placeholder: 'Your email for early access',
          button: 'Get Early Access'
        }
      case 'launch_notification':
        return {
          title: 'Get Launch Notification',
          subtitle: 'Stay updated on our launch progress and be notified the moment we go live in your city.',
          placeholder: 'Email for launch updates',
          button: 'Notify Me'
        }
      default:
        return {
          title: 'Join Our Waitlist',
          subtitle: 'Be the first to know when Vierla launches in your area. Get exclusive early access and special offers.',
          placeholder: 'Enter your email address',
          button: 'Join Waitlist'
        }
    }
  }

  return getCopy()
}

// A/B Test results component (for admin/debugging)
export function ABTestResults() {
  const [results, setResults] = useState<any>(null)

  useEffect(() => {
    // Get A/B test data from analytics
    const events = JSON.parse(localStorage.getItem('vierla-events') || '[]')
    const abTestEvents = events.filter((event: any) => 
      event.name === 'ab_test_assigned' || 
      event.name.includes('_cta_click')
    )

    // Process results
    const testResults: Record<string, any> = {}
    
    abTestEvents.forEach((event: any) => {
      if (event.name === 'ab_test_assigned') {
        const testName = event.parameters?.test_name
        const variant = event.parameters?.variant
        
        if (!testResults[testName]) {
          testResults[testName] = { assignments: {}, clicks: {} }
        }
        
        testResults[testName].assignments[variant] = 
          (testResults[testName].assignments[variant] || 0) + 1
      } else if (event.name.includes('_cta_click')) {
        const variant = event.parameters?.variant
        const testName = event.name.replace('_cta_click', '_cta')
        
        if (!testResults[testName]) {
          testResults[testName] = { assignments: {}, clicks: {} }
        }
        
        testResults[testName].clicks[variant] = 
          (testResults[testName].clicks[variant] || 0) + 1
      }
    })

    setResults(testResults)
  }, [])

  if (!results || process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm">
      <h4 className="font-bold mb-2">A/B Test Results (Dev Only)</h4>
      {Object.entries(results).map(([testName, data]: [string, any]) => (
        <div key={testName} className="mb-2">
          <div className="font-semibold">{testName}</div>
          {Object.entries(data.assignments || {}).map(([variant, count]: [string, any]) => {
            const clicks = data.clicks?.[variant] || 0
            const conversionRate = count > 0 ? ((clicks / count) * 100).toFixed(1) : '0'
            return (
              <div key={variant} className="text-xs">
                {variant}: {count} views, {clicks} clicks ({conversionRate}%)
              </div>
            )
          })}
        </div>
      ))}
    </div>
  )
}
