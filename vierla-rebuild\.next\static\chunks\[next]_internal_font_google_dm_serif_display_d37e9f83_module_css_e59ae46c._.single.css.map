{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/dm_serif_display_d37e9f83.module.css"], "sourcesContent": ["/* latin-ext */\n@font-face {\n  font-family: 'DM Serif Display';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dmserifdisplay/v16/-nFnOHM81r4j6k0gjAW3mujVU2B2G_5x0vrx52jJ3Q.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'DM Serif Display';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dmserifdisplay/v16/-nFnOHM81r4j6k0gjAW3mujVU2B2G_Bx0vrx52g.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'DM Serif Display Fallback';\n    src: local(\"Times New Roman\");\n    ascent-override: 94.37%;\ndescent-override: 30.51%;\nline-gap-override: 0.00%;\nsize-adjust: 109.78%;\n\n}\n.className {\n    font-family: 'DM Serif Display', 'DM Serif Display Fallback';\n    font-weight: 400;\nfont-style: normal;\n\n}\n.variable {\n    --font-dm-serif-display: 'DM Serif Display', 'DM Serif Display Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA", "ignoreList": [0]}}]}