{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// A full-page wrapper component for the aurora background\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <main\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center bg-[var(--aurora-bg)] dark:bg-[var(--aurora-bg-dark)] text-slate-950 transition-bg\",\n        className\n      )}\n      {...props}\n    >\n      <AuroraBackgroundLayer showRadialGradient={showRadialGradient} />\n      {children}\n    </main>\n  );\n};\n\n// A memoized background-only layer for embedding in existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div\n        className={cn(\n          \"fixed inset-0 -z-10 overflow-hidden\",\n          className\n        )}\n      >\n        <div\n          className={cn(\n            `aurora-background\n            [--white-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-light)_0%,var(--aurora-stripe-light)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-light)_16%)]\n            [--dark-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-dark)_0%,var(--aurora-stripe-dark)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-dark)_16%)]\n            [--aurora:repeating-linear-gradient(100deg,var(--aurora-flow-1)_10%,var(--aurora-flow-2)_15%,var(--aurora-flow-3)_20%,var(--aurora-flow-4)_25%,var(--aurora-flow-5)_30%)]\n            [background-image:var(--white-gradient),var(--aurora)]\n            dark:[background-image:var(--dark-gradient),var(--aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--white-gradient),var(--aurora)]\n            after:dark:[background-image:var(--dark-gradient),var(--aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform\n            transform-gpu`,\n            showRadialGradient &&\n              `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB,CAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mJACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAsB,oBAAoB;;;;;;YAC1C;;;;;;;AAGP;AAGO,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACtC,CAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uCACA;kBAGF,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;;yBAeY,CAAC,EACd,sBACE,CAAC,0EAA0E,CAAC;;;;;;;;;;;AAKxF;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/validation.ts"], "sourcesContent": ["export interface ValidationRule {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  pattern?: RegExp;\n  custom?: (value: any) => string | null;\n}\n\nexport interface ValidationSchema {\n  [key: string]: ValidationRule;\n}\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: { [key: string]: string };\n}\n\nexport function validateField(value: any, rules: ValidationRule): string | null {\n  // Required validation\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\n    return 'This field is required';\n  }\n\n  // Skip other validations if field is empty and not required\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\n    return null;\n  }\n\n  // String-specific validations\n  if (typeof value === 'string') {\n    // Min length validation\n    if (rules.minLength && value.length < rules.minLength) {\n      return `Must be at least ${rules.minLength} characters`;\n    }\n\n    // Max length validation\n    if (rules.maxLength && value.length > rules.maxLength) {\n      return `Must be no more than ${rules.maxLength} characters`;\n    }\n\n    // Pattern validation\n    if (rules.pattern && !rules.pattern.test(value)) {\n      return 'Invalid format';\n    }\n  }\n\n  // Custom validation\n  if (rules.custom) {\n    return rules.custom(value);\n  }\n\n  return null;\n}\n\nexport function validateForm(data: { [key: string]: any }, schema: ValidationSchema): ValidationResult {\n  const errors: { [key: string]: string } = {};\n\n  for (const [field, rules] of Object.entries(schema)) {\n    const error = validateField(data[field], rules);\n    if (error) {\n      errors[field] = error;\n    }\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n}\n\n// Common validation patterns\nexport const patterns = {\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  phone: /^[\\+]?[1-9][\\d]{0,15}$/,\n  url: /^https?:\\/\\/.+/,\n  alphanumeric: /^[a-zA-Z0-9]+$/,\n  alphabetic: /^[a-zA-Z\\s]+$/,\n  numeric: /^\\d+$/,\n  postalCode: /^[A-Za-z]\\d[A-Za-z][ -]?\\d[A-Za-z]\\d$/,\n  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/\n};\n\n// Common validation schemas\nexport const contactFormSchema: ValidationSchema = {\n  name: {\n    required: true,\n    minLength: 2,\n    maxLength: 100,\n    pattern: patterns.alphabetic\n  },\n  email: {\n    required: true,\n    pattern: patterns.email,\n    maxLength: 255\n  },\n  subject: {\n    required: true,\n    minLength: 5,\n    maxLength: 200\n  },\n  message: {\n    required: true,\n    minLength: 10,\n    maxLength: 2000\n  },\n  type: {\n    required: true,\n    custom: (value) => {\n      const validTypes = ['general', 'customer', 'professional', 'partnership', 'support'];\n      return validTypes.includes(value) ? null : 'Invalid contact type';\n    }\n  }\n};\n\nexport const applyFormSchema: ValidationSchema = {\n  businessName: {\n    required: true,\n    minLength: 2,\n    maxLength: 100\n  },\n  ownerName: {\n    required: true,\n    minLength: 2,\n    maxLength: 100,\n    pattern: patterns.alphabetic\n  },\n  email: {\n    required: true,\n    pattern: patterns.email,\n    maxLength: 255\n  },\n  phone: {\n    required: true,\n    pattern: patterns.phone\n  },\n  businessType: {\n    required: true,\n    custom: (value) => {\n      const validTypes = ['salon', 'spa', 'barbershop', 'beauty_clinic', 'wellness_center', 'other'];\n      return validTypes.includes(value) ? null : 'Invalid business type';\n    }\n  },\n  location: {\n    required: true,\n    minLength: 5,\n    maxLength: 200\n  },\n  experience: {\n    required: true,\n    custom: (value) => {\n      const validExperience = ['0-1', '1-3', '3-5', '5-10', '10+'];\n      return validExperience.includes(value) ? null : 'Invalid experience level';\n    }\n  },\n  services: {\n    required: true,\n    custom: (value) => {\n      if (!Array.isArray(value) || value.length === 0) {\n        return 'Please select at least one service';\n      }\n      return null;\n    }\n  },\n  description: {\n    required: true,\n    minLength: 50,\n    maxLength: 1000\n  }\n};\n\n// Utility function to sanitize input\nexport function sanitizeInput(input: string): string {\n  return input\n    .trim()\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, ''); // Remove event handlers\n}\n\n// Rate limiting utility\nexport class RateLimiter {\n  private attempts: Map<string, number[]> = new Map();\n\n  isAllowed(identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean {\n    const now = Date.now();\n    const windowStart = now - windowMs;\n    \n    // Get existing attempts for this identifier\n    const existingAttempts = this.attempts.get(identifier) || [];\n    \n    // Filter out attempts outside the window\n    const recentAttempts = existingAttempts.filter(time => time > windowStart);\n    \n    // Check if under the limit\n    if (recentAttempts.length >= maxAttempts) {\n      return false;\n    }\n    \n    // Add current attempt\n    recentAttempts.push(now);\n    this.attempts.set(identifier, recentAttempts);\n    \n    return true;\n  }\n\n  getRemainingTime(identifier: string, windowMs: number = 15 * 60 * 1000): number {\n    const attempts = this.attempts.get(identifier) || [];\n    if (attempts.length === 0) return 0;\n    \n    const oldestAttempt = Math.min(...attempts);\n    const windowEnd = oldestAttempt + windowMs;\n    const now = Date.now();\n    \n    return Math.max(0, windowEnd - now);\n  }\n}\n\nexport const rateLimiter = new RateLimiter();\n"], "names": [], "mappings": ";;;;;;;;;;AAiBO,SAAS,cAAc,KAAU,EAAE,KAAqB;IAC7D,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,8BAA8B;IAC9B,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,SAAS,aAAa,IAA4B,EAAE,MAAwB;IACjF,MAAM,SAAoC,CAAC;IAE3C,KAAK,MAAM,CAAC,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACnD,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE;QACzC,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,gBAAgB;AAClB;AAGO,MAAM,oBAAsC;IACjD,MAAM;QACJ,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS,SAAS,UAAU;IAC9B;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,WAAW;IACb;IACA,SAAS;QACP,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA,SAAS;QACP,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA,MAAM;QACJ,UAAU;QACV,QAAQ,CAAC;YACP,MAAM,aAAa;gBAAC;gBAAW;gBAAY;gBAAgB;gBAAe;aAAU;YACpF,OAAO,WAAW,QAAQ,CAAC,SAAS,OAAO;QAC7C;IACF;AACF;AAEO,MAAM,kBAAoC;IAC/C,cAAc;QACZ,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA,WAAW;QACT,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS,SAAS,UAAU;IAC9B;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,WAAW;IACb;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;IACzB;IACA,cAAc;QACZ,UAAU;QACV,QAAQ,CAAC;YACP,MAAM,aAAa;gBAAC;gBAAS;gBAAO;gBAAc;gBAAiB;gBAAmB;aAAQ;YAC9F,OAAO,WAAW,QAAQ,CAAC,SAAS,OAAO;QAC7C;IACF;IACA,UAAU;QACR,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA,YAAY;QACV,UAAU;QACV,QAAQ,CAAC;YACP,MAAM,kBAAkB;gBAAC;gBAAO;gBAAO;gBAAO;gBAAQ;aAAM;YAC5D,OAAO,gBAAgB,QAAQ,CAAC,SAAS,OAAO;QAClD;IACF;IACA,UAAU;QACR,UAAU;QACV,QAAQ,CAAC;YACP,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;gBAC/C,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,aAAa;QACX,UAAU;QACV,WAAW;QACX,WAAW;IACb;AACF;AAGO,SAAS,cAAc,KAAa;IACzC,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;KAClD,OAAO,CAAC,iBAAiB,IAAI,8BAA8B;KAC3D,OAAO,CAAC,YAAY,KAAK,wBAAwB;AACtD;AAGO,MAAM;IACH,WAAkC,IAAI,MAAM;IAEpD,UAAU,UAAkB,EAAE,cAAsB,CAAC,EAAE,WAAmB,KAAK,KAAK,IAAI,EAAW;QACjG,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,cAAc,MAAM;QAE1B,4CAA4C;QAC5C,MAAM,mBAAmB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE;QAE5D,yCAAyC;QACzC,MAAM,iBAAiB,iBAAiB,MAAM,CAAC,CAAA,OAAQ,OAAO;QAE9D,2BAA2B;QAC3B,IAAI,eAAe,MAAM,IAAI,aAAa;YACxC,OAAO;QACT;QAEA,sBAAsB;QACtB,eAAe,IAAI,CAAC;QACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY;QAE9B,OAAO;IACT;IAEA,iBAAiB,UAAkB,EAAE,WAAmB,KAAK,KAAK,IAAI,EAAU;QAC9E,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE;QACpD,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAElC,MAAM,gBAAgB,KAAK,GAAG,IAAI;QAClC,MAAM,YAAY,gBAAgB;QAClC,MAAM,MAAM,KAAK,GAAG;QAEpB,OAAO,KAAK,GAAG,CAAC,GAAG,YAAY;IACjC;AACF;AAEO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/dropdown-select.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ChevronDown, Check } from 'lucide-react';\n\nexport interface DropdownOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\nexport interface DropdownSelectProps {\n  options: DropdownOption[];\n  value?: string;\n  placeholder?: string;\n  onChange?: (value: string) => void;\n  className?: string;\n  disabled?: boolean;\n  error?: string;\n  label?: string;\n  required?: boolean;\n}\n\nexport function DropdownSelect({\n  options,\n  value,\n  placeholder = \"Select an option\",\n  onChange,\n  className,\n  disabled = false,\n  error,\n  label,\n  required = false,\n}: DropdownSelectProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedValue, setSelectedValue] = useState(value || '');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const selectedOption = options.find(option => option.value === selectedValue);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  useEffect(() => {\n    if (value !== undefined) {\n      setSelectedValue(value);\n    }\n  }, [value]);\n\n  const handleSelect = (optionValue: string) => {\n    setSelectedValue(optionValue);\n    setIsOpen(false);\n    onChange?.(optionValue);\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent) => {\n    if (disabled) return;\n\n    switch (event.key) {\n      case 'Enter':\n      case ' ':\n        event.preventDefault();\n        setIsOpen(!isOpen);\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        break;\n      case 'ArrowDown':\n        event.preventDefault();\n        if (!isOpen) {\n          setIsOpen(true);\n        } else {\n          // Focus next option\n          const currentIndex = options.findIndex(opt => opt.value === selectedValue);\n          const nextIndex = Math.min(currentIndex + 1, options.length - 1);\n          if (options[nextIndex] && !options[nextIndex].disabled) {\n            handleSelect(options[nextIndex].value);\n          }\n        }\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        if (isOpen) {\n          const currentIndex = options.findIndex(opt => opt.value === selectedValue);\n          const prevIndex = Math.max(currentIndex - 1, 0);\n          if (options[prevIndex] && !options[prevIndex].disabled) {\n            handleSelect(options[prevIndex].value);\n          }\n        }\n        break;\n    }\n  };\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      {label && (\n        <label className=\"block text-sm font-medium text-mantle-100 mb-2 font-sans\">\n          {label}\n          {required && <span className=\"text-error ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div ref={dropdownRef} className=\"relative\">\n        <button\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          onKeyDown={handleKeyDown}\n          disabled={disabled}\n          className={cn(\n            \"relative w-full rounded-lg px-4 py-3 text-left font-sans transition-all duration-200\",\n            \"bg-light-charcoal border border-sage/30 text-light-off-white\",\n            \"focus:outline-none focus:ring-2 focus:ring-muted-gold/30 focus:border-muted-gold\",\n            \"hover:border-sage/50\",\n            disabled && \"opacity-50 cursor-not-allowed\",\n            error && \"border-error focus:ring-error/30\",\n            isOpen && \"ring-2 ring-muted-gold/30 border-muted-gold\"\n          )}\n          aria-haspopup=\"listbox\"\n          aria-expanded={isOpen}\n          aria-labelledby={label ? `${label}-label` : undefined}\n        >\n          <span className=\"block truncate\">\n            {selectedOption ? selectedOption.label : placeholder}\n          </span>\n          <span className=\"absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none\">\n            <ChevronDown\n              className={cn(\n                \"w-4 h-4 text-mantle-300 transition-transform duration-200\",\n                isOpen && \"rotate-180\"\n              )}\n            />\n          </span>\n        </button>\n\n        {isOpen && (\n          <div className=\"absolute z-50 w-full mt-1 bg-neutral-charcoal-dark/80 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/70 border border-brand-sage/20 rounded-lg shadow-2xl max-h-60 overflow-auto\">\n            <ul role=\"listbox\" className=\"py-1\">\n              {options.map((option) => (\n                <li\n                  key={option.value}\n                  role=\"option\"\n                  aria-selected={selectedValue === option.value}\n                  className={cn(\n                    \"relative cursor-pointer select-none py-2 pl-10 pr-4 text-light-off-white font-sans\",\n                    \"hover:bg-light-charcoal focus:bg-light-charcoal\",\n                    option.disabled && \"opacity-50 cursor-not-allowed\",\n                    selectedValue === option.value && \"bg-light-charcoal\"\n                  )}\n                  onClick={() => !option.disabled && handleSelect(option.value)}\n                >\n                  <span className=\"block truncate\">{option.label}</span>\n                  {selectedValue === option.value && (\n                    <span className=\"absolute inset-y-0 left-0 flex items-center pl-3\">\n                      <Check className=\"w-4 h-4 text-terracotta\" />\n                    </span>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-1 text-sm text-error font-sans\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAwBO,SAAS,eAAe,EAC7B,OAAO,EACP,KAAK,EACL,cAAc,kBAAkB,EAChC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACI;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IAC5D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,iBAAiB;QACnB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB,UAAU;QACV,WAAW;IACb;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU;QAEd,OAAQ,MAAM,GAAG;YACf,KAAK;YACL,KAAK;gBACH,MAAM,cAAc;gBACpB,UAAU,CAAC;gBACX;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,CAAC,QAAQ;oBACX,UAAU;gBACZ,OAAO;oBACL,oBAAoB;oBACpB,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBAC5D,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG,QAAQ,MAAM,GAAG;oBAC9D,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACtD,aAAa,OAAO,CAAC,UAAU,CAAC,KAAK;oBACvC;gBACF;gBACA;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,QAAQ;oBACV,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBAC5D,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG;oBAC7C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACtD,aAAa,OAAO,CAAC,UAAU,CAAC,KAAK;oBACvC;gBACF;gBACA;QACJ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B,uBACC,8OAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;0BAInD,8OAAC;gBAAI,KAAK;gBAAa,WAAU;;kCAC/B,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,CAAC,YAAY,UAAU,CAAC;wBACvC,WAAW;wBACX,UAAU;wBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wFACA,gEACA,oFACA,wBACA,YAAY,iCACZ,SAAS,oCACT,UAAU;wBAEZ,iBAAc;wBACd,iBAAe;wBACf,mBAAiB,QAAQ,GAAG,MAAM,MAAM,CAAC,GAAG;;0CAE5C,8OAAC;gCAAK,WAAU;0CACb,iBAAiB,eAAe,KAAK,GAAG;;;;;;0CAE3C,8OAAC;gCAAK,WAAU;0CACd,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6DACA,UAAU;;;;;;;;;;;;;;;;;oBAMjB,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,MAAK;4BAAU,WAAU;sCAC1B,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,MAAK;oCACL,iBAAe,kBAAkB,OAAO,KAAK;oCAC7C,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sFACA,mDACA,OAAO,QAAQ,IAAI,iCACnB,kBAAkB,OAAO,KAAK,IAAI;oCAEpC,SAAS,IAAM,CAAC,OAAO,QAAQ,IAAI,aAAa,OAAO,KAAK;;sDAE5D,8OAAC;4CAAK,WAAU;sDAAkB,OAAO,KAAK;;;;;;wCAC7C,kBAAkB,OAAO,KAAK,kBAC7B,8OAAC;4CAAK,WAAU;sDACd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;mCAdhB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;YAwB5B,uBACC,8OAAC;gBAAE,WAAU;0BAAqC;;;;;;;;;;;;AAI1D", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/contact/page.tsx"], "sourcesContent": ["\"use client\"\nimport { useState } from 'react'\nimport Link from \"next/link\"\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport { useToast } from \"@/components/ui/toast\";\nimport { validateForm, contactFormSchema, sanitizeInput, rateLimiter } from \"@/lib/validation\";\nimport { DropdownSelect, type DropdownOption } from \"@/components/ui/dropdown-select\";\n\nexport default function ContactPage() {\n  const { addToast } = useToast();\n\n  // Contact type options for dropdown\n  const contactTypeOptions: DropdownOption[] = [\n    { value: 'general', label: 'General Inquiry' },\n    { value: 'customer', label: 'Customer Support' },\n    { value: 'professional', label: 'Professional Services' },\n    { value: 'partnership', label: 'Partnership Opportunity' },\n    { value: 'support', label: 'Technical Support' }\n  ];\n\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n    type: 'general'\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n  const [error, setError] = useState('')\n  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    setError('')\n    setValidationErrors({})\n\n    // Sanitize input data\n    const sanitizedData = {\n      name: sanitizeInput(formData.name),\n      email: sanitizeInput(formData.email),\n      subject: sanitizeInput(formData.subject),\n      message: sanitizeInput(formData.message),\n      type: formData.type\n    }\n\n    // Validate form data\n    const validation = validateForm(sanitizedData, contactFormSchema)\n    if (!validation.isValid) {\n      setValidationErrors(validation.errors)\n      setIsSubmitting(false)\n      addToast({\n        type: 'error',\n        title: 'Validation Error',\n        description: 'Please fix the errors below and try again.'\n      })\n      return\n    }\n\n    // Rate limiting check\n    const userIdentifier = sanitizedData.email || 'anonymous'\n    if (!rateLimiter.isAllowed(userIdentifier, 3, 15 * 60 * 1000)) {\n      const remainingTime = Math.ceil(rateLimiter.getRemainingTime(userIdentifier, 15 * 60 * 1000) / 1000 / 60)\n      setIsSubmitting(false)\n      addToast({\n        type: 'error',\n        title: 'Too Many Attempts',\n        description: `Please wait ${remainingTime} minutes before submitting again.`\n      })\n      return\n    }\n\n    try {\n      // Submit to backend API with fallback to localStorage\n      const response = await fetch('/api/contact', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...sanitizedData,\n          timestamp: new Date().toISOString()\n        }),\n      })\n\n      const result = await response.json()\n\n      if (result.success) {\n        setIsSubmitted(true)\n        setFormData({ name: '', email: '', subject: '', message: '', type: 'general' })\n        addToast({\n          type: 'success',\n          title: 'Message Sent!',\n          description: 'Thank you for contacting us. We\\'ll get back to you soon.'\n        })\n      } else {\n        setError(result.error || 'Something went wrong. Please try again.')\n        addToast({\n          type: 'error',\n          title: 'Submission Failed',\n          description: result.error || 'Something went wrong. Please try again.'\n        })\n      }\n    } catch (err) {\n      // Fallback to localStorage\n      const existingContacts = JSON.parse(localStorage.getItem('vierla-contacts') || '[]')\n      existingContacts.push({\n        ...formData,\n        timestamp: new Date().toISOString()\n      })\n      localStorage.setItem('vierla-contacts', JSON.stringify(existingContacts))\n      setIsSubmitted(true)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n  }\n\n  return (\n    <div className=\"page-contact min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Main Content */}\n      <main className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-6xl font-black text-light-off-white mb-6 drop-shadow-lg font-notable\">\n              CONTACT US\n            </h1>\n            <p className=\"text-xl text-warm-beige max-w-2xl mx-auto drop-shadow-sm font-sans\">\n              Have questions about our services? Want to join our platform as a beauty professional? We'd love to hear from you.\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <div className=\"bg-light-charcoal backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-sage/30\">\n              <h2 className=\"text-2xl font-bold text-light-off-white mb-6 drop-shadow-lg font-tai-heritage\">\n                Send us a message\n              </h2>\n              \n              {isSubmitted ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 bg-primary/20\">\n                    <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-light-off-white mb-2 drop-shadow-lg font-tai-heritage\">Message Sent!</h3>\n                  <p className=\"text-warm-beige drop-shadow-sm font-sans\">Thank you for reaching out. We'll get back to you within 24 hours.</p>\n                </div>\n              ) : (\n                <form className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans\">Name *</label>\n                      <input\n                        type=\"text\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm font-sans\"\n                        placeholder=\"Your full name\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans\">Email *</label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm font-sans\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <DropdownSelect\n                      label=\"Contact Type\"\n                      options={contactTypeOptions}\n                      value={formData.type}\n                      onChange={(value) => setFormData(prev => ({ ...prev, type: value }))}\n                      error={validationErrors.type}\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans\">Subject *</label>\n                    <input\n                      type=\"text\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm font-sans\"\n                      placeholder=\"What's this about?\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-warm-beige text-sm font-medium mb-2 drop-shadow-sm font-sans\">Message *</label>\n                    <textarea\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      rows={5}\n                      className=\"w-full px-4 py-3 rounded-xl bg-warm-beige/10 border border-sage text-light-off-white placeholder-warm-beige/50 focus:outline-none focus:ring-2 focus:ring-muted-gold focus:border-muted-gold backdrop-blur-sm resize-none font-sans\"\n                      placeholder=\"Tell us more about your inquiry...\"\n                    />\n                  </div>\n\n                  {error && (\n                    <div className=\"p-4 rounded-xl bg-red-500/20 border border-red-500/30\">\n                      <p className=\"text-red-200 text-sm drop-shadow-sm\">{error}</p>\n                    </div>\n                  )}\n\n                  <ShimmerButton\n                    type=\"submit\"\n                    size=\"lg\"\n                    background=\"#B8956A\"\n                    shimmerColor=\"#E5D4A1\"\n                    className=\"w-full py-4 text-lg font-medium text-[#2D2A26]\"\n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? \"Sending...\" : \"Send Message\"}\n                  </ShimmerButton>\n                </form>\n              )}\n            </div>\n\n            {/* Contact Information */}\n            <div className=\"space-y-8\">\n              <div className=\"bg-light-charcoal backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-sage/30\">\n                <h3 className=\"text-xl font-bold text-light-off-white mb-6 drop-shadow-lg font-tai-heritage\">\n                  Get in Touch\n                </h3>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 rounded-xl flex items-center justify-center bg-sage/20 border border-sage/30\">\n                      <svg className=\"w-6 h-6 text-sage drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-light-off-white mb-1 drop-shadow-lg font-tai-heritage\">Email</h3>\n                      <p className=\"text-warm-beige drop-shadow-sm font-sans\"><EMAIL></p>\n                      <p className=\"text-warm-beige/60 text-sm drop-shadow-sm font-sans\">We typically respond within 24 hours</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 rounded-xl flex items-center justify-center bg-sage/20 border border-sage/30\">\n                      <svg className=\"w-6 h-6 text-sage drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-light-off-white mb-1 drop-shadow-lg font-tai-heritage\">Location</h3>\n                      <p className=\"text-warm-beige drop-shadow-sm font-sans\">Toronto & Ottawa, Ontario</p>\n                      <p className=\"text-warm-beige/60 text-sm drop-shadow-sm font-sans\">Serving the Greater Toronto and Ottawa areas</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 rounded-xl flex items-center justify-center bg-sage/20 border border-sage/30\">\n                      <svg className=\"w-6 h-6 text-sage drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-light-off-white mb-1 drop-shadow-lg font-tai-heritage\">Response Time</h3>\n                      <p className=\"text-warm-beige drop-shadow-sm font-sans\">Within 24 hours</p>\n                      <p className=\"text-warm-beige/60 text-sm drop-shadow-sm font-sans\">Monday to Friday, 9 AM - 6 PM EST</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-light-charcoal backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-sage/30\">\n                <h3 className=\"text-xl font-bold text-light-off-white mb-4 drop-shadow-lg font-tai-heritage\">\n                  For Beauty Professionals\n                </h3>\n                <p className=\"text-warm-beige mb-4 drop-shadow-sm font-sans\">\n                  Interested in joining our platform? We're always looking for talented, licensed beauty professionals.\n                </p>\n                <Link href=\"/apply\">\n                  <ShimmerButton\n                    size=\"md\"\n                    background=\"#B8956A\"\n                    shimmerColor=\"#E5D4A1\"\n                    className=\"w-full px-4 py-3 text-base font-medium text-[#2D2A26]\"\n                  >\n                    Apply Now\n                  </ShimmerButton>\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD;IAE5B,oCAAoC;IACpC,MAAM,qBAAuC;QAC3C;YAAE,OAAO;YAAW,OAAO;QAAkB;QAC7C;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAgB,OAAO;QAAwB;QACxD;YAAE,OAAO;YAAe,OAAO;QAA0B;QACzD;YAAE,OAAO;YAAW,OAAO;QAAoB;KAChD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAErF,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QACT,oBAAoB,CAAC;QAErB,sBAAsB;QACtB,MAAM,gBAAgB;YACpB,MAAM,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI;YACjC,OAAO,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK;YACnC,SAAS,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,OAAO;YACvC,SAAS,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,OAAO;YACvC,MAAM,SAAS,IAAI;QACrB;QAEA,qBAAqB;QACrB,MAAM,aAAa,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE,eAAe,iHAAA,CAAA,oBAAiB;QAChE,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,oBAAoB,WAAW,MAAM;YACrC,gBAAgB;YAChB,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,sBAAsB;QACtB,MAAM,iBAAiB,cAAc,KAAK,IAAI;QAC9C,IAAI,CAAC,iHAAA,CAAA,cAAW,CAAC,SAAS,CAAC,gBAAgB,GAAG,KAAK,KAAK,OAAO;YAC7D,MAAM,gBAAgB,KAAK,IAAI,CAAC,iHAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC,gBAAgB,KAAK,KAAK,QAAQ,OAAO;YACtG,gBAAgB;YAChB,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,YAAY,EAAE,cAAc,iCAAiC,CAAC;YAC9E;YACA;QACF;QAEA,IAAI;YACF,sDAAsD;YACtD,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,aAAa;oBAChB,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe;gBACf,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;oBAAI,SAAS;oBAAI,MAAM;gBAAU;gBAC7E,SAAS;oBACP,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;gBACzB,SAAS;oBACP,MAAM;oBACN,OAAO;oBACP,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,KAAK;YACZ,2BAA2B;YAC3B,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,sBAAsB;YAC/E,iBAAiB,IAAI,CAAC;gBACpB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,eAAe;QACjB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwF;;;;;;8CAGtG,8OAAC;oCAAE,WAAU;8CAAqE;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgF;;;;;;wCAI7F,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAuB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC9E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DAA+E;;;;;;8DAC7F,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;iEAG1D,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA0E;;;;;;8EAC3F,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAGhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA0E;;;;;;8EAC3F,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,8OAAC;8DACC,cAAA,8OAAC,uIAAA,CAAA,iBAAc;wDACb,OAAM;wDACN,SAAS;wDACT,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM;gEAAM,CAAC;wDAClE,OAAO,iBAAiB,IAAI;wDAC5B,QAAQ;;;;;;;;;;;8DAIZ,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0E;;;;;;sEAC3F,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0E;;;;;;sEAC3F,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,QAAQ;4DACR,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;gDAIf,uBACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAuC;;;;;;;;;;;8DAIxD,8OAAC,sIAAA,CAAA,gBAAa;oDACZ,MAAK;oDACL,MAAK;oDACL,YAAW;oDACX,cAAa;oDACb,WAAU;oDACV,UAAU;8DAET,eAAe,eAAe;;;;;;;;;;;;;;;;;;8CAOvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+E;;;;;;8DAI7F,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAmC,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC1F,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAmF;;;;;;sFACjG,8OAAC;4EAAE,WAAU;sFAA2C;;;;;;sFACxD,8OAAC;4EAAE,WAAU;sFAAsD;;;;;;;;;;;;;;;;;;sEAIvE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAmC,MAAK;wEAAO,QAAO;wEAAe,SAAQ;;0FAC1F,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;0FACrE,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;8EAGzE,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAmF;;;;;;sFACjG,8OAAC;4EAAE,WAAU;sFAA2C;;;;;;sFACxD,8OAAC;4EAAE,WAAU;sFAAsD;;;;;;;;;;;;;;;;;;sEAIvE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAmC,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC1F,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAmF;;;;;;sFACjG,8OAAC;4EAAE,WAAU;sFAA2C;;;;;;sFACxD,8OAAC;4EAAE,WAAU;sFAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAM3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+E;;;;;;8DAG7F,8OAAC;oDAAE,WAAU;8DAAgD;;;;;;8DAG7D,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,sIAAA,CAAA,gBAAa;wDACZ,MAAK;wDACL,YAAW;wDACX,cAAa;wDACb,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,cAAA,CAAA;YAAgB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAanF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,WAAA,CAAA,CAAA,wKAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/check.js", "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/node_modules/lucide-react/src/icons/check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,iBAAA,CAAA;YAAmB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAatF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,wKAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}