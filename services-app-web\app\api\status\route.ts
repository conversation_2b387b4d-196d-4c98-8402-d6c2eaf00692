import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const statusData = {
      version: process.env.APP_VERSION || '1.0.0',
      uptime: process.uptime() * 1000, // Convert to milliseconds
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString(),
      features: {
        emailSignup: true,
        contactForm: true,
        professionalApplication: true,
        analytics: true
      }
    }

    return NextResponse.json({
      success: true,
      data: statusData
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Status check failed'
      },
      { status: 500 }
    )
  }
}
