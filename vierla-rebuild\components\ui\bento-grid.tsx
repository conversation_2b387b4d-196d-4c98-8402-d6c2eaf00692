import { ReactNode } from "react";
import { ArrowRightIcon } from "@radix-ui/react-icons";
import Link from "next/link";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

const BentoGrid = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "grid w-full auto-rows-[20rem] grid-cols-3 gap-4",
        // 16px gap as specified in design system
        "gap-4", // 16px
        className,
      )}
    >
      {children}
    </div>
  );
};

const BentoCard = ({
  name,
  className,
  background,
  Icon,
  description,
  services,
  href,
  cta,
}: {
  name: string;
  className: string;
  background: ReactNode;
  Icon: any;
  description: string;
  services?: string[];
  href: string;
  cta: string;
}) => (
  <div
    key={name}
    className={cn(
      "group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl",
      // New Visual Identity - Light Charcoal with subtle sage borders
      "bg-light-charcoal border border-sage/30 shadow-xl",
      // Hover effect with gold glow
      "hover:shadow-[0_0_20px_rgba(184,149,106,0.3)] transition-all duration-300",
      "transform-gpu",
      className,
    )}
  >
    <div>{background}</div>
    <div className="pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-4 transition-all duration-300 group-hover:-translate-y-8">
      <Icon className="h-10 w-10 origin-left transform-gpu transition-all duration-300 ease-in-out group-hover:scale-75" style={{ color: 'var(--icon-accent)' }} />
      <h3 className="text-lg font-semibold text-light-off-white font-tai-heritage leading-tight">
        {name}
      </h3>
      <p className="max-w-lg text-warm-beige font-sans text-sm leading-relaxed">{description}</p>
    </div>

    <div
      className={cn(
        "pointer-events-none absolute bottom-0 flex w-full translate-y-12 transform-gpu flex-col p-3 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100 bg-gradient-to-t from-light-charcoal/95 to-transparent",
      )}
    >
      {services ? (
        <div className="text-warm-beige/90 text-xs leading-relaxed font-sans space-y-0.5 max-h-20 overflow-hidden">
          {services.map((service, idx) => (
            <div key={idx} className="text-left truncate">
              {service}
            </div>
          ))}
        </div>
      ) : (
        <Button variant="ghost" asChild size="sm" className="pointer-events-auto">
          <Link href={href}>
            {cta}
            <ArrowRightIcon className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      )}
    </div>
    <div className="pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-muted-gold/[.05] group-hover:dark:bg-muted-gold/10" />
  </div>
);

export { BentoCard, BentoGrid };
