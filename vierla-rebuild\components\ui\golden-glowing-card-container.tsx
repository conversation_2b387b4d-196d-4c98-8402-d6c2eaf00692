"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { GlowingEffect } from "./glowing-effect";

interface GoldenGlowingCardContainerProps {
  children: React.ReactNode;
  className?: string;
}

export const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = React.memo(({
  children,
  className,
}) => {
  return (
    <div className={cn("relative h-full group", className)}>
      {/* Outer container with glowing effect */}
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
          borderWidth={3}
          variant="sage"
        />

        {/* Inner container with margin - Enhanced glassmorphism */}
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] bg-neutral-charcoal-light/20 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-light/15 p-6 shadow-xl border-brand-sage/10">
          {children}
        </div>
      </div>
    </div>
  );
});

export default GoldenGlowingCardContainer;
