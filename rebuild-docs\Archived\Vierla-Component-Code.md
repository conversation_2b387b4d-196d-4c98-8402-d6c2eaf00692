

# **Vierla.com: Component Reference Code**

This document contains the source code for the finalized selection of UI components. Copy this code directly into the appropriate files within your project structure (e.g., components/ui/ or components/marketing/).

---

### **1\. Aurora Background (Aceternity UI)**

\*File: components/ui/aurora-background.tsx\*tsx  
"use client";  
import { cn } from "@/lib/utils";  
import React, { ReactNode } from "react";  
interface AuroraBackgroundProps extends React.HTMLProps {  
children: ReactNode;  
showRadialGradient?: boolean;  
}  
export const AuroraBackground \= ({  
className,  
children,  
showRadialGradient \= true,  
...props  
}: AuroraBackgroundProps) \=\> {  
return (

\<div  
className={cn(  
"relative flex flex-col h-\[100vh\] items-center justify-center bg-zinc-50 dark:bg-zinc-900 text-slate-950 transition-bg",  
className  
)}  
{...props}  
\>

\<div  
className={cn(  
...\[source\](https://ui.aceternity.com/components/aurora-background) after:content-\[""\] after:absolute after:inset-0 after:\[background-image:var(--white-gradient),var(--aurora)\] after:dark:\[background-image:var(--dark-gradient),var(--aurora)\] after:\[background-size:200%,\_100%\] after:animate-aurora after:\[background-attachment:fixed\] after:mix-blend-difference pointer-events-none absolute \-inset-\[10px\] opacity-50 will-change-transform,

          showRadialGradient &&  
            \`\[mask-image:radial-gradient(ellipse\_at\_100%\_0%,black\_10%,var(--transparent)\_70%)\]\`  
        )}  
      \>\</div\>  
    \</div\>  
    {children}  
  \</div\>  
\</main\>

);  
};

\---

\#\#\# \*\*2. Bento Grid & Glowing Effect Card (Aceternity UI)\*\*  
\*File 1: \`components/ui/bento-grid.tsx\`\*  
\`\`\`tsx  
import { cn } from "@/lib/utils";

export const BentoGrid \= ({  
  className,  
  children,  
}: {  
  className?: string;  
  children?: React.ReactNode;  
}) \=\> {  
  return (  
    \<div  
      className={cn(  
        "grid md:auto-rows-\[18rem\] grid-cols-1 md:grid-cols-3 gap-4 max-w-7xl mx-auto ",  
        className  
      )}  
    \>  
      {children}  
    \</div\>  
  );  
};

export const BentoGridItem \= ({  
  className,  
  title,  
  description,  
  header,  
  icon,  
}: {  
  className?: string;  
  title?: string | React.ReactNode;  
  description?: string | React.ReactNode;  
  header?: React.ReactNode;  
  icon?: React.ReactNode;  
}) \=\> {  
  return (  
    \<div  
      className={cn(  
        "row-span-1 rounded-xl group/bento hover:shadow-xl transition duration-200 shadow-input dark:shadow-none p-4 dark:bg-black dark:border-white/\[0.2\] bg-white border border-transparent justify-between flex flex-col space-y-4",  
        className  
      )}  
    \>  
      {header}  
      \<div className="group-hover/bento:translate-x-2 transition duration-200"\>  
        {icon}  
        \<div className="font-sans font-bold text-neutral-600 dark:text-neutral-200 mb-2 mt-2"\>  
          {title}  
        \</div\>  
        \<div className="font-sans font-normal text-neutral-600 text-xs dark:text-neutral-300"\>  
          {description}  
        \</div\>  
      \</div\>  
    \</div\>  
  );  
};

*File 2: components/ui/glowing-border-card.tsx (This is the effect to apply to the BentoGridItem)*

TypeScript

"use client";  
import React, { useEffect, useRef, useState } from "react";

export const GlowingBordersCard \= ({  
  children,  
  className,  
  containerClassName,  
}: {  
  children?: React.ReactNode;  
  className?: string;  
  containerClassName?: string;  
}) \=\> {  
  const containerRef \= useRef\<HTMLDivElement\>(null);  
  const \= useState({ width: 0, height: 0 });

  useEffect(() \=\> {  
    if (containerRef.current) {  
      const { width, height } \= containerRef.current.getBoundingClientRect();  
      setDimensions({ width, height });  
    }  
  },);

  return (  
    \<div  
      ref={containerRef}  
      style={{  
        "--card-width": \`${dimensions.width}px\`,  
        "--card-height": \`${dimensions.height}px\`,  
      }}  
      className={\`relative w-full h-full p-6 bg-gray-100 dark:bg-gray-900 rounded-xl border border-neutral-200 dark:border-neutral-800 ${containerClassName}\`}  
    \>  
      \<div className="relative z-10"\>{children}\</div\>  
      \<div  
        style={  
          {  
            "--glow-color": "hsl(var(--primary))",  
          } as React.CSSProperties  
        }  
        className={\`absolute inset-0 w-full h-full bg-\[radial-gradient(var(--glow-color)\_40%,transparent\_60%)\] \-z-10 blur-3xl  
        group-hover/bento:opacity-100 opacity-0 transition-opacity duration-500  
        \`}  
      /\>  
    \</div\>  
  );  
};

*Note: To use this, you will wrap the content of BentoGridItem with the GlowingBordersCard component.*

---

### **3\. Shiny Button (Magic UI)**

*File: components/ui/shiny-button.tsx*

TypeScript

import { cn } from "@/lib/utils";  
import { CSSProperties, FC, ReactNode } from "react";

interface ShinyButtonProps {  
  text?: string;  
  className?: string;  
  children?: ReactNode;  
}

const ShinyButton: FC\<ShinyButtonProps\> \= ({  
  text \= "shiny-button",  
  className,  
  children,  
 ...props  
}) \=\> {  
  return (  
    \<button  
      className={cn(  
        "relative inline-flex h-12 overflow-hidden rounded-full p-\[1px\] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50",  
        className,  
      )}  
      {...props}  
    \>  
      \<span className="absolute inset-\[-1000%\] animate-\[spin\_2s\_linear\_infinite\] bg-" /\>  
      \<span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-slate-950 px-3 py-1 text-sm font-medium text-white backdrop-blur-3xl"\>  
        {children |

| text}  
      \</span\>  
    \</button\>  
  );  
};

export default ShinyButton;

---

### **4\. Multistep Form (arihantcodes)**

*File: components/marketing/multistep-form.tsx*

TypeScript

"use client";  
import { useState } from "react";  
import { motion } from "framer-motion";  
import { cn } from "@/lib/utils";

type Field \= {  
  label: string;  
  name: string;  
  type: string;  
  placeholder: string;  
};

type Step \= {  
  title: string;  
  fields: Field;  
};

const steps: Step \=,  
  },  
  {  
    title: "Company Information",  
    fields:,  
  },  
  {  
    title: "Confirmation",  
    fields:,  
  },  
\];

export function MultiStepForm() {  
  const \= useState(0);  
  const \= useState({});

  const handleChange \= (e: React.ChangeEvent\<HTMLInputElement\>) \=\> {  
    setFormData({...formData, \[e.target.name\]: e.target.value });  
  };

  const nextStep \= () \=\> {  
    if (currentStep \< steps.length \- 1\) {  
      setCurrentStep(currentStep \+ 1);  
    }  
  };

  const prevStep \= () \=\> {  
    if (currentStep \> 0\) {  
      setCurrentStep(currentStep \- 1);  
    }  
  };

  const handleSubmit \= (e: React.FormEvent) \=\> {  
    e.preventDefault();  
    // Final submission logic here  
    console.log("Form submitted:", formData);  
  };

  return (  
    \<div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md dark:bg-gray-800"\>  
      \<h2 className="text-2xl font-bold text-center text-gray-900 dark:text-white"\>  
        {steps.title}  
      \</h2\>  
      \<form onSubmit={handleSubmit} className="mt-8 space-y-6"\>  
        \<motion.div  
          key={currentStep}  
          initial={{ x: 300, opacity: 0 }}  
          animate={{ x: 0, opacity: 1 }}  
          exit={{ x: \-300, opacity: 0 }}  
          transition={{ duration: 0.5 }}  
        \>  
          {steps.fields.map((field) \=\> (  
            \<div key={field.name} className="mb-4"\>  
              \<label htmlFor={field.name} className="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-300"\>  
                {field.label}  
              \</label\>  
              \<input  
                type={field.type}  
                name={field.name}  
                id={field.name}  
                placeholder={field.placeholder}  
                onChange={handleChange}  
                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"  
              /\>  
            \</div\>  
          ))}  
        \</motion.div\>  
        \<div className="flex justify-between"\>  
          {currentStep \> 0 && (  
            \<button type="button" onClick={prevStep} className="px-4 py-2 text-sm font-medium text-white bg-gray-500 rounded-md hover:bg-gray-600"\>  
              Back  
            \</button\>  
          )}  
          {currentStep \< steps.length \- 1? (  
            \<button type="button" onClick={nextStep} className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"\>  
              Next  
            \</button\>  
          ) : (  
            \<button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"\>  
              Submit  
            \</button\>  
          )}  
        \</div\>  
      \</form\>  
    \</div\>  
  );  
}

*Note: The styling of this form will need to be updated to use the custom shadcn/ui components (Input, Button, Label) created in Task 2 of the roadmap.*

---

### **5\. Footer (mdadul)**

*File: components/marketing/footer.tsx*

TypeScript

import React from "react";

export function Footer() {  
  return (  
    \<footer className="bg-gray-900 text-white py-12"\>  
      \<div className="container mx-auto px-4"\>  
        \<div className="grid grid-cols-1 md:grid-cols-4 gap-8"\>  
          \<div\>  
            \<h2 className="text-2xl font-bold mb-4"\>Vierla\</h2\>  
            \<p className="text-gray-400"\>The AI-Powered Platform for Modern Business.\</p\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Product\</h3\>  
            \<ul className="space-y-2"\>  
              \<li\>\<a href="/features" className="hover:text-gray-300"\>Features\</a\>\</li\>  
              \<li\>\<a href="/pricing" className="hover:text-gray-300"\>Pricing\</a\>\</li\>  
              \<li\>\<a href="/updates" className="hover:text-gray-300"\>Updates\</a\>\</li\>  
            \</ul\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Company\</h3\>  
            \<ul className="space-y-2"\>  
              \<li\>\<a href="/about" className="hover:text-gray-300"\>About Us\</a\>\</li\>  
              \<li\>\<a href="/blog" className="hover:text-gray-300"\>Blog\</a\>\</li\>  
              \<li\>\<a href="/contact" className="hover:text-gray-300"\>Contact\</a\>\</li\>  
            \</ul\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Legal\</h3\>  
            \<ul className="space-y-2"\>  
              \<li\>\<a href="/privacy" className="hover:text-gray-300"\>Privacy Policy\</a\>\</li\>  
              \<li\>\<a href="/terms" className="hover:text-gray-300"\>Terms of Service\</a\>\</li\>  
            \</ul\>  
          \</div\>  
        \</div\>  
        \<div className="mt-8 border-t border-gray-800 pt-8 text-center text-gray-500"\>  
          \<p\>© {new Date().getFullYear()} Vierla, Inc. All rights reserved.\</p\>  
        \</div\>  
      \</div\>  
    \</footer\>  
  );  
}

---

### **6\. Navigation Bar (shadcnblocks-com-navbar1)**

*File: components/marketing/navbar.tsx*

TypeScript

import Link from "next/link";  
import { Button } from "@/components/ui/button"; // Assumes custom Button component exists  
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"; // Assumes Sheet component exists  
import { Menu } from "lucide-react";

export function Navbar() {  
  return (  
    \<header className="flex h-16 w-full items-center justify-between px-4 md:px-6 bg-transparent text-white absolute top-0 left-0 z-50"\>  
      \<Link href="/" className="flex items-center gap-2" prefetch={false}\>  
        \<span className="text-lg font-semibold"\>Vierla\</span\>  
      \</Link\>  
      \<nav className="hidden items-center gap-6 text-sm font-medium md:flex"\>  
        \<Link href="/features" className="hover:underline underline-offset-4" prefetch={false}\>  
          Features  
        \</Link\>  
        \<Link href="/pricing" className="hover:underline underline-offset-4" prefetch={false}\>  
          Pricing  
        \</Link\>  
        \<Link href="/about" className="hover:underline underline-offset-4" prefetch={false}\>  
          About  
        \</Link\>  
      \</nav\>  
      \<div className="hidden items-center gap-4 md:flex"\>  
        \<Button variant="ghost"\>Login\</Button\>  
        {/\* Replace with ShinyButton for the primary CTA \*/}  
        \<Button\>Get Started\</Button\>  
      \</div\>  
      \<Sheet\>  
        \<SheetTrigger asChild\>  
          \<Button variant="outline" size="icon" className="md:hidden"\>  
            \<Menu className="h-6 w-6" /\>  
            \<span className="sr-only"\>Toggle navigation menu\</span\>  
          \</Button\>  
        \</SheetTrigger\>  
        \<SheetContent side="right"\>  
          \<div className="grid gap-4 p-4"\>  
            \<Link href="/features" className="font-medium hover:underline underline-offset-4" prefetch={false}\>  
              Features  
            \</Link\>  
            \<Link href="/pricing" className="font-medium hover:underline underline-offset-4" prefetch={false}\>  
              Pricing  
            \</Link\>  
            \<Link href="/about" className="font-medium hover:underline underline-offset-4" prefetch={false}\>  
              About  
            \</Link\>  
            \<div className="flex flex-col gap-2"\>  
              \<Button variant="ghost"\>Login\</Button\>  
              \<Button\>Get Started\</Button\>  
            \</div\>  
          \</div\>  
        \</SheetContent\>  
      \</Sheet\>  
    \</header\>  
  );  
}

*Note: This component will need to be adapted to use the ShinyButton for its primary "Get Started" call-to-action.*