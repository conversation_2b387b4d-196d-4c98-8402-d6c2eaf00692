@tailwind base;
@tailwind components;
@tailwind utilities;

/*
================================================================================
| VIERLA DESIGN SYSTEM - GLOBAL STYLES (REDESIGNED)
|
| This file contains the complete design system for the Vierla application.
| It's organized into sections for easy maintenance and customization:
|
| 1. MASTER CONTROL PANEL - Core brand colors and theme variables
| 2. PAGE-SPECIFIC CONTROLS - Individual page customization
| 3. GLOBAL COMPONENTS - Shared component styling
| 4. SHADCN UI THEME - UI library integration
| 5. BASE ELEMENT STYLES - HTML element defaults
| 6. PAGE & COMPONENT OVERRIDES - Specific styling rules
| 7. UTILITIES & ANIMATIONS - Helper classes and keyframes
|
| Each component can be individually customized per page while maintaining
| consistency through the master control variables.
================================================================================
*/

@layer base {
  :root {
    /*
    ========================================
    | 🎨 MASTER CONTROL PANEL - New Visual Identity
    | Single source of truth for theme variables that map to Tailwind colors
    ========================================
    */

    /* iOS Safe Area Insets */
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);

    /* Master Brand Colors - Maps to Tailwind semantic colors */
    --master-brand-primary: theme('colors.brand-sage');        /* #7C9A85 - Primary brand color */
    --master-brand-secondary: theme('colors.neutral-charcoal-dark'); /* #2C3137 - Main background */
    --master-brand-accent: theme('colors.brand-gold');         /* #B8956A - Highlights & CTAs */
    --master-brand-beige: theme('colors.brand-beige');         /* #F0E6D9 - Accent background */
    --master-brand-glow: theme('colors.brand-gold');           /* #B8956A - Glow effects */

    /* Master Action Colors */
    --master-action-primary: theme('colors.brand-gold');       /* #B8956A - Primary buttons */
    --master-action-hover: theme('colors.gold.600');           /* #A6845C - Hover states */
    --master-action-focus: theme('colors.gold.700');           /* #94734E - Focus states */

    /* Master Semantic Colors */
    --master-success: theme('colors.success.DEFAULT');         /* #7C9A85 - Success states */
    --master-warning: theme('colors.warning.DEFAULT');         /* #B8956A - Warning states */
    --master-error: theme('colors.error.DEFAULT');             /* #D97706 - Error states */

    /* Master Text Colors */
    --master-text-primary: theme('colors.neutral-charcoal-dark');    /* #2C3137 - Main text on light */
    --master-text-secondary: theme('colors.brand-sage');             /* #7C9A85 - Subtitles */
    --master-text-on-dark: theme('colors.neutral-off-white');        /* #F5FAF7 - Text on dark backgrounds */
    --master-text-muted: theme('colors.brand-beige');                /* #F0E6D9 - Muted text */

    /* Typography Scale (New Visual Identity) - Original Desktop Sizes */
    --font-size-h1-original: 3.052rem;    /* 48.83px - Notable (Oswald) Bold */
    --font-size-h2-original: 2.441rem;    /* 39.06px - Tai Heritage Pro (Playfair) Bold */
    --font-size-h3-original: 1.953rem;    /* 31.25px - Tai Heritage Pro (Playfair) Regular */
    --font-size-h4-original: 1.563rem;    /* 25px - Jost (Inter) Bold */
    --font-size-h5-original: 1.25rem;     /* 20px - Jost (Inter) Bold */
    --font-size-body-original: 1rem;      /* 16px - Jost (Inter) Regular */
    --font-size-small-original: 0.8rem;   /* 12.8px - Jost (Inter) Medium */

    /* Typography Scale - 33.33% Reduced for Non-Mobile Devices */
    --font-size-h1: 2.035rem;    /* 32.56px - Reduced by 33.33% */
    --font-size-h2: 1.627rem;    /* 26.04px - Reduced by 33.33% */
    --font-size-h3: 1.302rem;    /* 20.83px - Reduced by 33.33% */
    --font-size-h4: 1.042rem;    /* 16.67px - Reduced by 33.33% */
    --font-size-h5: 0.833rem;    /* 13.33px - Reduced by 33.33% */
    --font-size-body: 0.667rem;  /* 10.67px - Reduced by 33.33% */
    --font-size-small: 0.533rem; /* 8.53px - Reduced by 33.33% */

    /* Mobile Typography Scale (Responsive) - Keep existing mobile sizes */
    --font-size-h1-mobile: 2.25rem;  /* 36px - Scaled down for mobile */
    --font-size-h2-mobile: 1.875rem; /* 30px - Scaled down for mobile */
    --font-size-h3-mobile: 1.5rem;   /* 24px - Scaled down for mobile */
    --font-size-h4-mobile: 1.25rem;  /* 20px - Scaled down for mobile */
    --font-size-h5-mobile: 1.125rem; /* 18px - Scaled down for mobile */
    --font-size-body-mobile: 0.875rem; /* 14px - Slightly smaller for mobile */
    --font-size-small-mobile: 0.75rem; /* 12px - Smaller for mobile */

    /* Font Family Controls */
    --font-family-h1: var(--font-notable);        /* Notable (Oswald) for H1 headlines */
    --font-family-h2: var(--font-tai-heritage);   /* Tai Heritage Pro (Playfair) for H2 */
    --font-family-h3: var(--font-tai-heritage);   /* Tai Heritage Pro (Playfair) for H3 */
    --font-family-body: var(--font-jost);         /* Jost (Inter) for UI/body text */
    --font-family-accent: var(--font-farsan);     /* Farsan (Dancing Script) for accent */

    /* 8-Point Grid System (Vierla Design System) - Original Sizes */
    --space-1-original: 0.5rem;   /* 8px */
    --space-2-original: 1rem;     /* 16px */
    --space-3-original: 1.5rem;   /* 24px */
    --space-4-original: 2rem;     /* 32px */
    --space-5-original: 2.5rem;   /* 40px */
    --space-6-original: 3rem;     /* 48px */
    --space-7-original: 3.5rem;   /* 56px */
    --space-8-original: 4rem;     /* 64px */
    --space-9-original: 4.5rem;   /* 72px */
    --space-10-original: 5rem;    /* 80px */
    --space-12-original: 6rem;    /* 96px */
    --space-16-original: 8rem;    /* 128px */
    --space-20-original: 10rem;   /* 160px */
    --space-24-original: 12rem;   /* 192px */

    /* 8-Point Grid System - 33.33% Reduced for Non-Mobile Devices */
    --space-1: 0.333rem;   /* 5.33px - Reduced by 33.33% */
    --space-2: 0.667rem;   /* 10.67px - Reduced by 33.33% */
    --space-3: 1rem;       /* 16px - Reduced by 33.33% */
    --space-4: 1.333rem;   /* 21.33px - Reduced by 33.33% */
    --space-5: 1.667rem;   /* 26.67px - Reduced by 33.33% */
    --space-6: 2rem;       /* 32px - Reduced by 33.33% */
    --space-7: 2.333rem;   /* 37.33px - Reduced by 33.33% */
    --space-8: 2.667rem;   /* 42.67px - Reduced by 33.33% */
    --space-9: 3rem;       /* 48px - Reduced by 33.33% */
    --space-10: 3.333rem;  /* 53.33px - Reduced by 33.33% */
    --space-12: 4rem;      /* 64px - Reduced by 33.33% */
    --space-16: 5.333rem;  /* 85.33px - Reduced by 33.33% */
    --space-20: 6.667rem;  /* 106.67px - Reduced by 33.33% */
    --space-24: 8rem;      /* 128px - Reduced by 33.33% */

    /* Master Button Colors */
    --master-button-primary-bg: theme('colors.brand-gold');           /* #B8956A - Primary button background */
    --master-button-primary-text: theme('colors.neutral-charcoal-dark'); /* #2C3137 - Text on primary buttons */
    --master-button-primary-hover: theme('colors.gold.600');          /* #A6845C - Primary button hover */
    --master-button-secondary-bg: transparent;                        /* Transparent secondary buttons */
    --master-button-secondary-text: theme('colors.brand-sage');       /* #7C9A85 - Secondary button text */
    --master-button-secondary-border: theme('colors.brand-sage');     /* #7C9A85 - Secondary button border */
    --master-button-secondary-hover: theme('colors.sage.100');        /* #E8EFEA - Secondary button hover */

    /* Master Card & Container Colors */
    --master-card-background: theme('colors.neutral-charcoal-light'); /* #333333 - Card backgrounds */
    --master-card-border: theme('colors.sage.DEFAULT / 0.3');         /* Sage border with opacity */
    --master-card-glow: theme('colors.brand-gold');                   /* #B8956A - Card glow effects */

    /* Master Header/Navbar Colors - Proper Glassmorphism (Light Mode) */
    --master-header-background: #f6f7f6;                           /* Light mode: opaque very light sage */
    --master-header-backdrop-blur: 50%;                            /* 50% backdrop blur for proper glassmorphism */
    --master-header-border: rgba(139, 154, 140, 0.2);              /* Light mode: medium sage border */
    --master-header-logo-bg: rgba(225, 230, 225, 0.9);             /* Light mode: light sage logo bg */
    --master-header-logo-border: rgba(121, 136, 122, 0.3);         /* Light mode: medium-dark sage border */
    --master-header-logo-icon: #2e332f;                            /* Light mode: dark charcoal icon */
    --master-header-brand-text: #181b19;                           /* Light mode: very dark charcoal text */
    --master-header-nav-text: theme('colors.neutral-off-white');    /* #F5FAF7 - Nav text */
    --master-header-nav-text-muted: theme('colors.brand-gold');     /* #B8956A - Muted nav text */
    --master-header-nav-active-bg: rgba(124, 154, 133, 0.2);        /* Glassmorphic sage active background */
    --master-header-nav-active-glow: transparent;                   /* No active glow */
    --master-header-nav-hover-bg: rgba(184, 149, 106, 0.15);        /* Glassmorphic gold hover background */

    /*
    ========================================
    | 📄 PAGE-SPECIFIC COMPONENT CONTROLS
    ========================================
    */

    /* ----- 🏠 HOME PAGE ----- */
    --home-hero-title: #F5FAF7;                    /* Light Off-White for main headline */
    --home-hero-subtitle: #F0E6D9;                 /* Warm Beige for subtitle */
    --home-hero-cta-bg: #B8956A;                   /* Muted Gold/Tan for CTA */
    --home-hero-cta-text: #2C3137;                 /* Dark Charcoal text on CTA */
    --home-card-background: #333333;               /* Light Charcoal for cards */
    --home-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --home-card-glow: #B8956A;                     /* Gold glow effect */

    /* Home Page Shiny Button Controls */
    --home-shiny-button-bg: #B8956A;               /* Muted Gold/Tan background */
    --home-shiny-button-text: #2C3137;             /* Dark Charcoal text */
    --home-shiny-button-shimmer: #F5FAF7;          /* Light Off-White shimmer */
    --home-shiny-button-hero-bg: #B8956A;          /* Gold hero button */
    --home-shiny-button-hero-text: #2C3137;        /* Dark text on hero button */
    --home-shiny-button-hero-shimmer: #F5FAF7;     /* Light shimmer on hero */
    --home-shiny-button-cta-bg: #B8956A;           /* Gold CTA button */
    --home-shiny-button-cta-text: #2C3137;         /* Dark text on CTA */
    --home-shiny-button-cta-shimmer: #F5FAF7;      /* Light shimmer on CTA */

    /* Home Page Text Effects */
    --home-text-shimmer-base: var(--master-text-on-dark);
    --home-text-shimmer-highlight: var(--master-brand-accent);
    --home-word-pullup-color: var(--mantle-100);
    --home-marquee-text: var(--mantle-100);
    --home-marquee-bg: transparent;

    /* Home Page Golden Cards */
    --home-golden-card-bg: var(--master-card-background);
    --home-golden-card-border: var(--master-card-border);
    --home-golden-card-glow: var(--master-card-glow);

    /* ----- 🎯 FEATURES PAGE ----- */
    --features-hero-title: #F5FAF7;                /* Light Off-White for title */
    --features-hero-subtitle: #F0E6D9;             /* Warm Beige for subtitle */
    --features-bento-card-bg: #333333;             /* Light Charcoal for cards */
    --features-bento-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --features-bento-card-title: #F5FAF7;          /* Light Off-White for card titles */
    --features-bento-card-text: #F0E6D9;           /* Warm Beige for card text */
    --features-bento-icon-color: #7C9A85;          /* Sage for icons */
    --features-bento-grid-gap: 1.5rem;
    --features-cta-button-bg: #B8956A;             /* Gold for CTA buttons */
    --features-cta-button-text: #2C3137;           /* Dark text on CTA */
    --features-cta-button-hover: #A6845C;          /* Darker gold on hover */

    /* Features Page Component Controls */
    --features-shiny-button-bg: var(--master-button-primary-bg);
    --features-shiny-button-text: var(--master-button-primary-text);
    --features-shiny-button-shimmer: var(--master-brand-accent);
    --features-golden-card-bg: var(--master-card-background);
    --features-golden-card-border: var(--master-card-border);
    --features-golden-card-glow: var(--master-card-glow);

    /* ----- 💰 PRICING PAGE ----- */
    --pricing-hero-title: #F5FAF7;                 /* Light Off-White for title */
    --pricing-hero-subtitle: #F0E6D9;              /* Warm Beige for subtitle */
    --pricing-card-background: #333333;            /* Light Charcoal for cards */
    --pricing-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --pricing-card-glow: #B8956A;                  /* Gold glow effect */
    --pricing-card-title: #F5FAF7;                 /* Light Off-White for card titles */
    --pricing-card-price: #B8956A;                 /* Gold for pricing */
    --pricing-card-text: #F0E6D9;                  /* Warm Beige for card text */
    --pricing-popular-bg: rgba(184, 149, 106, 0.1); /* Subtle gold background */
    --pricing-popular-border: #B8956A;             /* Gold border for popular plan */
    --pricing-shiny-button-bg: #B8956A;            /* Gold button background */
    --pricing-shiny-button-text: #2C3137;          /* Dark text on button */
    --pricing-shiny-button-shimmer: #F5FAF7;       /* Light shimmer */
    --pricing-badge-bg: #7C9A85;                   /* Sage badge background */
    --pricing-badge-text: #F5FAF7;                 /* Light text on badge */

    /* ----- 👥 ABOUT PAGE ----- */
    --about-hero-title: #F5FAF7;                   /* Light Off-White for title */
    --about-hero-subtitle: #F0E6D9;                /* Warm Beige for subtitle */
    --about-card-background: #333333;              /* Light Charcoal for cards */
    --about-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --about-card-glow: #B8956A;                    /* Gold glow effect */
    --about-team-card-bg: #333333;                 /* Light Charcoal for team cards */
    --about-team-card-border: rgba(124, 154, 133, 0.2); /* Very subtle sage border */
    --about-team-name: #F5FAF7;                    /* Light Off-White for names */
    --about-team-role: #7C9A85;                    /* Sage for roles */
    --about-team-bio: #F0E6D9;                     /* Warm Beige for bios */
    --about-shiny-button-bg: #B8956A;              /* Gold button background */
    --about-shiny-button-text: #2C3137;            /* Dark text on button */
    --about-shiny-button-shimmer: #F5FAF7;         /* Light shimmer */
    --about-mission-bg: rgba(51, 51, 51, 0.5);     /* Semi-transparent charcoal */

    /* ----- 📞 CONTACT PAGE ----- */
    --contact-hero-title: #F5FAF7;                 /* Light Off-White for title */
    --contact-hero-subtitle: #F0E6D9;              /* Warm Beige for subtitle */
    --contact-form-bg: #333333;                    /* Light Charcoal for form */
    --contact-form-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --contact-input-bg: rgba(240, 230, 217, 0.1);  /* Very subtle warm background */
    --contact-input-border: #7C9A85;               /* Sage border for inputs */
    --contact-input-text: #F5FAF7;                 /* Light Off-White input text */
    --contact-label-text: #F0E6D9;                 /* Warm Beige for labels */
    --contact-input-focus: #B8956A;                /* Gold focus color */
    --contact-shiny-button-bg: #B8956A;            /* Gold button background */
    --contact-shiny-button-text: #2C3137;          /* Dark text on button */
    --contact-shiny-button-shimmer: #F5FAF7;       /* Light shimmer */

    /* ----- 🧩 GLOBAL COMPONENTS - Light Mode ----- */
    --global-footer-bg: rgba(225, 230, 225, 0.9);  /* Light mode: light sage footer */
    --global-footer-border: rgba(139, 154, 140, 0.3); /* Light mode: medium sage border */
    --global-footer-text: #2e332f;                 /* Light mode: dark charcoal text */
    --global-footer-link: #181b19;                 /* Light mode: very dark charcoal links */
    --global-footer-link-hover: #5f6d61;           /* Light mode: dark sage hover */
    --global-cookie-bg: #f6f7f6;                   /* Light mode: very light sage popup */
    --global-cookie-border: #8b9a8c;               /* Light mode: medium sage border */
    --global-cookie-text: #181b19;                 /* Light mode: very dark charcoal text */
    --global-cookie-button-bg: #5f6d61;            /* Light mode: dark sage button */

    /* ----- 🎨 THEME-AWARE ICON COLORS ----- */
    --icon-primary: #2e332f;                       /* Light mode: dark charcoal for primary icons */
    --icon-secondary: #5f6d61;                     /* Light mode: dark sage for secondary icons */
    --icon-accent: #79887a;                        /* Light mode: medium-dark sage for accent icons */
    --icon-muted: #8b9a8c;                         /* Light mode: medium sage for muted icons */
    --icon-on-dark: #f6f7f6;                       /* Light mode: very light sage for icons on dark backgrounds */
    --global-cookie-button-text: #2C3137;          /* Dark text on cookie button */

    /* ----- 🎨 PROVIDERS PAGE ----- */
    --providers-hero-title: #F5FAF7;               /* Light Off-White for title */
    --providers-hero-subtitle: #F0E6D9;            /* Warm Beige for subtitle */
    --providers-card-bg: #333333;                  /* Light Charcoal for cards */
    --providers-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --providers-card-title: #F5FAF7;               /* Light Off-White for card titles */
    --providers-card-text: #F0E6D9;                /* Warm Beige for card text */
    --providers-icon-color: #7C9A85;               /* Sage for icons */
    --providers-cta-bg: #B8956A;                   /* Gold for CTA buttons */
    --providers-cta-text: #2C3137;                 /* Dark text on CTA */
    --providers-coming-soon-bg: #333333;           /* Light Charcoal for coming soon section */

    /* ----- 📝 APPLY PAGE ----- */
    --apply-hero-title: #F5FAF7;                   /* Light Off-White for title */
    --apply-hero-subtitle: #F0E6D9;                /* Warm Beige for subtitle */
    --apply-form-bg: #333333;                      /* Light Charcoal for form */
    --apply-form-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --apply-progress-active: #7C9A85;              /* Sage for active progress */
    --apply-progress-inactive: #333333;            /* Light Charcoal for inactive */
    --apply-progress-bar: #7C9A85;                 /* Sage for progress bar */
    --apply-step-text: #F5FAF7;                    /* Light Off-White for step text */

    /* Shiny Button Global Controls */
    --global-shiny-button-primary-bg: #B8956A;     /* Gold primary button */
    --global-shiny-button-primary-text: #2C3137;   /* Dark text on primary */
    --global-shiny-button-primary-shimmer: #F5FAF7; /* Light shimmer */
    --global-shiny-button-secondary-bg: transparent; /* Transparent secondary */
    --global-shiny-button-secondary-text: #7C9A85; /* Sage secondary text */
    --global-shiny-button-secondary-shimmer: #7C9A85; /* Sage shimmer */

    /* Shimmer Button Global Controls */
    --global-shimmer-button-primary-bg: #B8956A;   /* Gold primary shimmer button */
    --global-shimmer-button-primary-shimmer: #F5FAF7; /* Light shimmer */
    --global-shimmer-button-secondary-bg: transparent; /* Transparent secondary */
    --global-shimmer-button-secondary-shimmer: #7C9A85; /* Sage shimmer */

    /* Text Shimmer Global Controls */
    --global-text-shimmer-base: #F5FAF7;           /* Light Off-White base */
    --global-text-shimmer-highlight: #B8956A;      /* Gold highlight */

    /* Golden Glowing Card Global Controls */
    --global-golden-card-bg: #333333;              /* Light Charcoal background */
    --global-golden-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --global-golden-card-glow: #B8956A;            /* Gold glow effect */

    /*
    ========================================
    | 🎨 SHADCN UI THEME VARIABLES - Light Mode
    | HSL values that map to our semantic color system
    ========================================
    */
    --background: 44 12% 20%;      /* Dark Charcoal (#2C3137) - Main background */
    --foreground: 120 20% 97%;     /* Light Off-White (#F5FAF7) - Main text */
    --card: 0 0% 20%;              /* Light Charcoal (#333333) - Card backgrounds */
    --card-foreground: 120 20% 97%; /* Light Off-White - Card text */
    --popover: 0 0% 20%;           /* Light Charcoal - Popover backgrounds */
    --popover-foreground: 120 20% 97%; /* Light Off-White - Popover text */
    --primary: 150 15% 52%;        /* Muted Sage Green (#7C9A85) - Primary elements */
    --primary-foreground: 120 20% 97%; /* Light Off-White - Text on primary */
    --secondary: 44 12% 20%;       /* Dark Charcoal - Secondary elements */
    --secondary-foreground: 120 20% 97%; /* Light Off-White - Text on secondary */
    --muted: 44 12% 20%;           /* Dark Charcoal - Muted backgrounds */
    --muted-foreground: 40 20% 90%; /* Warm Beige (#F0E6D9) - Muted text */
    --accent: 40 35% 55%;          /* Muted Gold/Tan (#B8956A) - Accent elements */
    --accent-foreground: 44 12% 20%; /* Dark Charcoal - Text on accent */
    --destructive: 25 95% 53%;     /* Terracotta (#D97706) - Error/destructive */
    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */
    --border: 150 15% 52% / 0.3;   /* Sage with opacity - Borders */
    --input: 150 15% 52% / 0.3;    /* Sage with opacity - Input borders */
    --ring: 40 35% 55%;            /* Muted Gold/Tan - Focus rings */
    --radius: 0.5rem;              /* Border radius */
  }

  .dark {
    /*
    ========================================
    | 🎨 SHADCN UI THEME VARIABLES - Dark Mode
    | Dark theme maintains our current design
    ========================================
    */
    --background: 44 12% 20%;      /* Dark Charcoal - Main background */
    --foreground: 120 20% 97%;     /* Light Off-White - Main text */
    --card: 0 0% 20%;              /* Light Charcoal - Card backgrounds */
    --card-foreground: 120 20% 97%; /* Light Off-White - Card text */
    --popover: 0 0% 20%;           /* Light Charcoal - Popover backgrounds */
    --popover-foreground: 120 20% 97%; /* Light Off-White - Popover text */
    --primary: 150 15% 52%;        /* Muted Sage Green - Primary elements */
    --primary-foreground: 120 20% 97%; /* Light Off-White - Text on primary */
    --secondary: 44 12% 20%;       /* Dark Charcoal - Secondary elements */
    --secondary-foreground: 120 20% 97%; /* Light Off-White - Text on secondary */
    --muted: 44 12% 20%;           /* Dark Charcoal - Muted backgrounds */
    --muted-foreground: 40 20% 90%; /* Warm Beige - Muted text */
    --accent: 40 35% 55%;          /* Muted Gold/Tan - Accent elements */
    --accent-foreground: 44 12% 20%; /* Dark Charcoal - Text on accent */
    --destructive: 25 95% 53%;     /* Terracotta - Error/destructive */
    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */
    --border: 150 15% 52% / 0.3;   /* Sage with opacity - Borders */
    --input: 150 15% 52% / 0.3;    /* Sage with opacity - Input borders */
    --ring: 40 35% 55%;            /* Muted Gold/Tan - Focus rings */

    /* Dark mode master variables */
    --master-brand-secondary: theme('colors.neutral-charcoal-dark');
    --master-text-on-dark: theme('colors.neutral-off-white');
    --master-card-background: theme('colors.neutral-charcoal-light');
  }

  /* Light mode overrides */
  :root:not(.dark) {
    /*
    ========================================
    | 🎨 SHADCN UI THEME VARIABLES - Light Mode
    | Light theme with inverted colors for accessibility
    ========================================
    */
    --background: 120 20% 97%;     /* Light Off-White - Main background */
    --foreground: 44 12% 20%;      /* Dark Charcoal - Main text */
    --card: 0 0% 95%;              /* Very light gray - Card backgrounds */
    --card-foreground: 44 12% 20%; /* Dark Charcoal - Card text */
    --popover: 0 0% 98%;           /* Almost white - Popover backgrounds */
    --popover-foreground: 44 12% 20%; /* Dark Charcoal - Popover text */
    --primary: 150 15% 52%;        /* Muted Sage Green - Primary elements */
    --primary-foreground: 120 20% 97%; /* Light Off-White - Text on primary */
    --secondary: 0 0% 90%;         /* Light gray - Secondary elements */
    --secondary-foreground: 44 12% 20%; /* Dark Charcoal - Text on secondary */
    --muted: 0 0% 95%;             /* Very light gray - Muted backgrounds */
    --muted-foreground: 44 12% 45%; /* Medium charcoal - Muted text */
    --accent: 40 35% 55%;          /* Muted Gold/Tan - Accent elements */
    --accent-foreground: 120 20% 97%; /* Light Off-White - Text on accent */
    --destructive: 25 95% 53%;     /* Terracotta - Error/destructive */
    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */
    --border: 150 15% 52% / 0.2;   /* Sage with opacity - Borders */
    --input: 150 15% 52% / 0.2;    /* Sage with opacity - Input borders */
    --ring: 40 35% 55%;            /* Muted Gold/Tan - Focus rings */

    /* Light mode master variables */
    --master-brand-secondary: theme('colors.neutral-off-white');
    --master-text-on-dark: theme('colors.neutral-charcoal-dark');
    --master-card-background: theme('colors.neutral-off-white');
  }

  /*
  ========================================
  | BASE ELEMENT STYLES
  ========================================
  */
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground font-sans;
    background-color: var(--master-brand-secondary); /* Dark Charcoal primary background */
    color: var(--master-text-on-dark); /* Light Off-White text */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: var(--font-size-body);
  }

  /* Typography Hierarchy - New Visual Identity */
  h1 {
    font-family: var(--font-family-h1);
    font-size: var(--font-size-h1);
    font-weight: 700;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.02em;
  }
  h2 {
    font-family: var(--font-family-h2);
    font-size: var(--font-size-h2);
    font-weight: 700;
    line-height: 1.3;
  }
  h3 {
    font-family: var(--font-family-h3);
    font-size: var(--font-size-h3);
    font-weight: 400;
    line-height: 1.4;
  }
  h4 {
    font-family: var(--font-family-body);
    font-size: var(--font-size-h4);
    font-weight: 700;
    line-height: 1.4;
  }
  h5 {
    font-family: var(--font-family-body);
    font-size: var(--font-size-h5);
    font-weight: 700;
    line-height: 1.5;
  }
  h6 {
    font-family: var(--font-family-body);
    font-size: var(--font-size-body);
    font-weight: 600;
    line-height: 1.5;
  }

  p {
    font-family: var(--font-family-body);
    font-size: var(--font-size-body);
    font-weight: 400;
    line-height: 1.6;
  }

  small, .text-small {
    font-family: var(--font-family-body);
    font-size: var(--font-size-small);
    font-weight: 500;
    line-height: 1.4;
  }

  /* Accent text class for optional signature/handwritten touch */
  .text-accent {
    font-family: var(--font-family-accent);
    font-weight: 400;
  }
}

/*
========================================
| PAGE & COMPONENT-SPECIFIC CLASS OVERRIDES
========================================
*/

/* ----- Home Page ----- */
.page-home .shiny-button {
  background-color: var(--home-shiny-button-bg);
  color: var(--home-shiny-button-text);
  --shimmer-color: var(--home-shiny-button-shimmer);
}

/* Home Page Specific Shiny Button Variants */
.page-home .shiny-button.hero-cta {
  background-color: var(--home-shiny-button-hero-bg);
  color: var(--home-shiny-button-hero-text);
  --shimmer-color: var(--home-shiny-button-hero-shimmer);
}

.page-home .shiny-button.section-cta {
  background-color: var(--home-shiny-button-cta-bg);
  color: var(--home-shiny-button-cta-text);
  --shimmer-color: var(--home-shiny-button-cta-shimmer);
}

.page-home .golden-glowing-card {
  background: var(--home-golden-card-bg);
  border-color: var(--home-golden-card-border);
  --glow-color: var(--home-golden-card-glow);
}

.page-home .text-shimmer {
  --base-color: var(--home-text-shimmer-base);
  --base-gradient-color: var(--home-text-shimmer-highlight);
}

.page-home .word-pull-up {
  color: var(--home-word-pullup-color);
}

.page-home .marquee-animation {
  color: var(--home-marquee-text);
  background: var(--home-marquee-bg);
}

/* ----- Navigation Styles ----- */
.nav-link {
  color: var(--master-header-nav-text);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.nav-link:hover {
  background: var(--master-header-nav-hover-bg);
  color: var(--master-header-nav-text);
}

.nav-link-active {
  background: var(--master-header-nav-active-bg);
  color: var(--master-header-nav-text);
  box-shadow: var(--master-header-nav-active-glow);
}

/* ----- Limelight Navigation Effect ----- */
.limelight-nav {
  position: relative;
  overflow: hidden;
}

.limelight-nav::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, #D97706 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: -1;
  border-radius: 50%;
  opacity: 0;
}

.limelight-nav:hover::before,
.limelight-nav.nav-link-active::before {
  width: 120px;
  height: 120px;
  opacity: 0.3;
}

.limelight-nav:hover,
.limelight-nav.nav-link-active {
  color: #CA8A04;
  text-shadow: 0 0 8px rgba(202, 138, 4, 0.5);
}

/*
========================================
| UTILITIES & ANIMATIONS
========================================
*/
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@keyframes aurora {
  from {
    background-position: 50% 50%, 50% 50%;
  }
  to {
    background-position: 350% 50%, 350% 50%;
  }
}

@keyframes beam {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

/*
========================================
| FIX FOR BACKGROUND ISSUE
| This section is intentionally left blank. The problematic
| `!important` override has been removed.
========================================
*/

/*
========================================
| 🔮 GLASSMORPHISM UTILITY CLASSES
| Consistent glassmorphism effects for UI chrome elements
========================================
*/

.glassmorphism-light {
  background: rgba(245, 250, 247, 0.1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(124, 154, 133, 0.2);
  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.37);
}

.glassmorphism-medium {
  background: rgba(44, 49, 55, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 154, 133, 0.15);
  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.5);
}

.glassmorphism-heavy {
  background: rgba(44, 49, 55, 0.8);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(124, 154, 133, 0.1);
  box-shadow: 0 12px 40px 0 rgba(44, 49, 55, 0.6);
}

.glassmorphism-card {
  background: rgba(51, 51, 51, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(124, 154, 133, 0.1);
  box-shadow: 0 4px 24px 0 rgba(44, 49, 55, 0.3);
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(1px)) {
  .glassmorphism-light,
  .glassmorphism-medium,
  .glassmorphism-heavy,
  .glassmorphism-card {
    background: rgba(44, 49, 55, 0.9);
  }
}

/*
========================================
| 📱 RESPONSIVE DESIGN & MOBILE OPTIMIZATIONS
========================================
*/

/* Mobile-First Responsive Typography */
@media screen and (max-width: 767px) {
  :root {
    /* Override typography scale for mobile */
    --font-size-h1: var(--font-size-h1-mobile);
    --font-size-h2: var(--font-size-h2-mobile);
    --font-size-h3: var(--font-size-h3-mobile);
    --font-size-h4: var(--font-size-h4-mobile);
    --font-size-h5: var(--font-size-h5-mobile);
    --font-size-body: var(--font-size-body-mobile);
    --font-size-small: var(--font-size-small-mobile);

    /* Mobile Header Scaling - Increased by 11.5% from previous 33% reduction */
    --header-height: 3.713rem;       /* Increased from 3.33rem by 11.5% (59.41px) */
    --header-logo-size: 2.23rem;     /* Increased from 2rem by 11.5% (35.68px) */
    --header-nav-font-size: 0.836rem; /* Increased from 0.75rem by 11.5% (13.38px) */
    --header-button-height: 2.23rem; /* Increased from 2rem by 11.5% (35.68px) */
    --header-padding: 0.747rem;      /* Increased from 0.67rem by 11.5% (11.95px) */
  }
}

/* Dark Mode Theme Overrides */
.dark {
  /* Header/Navbar Dark Mode Colors - Proper Glassmorphism */
  --master-header-background: #181b19;                           /* Dark mode: opaque very dark charcoal */
  --master-header-backdrop-blur: 50%;                            /* 50% backdrop blur for proper glassmorphism */
  --master-header-border: rgba(95, 109, 97, 0.2);                /* Dark mode: dark sage border */
  --master-header-logo-bg: rgba(46, 51, 47, 0.9);                /* Dark mode: dark charcoal logo bg */
  --master-header-logo-border: rgba(121, 136, 122, 0.3);         /* Dark mode: medium-dark sage border */
  --master-header-logo-icon: #f6f7f6;                            /* Dark mode: very light sage icon */
  --master-header-brand-text: #e1e6e1;                           /* Dark mode: light sage text */

  /* Footer Dark Mode Colors */
  --global-footer-bg: rgba(46, 51, 47, 0.9);                     /* Dark mode: dark charcoal footer */
  --global-footer-border: rgba(95, 109, 97, 0.3);               /* Dark mode: dark sage border */
  --global-footer-text: #e1e6e1;                                 /* Dark mode: light sage text */
  --global-footer-link: #f6f7f6;                                 /* Dark mode: very light sage links */
  --global-footer-link-hover: #c3ccc3;                           /* Dark mode: medium-light sage hover */
  --global-cookie-bg: #181b19;                                   /* Dark mode: very dark charcoal popup */
  --global-cookie-border: #5f6d61;                               /* Dark mode: dark sage border */
  --global-cookie-text: #f6f7f6;                                 /* Dark mode: very light sage text */
  --global-cookie-button-bg: #79887a;                            /* Dark mode: medium-dark sage button */

  /* ----- 🎨 THEME-AWARE ICON COLORS - Dark Mode ----- */
  --icon-primary: #f6f7f6;                                       /* Dark mode: very light sage for primary icons */
  --icon-secondary: #e1e6e1;                                     /* Dark mode: light sage for secondary icons */
  --icon-accent: #c3ccc3;                                        /* Dark mode: medium-light sage for accent icons */
  --icon-muted: #8b9a8c;                                         /* Dark mode: medium sage for muted icons */
  --icon-on-dark: #2e332f;                                       /* Dark mode: dark charcoal for icons on light backgrounds */

  /* ----- 🌙 AURORA BACKGROUND - Dark Mode Override ----- */
  --aurora-bg: #181b19;                                          /* Very dark charcoal - matches current palette */
  --aurora-bg-dark: #2e332f;                                     /* Dark charcoal for variation */
  --aurora-stripe-light: rgba(246, 247, 246, 0.02);             /* Very subtle light stripes */
  --aurora-stripe-dark: rgba(52, 59, 54, 0.15);                 /* Charcoal sage for contrast */
  --aurora-flow-1: rgba(95, 109, 97, 0.12);                     /* Dark sage - visible but subtle */
  --aurora-flow-2: rgba(74, 87, 75, 0.08);                      /* Darker sage */
  --aurora-flow-3: rgba(62, 71, 63, 0.10);                      /* Very dark sage */
  --aurora-flow-4: rgba(46, 51, 47, 0.18);                      /* Dark charcoal variation */
  --aurora-flow-5: rgba(121, 136, 122, 0.06);                   /* Medium-dark sage - accent */
}

/* Mobile-specific improvements */
@media screen and (max-width: 767px) {
  body {
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Improve touch targets for mobile */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile navigation improvements */
  .nav-link {
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
  }

  /* Mobile card spacing */
  .golden-glowing-card-container {
    margin: 0.75rem;
  }

  /* Mobile hero text adjustments */
  .hero-title {
    font-size: 2.5rem !important;
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: 1.125rem !important;
    line-height: 1.5;
  }
}

/* Tablet Responsive Design */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Tablet-specific grid adjustments */
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop Responsive Design */
@media screen and (min-width: 1024px) {
  .container {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  /* Desktop-specific grid adjustments */
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

/*
========================================
| 🤖 ANDROID PERFORMANCE OPTIMIZATIONS
| Fixes for flickering and performance issues on Android devices
========================================
*/

/* Hardware acceleration for better performance */
.aurora-background,
.backdrop-blur-lg,
.backdrop-blur-md,
.backdrop-blur-sm {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

/* Reduce backdrop-filter complexity on Android */
@media screen and (max-width: 768px) {
  .backdrop-blur-lg {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }
}

/* Prevent flickering during animations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Optimize aurora background for mobile */
@media screen and (max-width: 768px) {
  .aurora-background {
    opacity: 0.7;
    filter: blur(8px);
  }

  .aurora-background::after {
    animation-duration: 120s; /* Slower animation for better performance */
  }
}

/*
========================================
| 📱 MOBILE & SAFARI SPECIFIC FIXES
========================================
*/
@media screen and (orientation: landscape) {
  html, body {
    min-height: 100lvh !important;
  }
}

@supports (padding: max(0px)) {
  html {
    padding-top: max(var(--safe-area-inset-top), 0px);
    padding-left: max(var(--safe-area-inset-left), 0px);
    padding-right: max(var(--safe-area-inset-right), 0px);
    padding-bottom: max(var(--safe-area-inset-bottom), 0px);
  }
}

@supports (-webkit-touch-callout: none) {
  html, body {
    height: -webkit-fill-available;
  }
}
