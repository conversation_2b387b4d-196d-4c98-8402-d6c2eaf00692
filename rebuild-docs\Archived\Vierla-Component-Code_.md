

# **Vierla: Complete Component Implementation Guide**

**Objective:** This is the definitive guide for implementing all UI components for the Vierla website rebuild. It contains the finalized component selections, newly identified components to enhance the user experience, and detailed implementation instructions to ensure a polished, functional, and visually consistent result.

---

### **Part 1: Foundational Marketing Components**

These components form the core of Vierla's marketing pages and user-facing interactions.

#### **1.1. Global Background: Background Beams**

* **Source:** kokonutd/beams-background  
* **Purpose & Strategic Fit:** Provides a subtle, elegant, and dynamic background that reinforces the modern, tech-forward brand identity without being distracting. It's a professional alternative to a static background.  
* **File Path:** components/ui/background-beams.tsx  
* **Implementation Specifics:**  
  * This component should be placed at the top level of your main layout file (app/layout.tsx) or page file (app/page.tsx).  
  * It must have a negative z-index (e.g., z-\[-1\]) to ensure it sits behind all other page content.  
  * The animation requires keyframes to be defined in your global CSS file.

**Component Code:**

TypeScript

// File: components/ui/background-beams.tsx  
"use client";  
import React from "react";  
import { motion } from "framer-motion";  
import { cn } from "@/lib/utils";

export const BackgroundBeams \= ({ className }: { className?: string }) \=\> {  
  return (  
    \<div  
      className={cn(  
        "absolute top-0 left-0 w-full h-full \-z-10 overflow-hidden",  
        className  
      )}  
    \>  
      {/\* Simplified representation. Ensure full SVG/div structure from source is used if available. \*/}  
      \<div className="absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-primary to-transparent animate-beam" style={{ animationDelay: '0s', animationDuration: '8s' }}\>\</div\>  
      \<div className="absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-primary to-transparent animate-beam" style={{ animationDelay: '2s', animationDuration: '10s' }}\>\</div\>  
      \<div className="absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-primary to-transparent animate-beam" style={{ animationDelay: '1s', animationDuration: '9s' }}\>\</div\>  
    \</div\>  
  );  
};

**Required CSS (add to app/globals.css):**

CSS

@keyframes beam {  
  0% {  
    transform: translateY(-100%);  
  }  
  100% {  
    transform: translateY(100%);  
  }  
}

.animate-beam {  
  animation: beam linear infinite;  
}

---

#### **1.2. Navigation Bar**

* **Source:** shadcnblockscom/shadcnblocks-com-navbar1  
* **Purpose & Strategic Fit:** A clean, professional, and responsive navigation bar that establishes trust and provides clear user orientation. Its standard layout is familiar to SaaS users.  
* **File Path:** components/marketing/navbar.tsx  
* **Implementation Specifics:**  
  * Place this component in the root layout (app/layout.tsx) to ensure it appears on every page.  
  * The primary "Get Started" button should be replaced with the \<ShinyButton /\> component for visual emphasis.  
  * Populate the mobile menu inside \<SheetContent\> for a consistent experience on smaller devices.

**Component Code:**

TypeScript

// File: components/marketing/navbar.tsx  
import Link from "next/link";  
import { Button } from "@/components/ui/button";  
import ShinyButton from "@/components/ui/shiny-button";  
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";  
import { Menu } from "lucide-react";

export function Navbar() {  
  const navLinks \= \[  
    { href: "/features", label: "Features" },  
    { href: "/pricing", label: "Pricing" },  
    { href: "/about", label: "About" },  
  \];

  return (  
    \<header className="flex h-20 w-full items-center justify-between px-4 md:px-6 bg-transparent absolute top-0 left-0 z-50"\>  
      \<Link href="/" className="flex items-center gap-2" prefetch={false}\>  
        \<span className="text-lg font-semibold"\>Vierla\</span\>  
      \</Link\>  
      \<nav className="hidden items-center gap-6 text-sm font-medium md:flex"\>  
        {navLinks.map((link) \=\> (  
          \<Link key={link.href} href={link.href} className="hover:underline underline-offset-4" prefetch={false}\>  
            {link.label}  
          \</Link\>  
        ))}  
      \</nav\>  
      \<div className="hidden items-center gap-4 md:flex"\>  
        \<Button variant="ghost"\>Login\</Button\>  
        \<Link href="/apply"\>\<ShinyButton\>Get Started\</ShinyButton\>\</Link\>  
      \</div\>  
      \<Sheet\>  
        \<SheetTrigger asChild\>  
          \<Button variant="outline" size="icon" className="md:hidden"\>  
            \<Menu className="h-6 w-6" /\>  
            \<span className="sr-only"\>Toggle navigation menu\</span\>  
          \</Button\>  
        \</SheetTrigger\>  
        \<SheetContent side="right"\>  
          \<div className="grid gap-4 p-4"\>  
            {navLinks.map((link) \=\> (  
              \<Link key={link.href} href={link.href} className="font-medium hover:underline underline-offset-4" prefetch={false}\>  
                {link.label}  
              \</Link\>  
            ))}  
            \<div className="flex flex-col gap-2 mt-4"\>  
              \<Button variant="ghost" className="w-full"\>Login\</Button\>  
              \<Link href="/apply"\>\<ShinyButton className="w-full"\>Get Started\</ShinyButton\>\</Link\>  
            \</div\>  
          \</div\>  
        \</SheetContent\>  
      \</Sheet\>  
    \</header\>  
  );  
}

---

#### **1.3. Card Effects: Spotlight Card & Glowing Border**

* **Sources:** easemize/spotlight-card and aceternity/glowing-effect  
* **Purpose & Strategic Fit:** These effects elevate standard content cards into engaging, interactive elements. The SpotlightCard provides a subtle mouse-following glow, perfect for feature grids, while the GlowingBorder adds a premium feel on hover. Using them together creates a rich, layered interaction.  
* **File Paths:** components/ui/spotlight-card.tsx, components/ui/glowing-border.tsx  
* **Implementation Specifics:**  
  * These are wrapper components. You will wrap your standard shadcn/ui \<Card\> component first with \<SpotlightCard\> and then with \<GlowingBorder\>.  
  * The parent element must have the group class for the hover effects to trigger.  
  * The glow colors are dynamically set via the \--spotlight-color and \--glow-color CSS variables, which should be defined in your global styles to match your theme.

**Component Code:**

TypeScript

// File: components/ui/spotlight-card.tsx  
"use client";  
import React, { useRef, useState, useEffect } from "react";  
import { motion } from "framer-motion";  
import { cn } from "@/lib/utils";

export const SpotlightCard \= ({  
  children,  
  className,  
}: {  
  children: React.ReactNode;  
  className?: string;  
}) \=\> {  
  const ref \= useRef\<HTMLDivElement\>(null);  
  const \[isFocused, setIsFocused\] \= useState(false);  
  const \[position, setPosition\] \= useState({ x: 0, y: 0 });  
  const \[opacity, setOpacity\] \= useState(0);

  const handleMouseMove \= (e: React.MouseEvent\<HTMLDivElement\>) \=\> {  
    if (\!ref.current |

| isFocused) return;  
    const div \= ref.current;  
    const rect \= div.getBoundingClientRect();  
    setPosition({ x: e.clientX \- rect.left, y: e.clientY \- rect.top });  
  };

  const handleFocus \= () \=\> {  
    setIsFocused(true);  
    setOpacity(1);  
  };

  const handleBlur \= () \=\> {  
    setIsFocused(false);  
    setOpacity(0);  
  };

  const handleMouseEnter \= () \=\> setOpacity(1);  
  const handleMouseLeave \= () \=\> setOpacity(0);

  return (  
    \<div  
      ref={ref}  
      onMouseMove={handleMouseMove}  
      onFocus={handleFocus}  
      onBlur={handleBlur}  
      onMouseEnter={handleMouseEnter}  
      onMouseLeave={handleMouseLeave}  
      className={cn("relative w-full p-px rounded-lg overflow-hidden", className)}  
    \>  
      \<div className="absolute bottom-0 left-0 w-full h-full rounded-lg" /\>  
      \<motion.div  
        className="pointer-events-none absolute \-inset-px rounded-lg opacity-0 transition-opacity duration-500"  
        style={{  
          opacity,  
          background: \`radial-gradient(600px circle at ${position.x}px ${position.y}px, hsl(var(--spotlight-color)), transparent 40%)\`,  
        }}  
      /\>  
      {children}  
    \</div\>  
  );  
};

// File: components/ui/glowing-border.tsx  
"use client";  
import React from "react";  
import { cn } from "@/lib/utils";

export const GlowingBorder \= ({  
  children,  
  className,  
  borderWidth \= "1px",  
}: {  
  children: React.ReactNode;  
  className?: string;  
  borderWidth?: string;  
}) \=\> {  
  return (  
    \<div className={cn("relative w-full h-full", className)}\>  
      \<div  
        className="absolute \-inset-px rounded-lg bg-gradient-to-r from-primary/50 to-secondary/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"  
        style={{ borderWidth }}  
      /\>  
      \<div className="relative w-full h-full bg-card rounded-\[7px\]"\>  
        {children}  
      \</div\>  
    \</div\>  
  );  
};

---

#### **1.4. Button Upgrade: Shiny Button**

* **Source:** dillionverma/shiny-button  
* **Purpose & Strategic Fit:** Makes primary calls-to-action impossible to miss. The subtle shimmer adds a premium, "magical" feel that aligns with the AI-powered brand identity and encourages clicks.  
* **File Path:** components/ui/shiny-button.tsx  
* **Implementation Specifics:**  
  * Use this component in place of the standard Button for your most important CTAs (e.g., "Get Started", "Sign Up").  
  * The animation uses a conic-gradient. Customize the colors within the bg-\[conic-gradient(...)\] class to match your brand's primary and secondary colors.

**Component Code:**

TypeScript

// File: components/ui/shiny-button.tsx  
import { cn } from "@/lib/utils";  
import React from "react";

const ShinyButton \= React.forwardRef\<  
  HTMLButtonElement,  
  { className?: string; children: React.ReactNode }  
\>(({ className, children,...props }, ref) \=\> {  
  return (  
    \<button  
      className={cn(  
        "relative inline-flex h-12 overflow-hidden rounded-full p-\[1px\] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50",  
        className  
      )}  
      ref={ref}  
      {...props}  
    \>  
      \<span className="absolute inset-\[-1000%\] animate-\[spin\_2s\_linear\_infinite\] bg-" /\>  
      \<span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-card px-6 py-1 text-sm font-medium text-card-foreground backdrop-blur-3xl"\>  
        {children}  
      \</span\>  
    \</button\>  
  );  
});  
ShinyButton.displayName \= "ShinyButton";

export default ShinyButton;

---

#### **1.5. Application Form: Multistep Form**

* **Source:** arihantcodes/multistep-form  
* **Purpose & Strategic Fit:** Breaks down a potentially long sign-up process into manageable steps. This reduces user friction and cognitive load, directly leading to higher form completion rates.  
* **File Path:** components/marketing/multistep-form.tsx  
* **Implementation Specifics:**  
  * This component requires framer-motion for animations.  
  * **Crucially**, replace the default HTML \<input\>, \<label\>, and \<button\> elements with your project's styled shadcn/ui components (\<Input\>, \<Label\>, \<Button\>) for visual consistency.  
  * The form submission logic in the handleSubmit function must be wired to your backend API endpoint.

**Component Code:**

TypeScript

// File: components/marketing/multistep-form.tsx  
"use client";  
import { useState } from "react";  
import { motion, AnimatePresence } from "framer-motion";  
import { Button } from "@/components/ui/button";  
import { Input } from "@/components/ui/input";  
import { Label } from "@/components/ui/label";

// Define steps and fields  
const steps \= },  
  { id: 2, name: "Company Information", fields: },  
  { id: 3, name: "Finalize", fields: },  
\];

export function MultiStepForm() {  
  const \= useState(0);  
  const \= useState({});

  const next \= () \=\> setCurrentStep((prev) \=\> (prev \< steps.length \- 1? prev \+ 1 : prev));  
  const prev \= () \=\> setCurrentStep((prev) \=\> (prev \> 0? prev \- 1 : prev));

  const handleSubmit \= (e: React.FormEvent) \=\> {  
    e.preventDefault();  
    // Backend submission logic goes here  
    alert("Form submitted: " \+ JSON.stringify(formData));  
  };

  return (  
    \<div className="w-full max-w-xl p-8 space-y-6 bg-card text-card-foreground rounded-lg shadow-md border"\>  
      \<AnimatePresence mode="wait"\>  
        \<motion.div  
          key={currentStep}  
          initial={{ x: 300, opacity: 0 }}  
          animate={{ x: 0, opacity: 1 }}  
          exit={{ x: \-300, opacity: 0 }}  
          transition={{ duration: 0.3 }}  
        \>  
          \<h2 className="text-2xl font-bold mb-6"\>{steps.name}\</h2\>  
          {/\* Render form fields based on currentStep \*/}  
        \</motion.div\>  
      \</AnimatePresence\>  
      \<div className="flex justify-between pt-4"\>  
        \<Button onClick={prev} variant="outline" disabled={currentStep \=== 0}\>Back\</Button\>  
        {currentStep \< steps.length \- 1? (  
          \<Button onClick={next}\>Next\</Button\>  
        ) : (  
          \<Button onClick={handleSubmit}\>Create Account\</Button\>  
        )}  
      \</div\>  
    \</div\>  
  );  
}

---

#### **1.6. Footer**

* **Source:** mdadul/footer  
* **Purpose & Strategic Fit:** Provides a professional, comprehensive end-cap for every page. It's crucial for user navigation, trust signals (legal links), and SEO.  
* **File Path:** components/marketing/footer.tsx  
* **Implementation Specifics:**  
  * Place in the root layout (app/layout.tsx).  
  * It uses semantic colors from shadcn/ui (background, foreground, muted-foreground) and will adapt to your theme automatically.  
  * Update the links to match your site's final page structure.

**Component Code:**

TypeScript

// File: components/marketing/footer.tsx  
import React from "react";  
import Link from "next/link";

export function Footer() {  
  return (  
    \<footer className="bg-background border-t text-foreground py-12"\>  
      \<div className="container mx-auto px-4"\>  
        \<div className="grid grid-cols-2 md:grid-cols-4 gap-8"\>  
          \<div\>  
            \<h2 className="text-xl font-bold mb-4"\>Vierla\</h2\>  
            \<p className="text-muted-foreground text-sm"\>The AI-Powered Platform for Modern Business.\</p\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Product\</h3\>  
            \<ul className="space-y-2 text-sm"\>  
              \<li\>\<Link href="/features" className="text-muted-foreground hover:text-foreground"\>Features\</Link\>\</li\>  
              \<li\>\<Link href="/pricing" className="text-muted-foreground hover:text-foreground"\>Pricing\</Link\>\</li\>  
            \</ul\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Company\</h3\>  
            \<ul className="space-y-2 text-sm"\>  
              \<li\>\<Link href="/about" className="text-muted-foreground hover:text-foreground"\>About Us\</Link\>\</li\>  
              \<li\>\<Link href="/contact" className="text-muted-foreground hover:text-foreground"\>Contact\</Link\>\</li\>  
            \</ul\>  
          \</div\>  
          \<div\>  
            \<h3 className="font-semibold mb-4"\>Legal\</h3\>  
            \<ul className="space-y-2 text-sm"\>  
              \<li\>\<Link href="/privacy" className="text-muted-foreground hover:text-foreground"\>Privacy Policy\</Link\>\</li\>  
              \<li\>\<Link href="/terms" className="text-muted-foreground hover:text-foreground"\>Terms of Service\</Link\>\</li\>  
            \</ul\>  
          \</div\>  
        \</div\>  
        \<div className="mt-10 border-t border-border pt-8 text-center text-muted-foreground text-xs"\>  
          \<p\>© {new Date().getFullYear()} Vierla, Inc. All rights reserved.\</p\>  
        \</div\>  
      \</div\>  
    \</footer\>  
  );  
}

---

### **Part 2: Newly Added & Recommended Components**

These components were selected to further enhance the site's visual appeal and interactivity.

#### **2.1. Star Border**

* **Source:** reactbits/star-border  
* **Purpose & Strategic Fit:** A unique and eye-catching border effect that can be used to highlight special offers, premium feature cards, or testimonials. It adds a touch of delight and draws attention to key elements.  
* **File Path:** components/ui/star-border.tsx  
* **Implementation Specifics:**  
  * This is a wrapper component. Place the content you want to be bordered inside it.  
  * The animation is controlled via CSS variables (--star-color, \--star-thickness, \--star-speed) that can be customized via props.

**Component Code:**

TypeScript

// File: components/ui/star-border.tsx  
"use client";  
import React from "react";  
import { cn } from "@/lib/utils";

export const StarBorder \= ({  
  children,  
  className,  
  color \= "\#FFFFFF",  
  thickness \= "1px",  
  speed \= "5s",  
}: {  
  children: React.ReactNode;  
  className?: string;  
  color?: string;  
  thickness?: string;  
  speed?: string;  
}) \=\> {  
  const id \= React.useId();  
  return (  
    \<div  
      className={cn("relative w-full h-full p-4", className)}  
      style={{  
        // @ts-ignore  
        "--star-color": color,  
        "--star-thickness": thickness,  
        "--star-speed": speed,  
      }}  
    \>  
      \<div  
        className="absolute inset-0"  
        style={{  
          maskImage: \`url(\#${id})\`,  
          WebkitMaskImage: \`url(\#${id})\`,  
          maskSize: "cover",  
          WebkitMaskSize: "cover",  
        }}  
      \>  
        \<div className="absolute inset-0 bg-\[conic-gradient(from\_90deg\_at\_50%\_50%,var(--star-color)\_0%,transparent\_50%,var(--star-color)\_100%)\] animate-spin \[animation-duration:var(--star-speed)\]" /\>  
      \</div\>  
      \<svg width="0" height="0"\>  
        \<defs\>  
          \<mask id={id}\>  
            \<rect width="100%" height="100%" fill="white" /\>  
            \<rect width="100%" height="100%" fill="black" stroke="black" strokeWidth={thickness} rx="12" ry="12" /\>  
          \</mask\>  
        \</defs\>  
      \</svg\>  
      {children}  
    \</div\>  
  );  
};

---

#### **2.2. Animated Testimonials**

* **Source:** aceternity/animated-testimonials  
* **Purpose & Strategic Fit:** A dynamic and engaging way to display social proof. Auto-scrolling testimonials create a sense of vibrant customer satisfaction and are more likely to be noticed than static text blocks, building trust effectively.  
* **File Path:** components/marketing/animated-testimonials.tsx  
* **Implementation Specifics:**  
  * Requires framer-motion.  
  * The component takes an array of testimonial objects as a prop. Each object needs a quote, name, designation, and image src.  
  * The autoplay prop can be set to true to automatically cycle through testimonials.

**Component Code:**

TypeScript

// File: components/marketing/animated-testimonials.tsx  
"use client";  
import { cn } from "@/lib/utils";  
import { AnimatePresence, motion } from "framer-motion";  
import Image from "next/image";  
import { useEffect, useState } from "react";

type Testimonial \= {  
  quote: string;  
  name: string;  
  designation: string;  
  src: string;  
};

export const AnimatedTestimonials \= ({  
  testimonials,  
  autoplay \= true,  
  className,  
}: {  
  testimonials: Testimonial;  
  autoplay?: boolean;  
  className?: string;  
}) \=\> {  
  const \[active, setActive\] \= useState(0);

  const handleNext \= () \=\> {  
    setActive((prev) \=\> (prev \+ 1\) % testimonials.length);  
  };

  useEffect(() \=\> {  
    if (\!autoplay) return;  
    const interval \= setInterval(handleNext, 5000);  
    return () \=\> clearInterval(interval);  
  }, \[autoplay, testimonials.length\]);

  return (  
    \<div className={cn("relative w-full max-w-3xl mx-auto h-96", className)}\>  
      \<AnimatePresence mode="wait"\>  
        \<motion.div  
          key={active}  
          className="w-full h-full"  
          initial={{ opacity: 0, y: 20 }}  
          animate={{ opacity: 1, y: 0 }}  
          exit={{ opacity: 0, y: \-20 }}  
          transition={{ duration: 0.5 }}  
        \>  
          \<div className="flex flex-col items-center text-center"\>  
            \<Image  
              src={testimonials\[active\].src}  
              alt={testimonials\[active\].name}  
              width={80}  
              height={80}  
              className="rounded-full object-cover mb-4"  
            /\>  
            \<p className="text-lg italic text-muted-foreground"\>  
              "{testimonials\[active\].quote}"  
            \</p\>  
            \<p className="mt-4 font-semibold text-foreground"\>  
              {testimonials\[active\].name}  
            \</p\>  
            \<p className="text-sm text-muted-foreground"\>  
              {testimonials\[active\].designation}  
            \</p\>  
          \</div\>  
        \</motion.div\>  
      \</AnimatePresence\>  
    \</div\>  
  );  
};  
