export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: { [key: string]: string };
}

export function validateField(value: any, rules: ValidationRule): string | null {
  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return 'This field is required';
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }

  // String-specific validations
  if (typeof value === 'string') {
    // Min length validation
    if (rules.minLength && value.length < rules.minLength) {
      return `Must be at least ${rules.minLength} characters`;
    }

    // Max length validation
    if (rules.maxLength && value.length > rules.maxLength) {
      return `Must be no more than ${rules.maxLength} characters`;
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      return 'Invalid format';
    }
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return null;
}

export function validateForm(data: { [key: string]: any }, schema: ValidationSchema): ValidationResult {
  const errors: { [key: string]: string } = {};

  for (const [field, rules] of Object.entries(schema)) {
    const error = validateField(data[field], rules);
    if (error) {
      errors[field] = error;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// Common validation patterns
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphabetic: /^[a-zA-Z\s]+$/,
  numeric: /^\d+$/,
  postalCode: /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
};

// Common validation schemas
export const contactFormSchema: ValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: patterns.alphabetic
  },
  email: {
    required: true,
    pattern: patterns.email,
    maxLength: 255
  },
  subject: {
    required: true,
    minLength: 5,
    maxLength: 200
  },
  message: {
    required: true,
    minLength: 10,
    maxLength: 2000
  },
  type: {
    required: true,
    custom: (value) => {
      const validTypes = ['general', 'customer', 'professional', 'partnership', 'support'];
      return validTypes.includes(value) ? null : 'Invalid contact type';
    }
  }
};

export const applyFormSchema: ValidationSchema = {
  businessName: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  ownerName: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: patterns.alphabetic
  },
  email: {
    required: true,
    pattern: patterns.email,
    maxLength: 255
  },
  phone: {
    required: true,
    pattern: patterns.phone
  },
  businessType: {
    required: true,
    custom: (value) => {
      const validTypes = ['salon', 'spa', 'barbershop', 'beauty_clinic', 'wellness_center', 'other'];
      return validTypes.includes(value) ? null : 'Invalid business type';
    }
  },
  location: {
    required: true,
    minLength: 5,
    maxLength: 200
  },
  experience: {
    required: true,
    custom: (value) => {
      const validExperience = ['0-1', '1-3', '3-5', '5-10', '10+'];
      return validExperience.includes(value) ? null : 'Invalid experience level';
    }
  },
  services: {
    required: true,
    custom: (value) => {
      if (!Array.isArray(value) || value.length === 0) {
        return 'Please select at least one service';
      }
      return null;
    }
  },
  description: {
    required: true,
    minLength: 50,
    maxLength: 1000
  }
};

// Utility function to sanitize input
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
}

// Rate limiting utility
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();

  isAllowed(identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get existing attempts for this identifier
    const existingAttempts = this.attempts.get(identifier) || [];
    
    // Filter out attempts outside the window
    const recentAttempts = existingAttempts.filter(time => time > windowStart);
    
    // Check if under the limit
    if (recentAttempts.length >= maxAttempts) {
      return false;
    }
    
    // Add current attempt
    recentAttempts.push(now);
    this.attempts.set(identifier, recentAttempts);
    
    return true;
  }

  getRemainingTime(identifier: string, windowMs: number = 15 * 60 * 1000): number {
    const attempts = this.attempts.get(identifier) || [];
    if (attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const windowEnd = oldestAttempt + windowMs;
    const now = Date.now();
    
    return Math.max(0, windowEnd - now);
  }
}

export const rateLimiter = new RateLimiter();
