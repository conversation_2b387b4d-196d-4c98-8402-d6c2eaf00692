{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shimmer-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n}\n\nconst ShimmerButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor,\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background,\n      className,\n      children,\n      size = \"md\",\n      variant = \"primary\",\n      ...props\n    },\n    ref,\n  ) => {\n    // Size-based styling\n    const sizeClasses = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-base\",\n      lg: \"px-8 py-4 text-lg\"\n    };\n\n    // Color variants with customization support\n    const getColors = () => {\n      const baseColors = {\n        primary: {\n          bg: background || \"var(--master-button-primary-bg)\",\n          shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        },\n        secondary: {\n          bg: background || \"var(--master-button-secondary-bg)\",\n          shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        },\n        accent: {\n          bg: background || \"var(--master-brand-accent)\",\n          shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        }\n      };\n      return baseColors[variant];\n    };\n\n    const colors = getColors();\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": colors.shimmer,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": colors.bg,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          sizeClasses[size],\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* spark container */}\n        <div\n          className={cn(\n            \"-z-30 blur-[2px]\",\n            \"absolute inset-0 overflow-visible [container-type:size]\",\n          )}\n        >\n          {/* spark */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            {/* spark before */}\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n        {children}\n\n        {/* Highlight */}\n        <div\n          className={cn(\n            \"insert-0 absolute size-full\",\n            \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            // transition\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            // on hover\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            // on click\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n          )}\n        />\n\n        {/* backdrop */}\n        <div\n          className={cn(\n            \"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\",\n          )}\n        />\n      </button>\n    );\n  },\n);\n\nShimmerButton.displayName = \"ShimmerButton\";\n\nexport { ShimmerButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACpC,CACE,EACE,YAAY,EACZ,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,GAAG,OACJ,EACD;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4CAA4C;IAC5C,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,WAAW;gBACT,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,QAAQ;gBACN,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB,OAAO,OAAO;YACjC,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ,OAAO,EAAE;QACnB;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+MACA,qFACA,WAAW,CAAC,KAAK,EACjB;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oBACA;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;YAGlB;0BAGD,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+BACA,oFACA,aAAa;gBACb,yDACA,WAAW;gBACX,oDACA,WAAW;gBACX;;;;;;0BAKJ,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKV;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/get-started-button.tsx"], "sourcesContent": ["import { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface GetStartedButtonProps {\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  onClick?: () => void;\n  href?: string;\n}\n\nexport function GetStartedButton({\n  className,\n  size = \"lg\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  onClick,\n  href,\n}: GetStartedButtonProps) {\n  return (\n    <ShimmerButton\n      className={cn(\"group relative overflow-hidden\", className)}\n      size={size}\n      variant={variant}\n      shimmerColor={shimmerColor}\n      background={backgroundColor}\n      onClick={onClick}\n    >\n      <span className=\"mr-8 transition-opacity duration-500 group-hover:opacity-0\">\n        Get Started\n      </span>\n      <i className=\"absolute right-0.5 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-white/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95\">\n        <ChevronRight size={16} strokeWidth={2} aria-hidden=\"true\" className=\"text-white\" />\n      </i>\n    </ShimmerButton>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAYO,SAAS,iBAAiB,EAC/B,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,OAAO,EACP,IAAI,EACkB;IACtB,qBACE,8OAAC,sIAAA,CAAA,gBAAa;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAChD,MAAM;QACN,SAAS;QACT,cAAc;QACd,YAAY;QACZ,SAAS;;0BAET,8OAAC;gBAAK,WAAU;0BAA6D;;;;;;0BAG7E,8OAAC;gBAAE,WAAU;0BACX,cAAA,8OAAC,sNAAA,CAAA,eAAY;oBAAC,MAAM;oBAAI,aAAa;oBAAG,eAAY;oBAAO,WAAU;;;;;;;;;;;;;;;;;AAI7E", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,qMAAA,CAAA,aAAgB,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,qMAAA,CAAA,aAAgB,CAGnC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,qMAAA,CAAA,aAAgB,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/heart-icon.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface HeartIconProps {\n  className?: string\n  style?: React.CSSProperties\n}\n\nexport const HeartIcon: React.FC<HeartIconProps> = ({ className = \"w-6 h-6\", style }) => {\n  return (\n    <svg\n      className={className}\n      style={style}\n      viewBox=\"0 0 24 24\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n    </svg>\n  )\n}\n\nexport default HeartIcon\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,YAAsC,CAAC,EAAE,YAAY,SAAS,EAAE,KAAK,EAAE;IAClF,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { GetStartedButton } from \"@/components/ui/get-started-button\";\nimport { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON>rigger, SheetTitle } from \"@/components/ui/sheet\";\nimport { Menu } from \"lucide-react\";\nimport { HeartIcon } from \"@/components/ui/heart-icon\";\nimport { ThemeSwitcher } from \"@/components/ui/theme-switcher\";\n\nexport const Navbar = React.memo(function Navbar() {\n  const pathname = usePathname();\n\n  // Simple function to check if a link is active\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"flex h-20 w-full items-center justify-between px-4 md:px-6 absolute top-0 left-0 z-50 border-b\" style={{\n      background: 'var(--master-header-background)',\n      borderColor: 'var(--master-header-border)',\n      backdropFilter: `blur(var(--master-header-backdrop-blur))`\n    }}>\n      <Link href=\"/\" className=\"flex items-center gap-3 group\" prefetch={false}>\n        <div className=\"w-10 h-10 md:w-12 md:h-12 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-all duration-300\" style={{\n          background: 'var(--master-header-logo-bg)',\n          borderColor: 'var(--master-header-logo-border)',\n          border: '1px solid'\n        }}>\n          <HeartIcon className=\"w-5 h-5 md:w-7 md:h-7\" style={{ color: 'var(--master-header-logo-icon)' }} />\n        </div>\n        <span className=\"text-3xl md:text-4xl font-bold drop-shadow-lg font-playfair-display\" style={{ color: 'var(--master-header-brand-text)' }}>Vierla</span>\n      </Link>\n      <div className=\"hidden items-center gap-6 text-sm font-medium md:flex\">\n        <nav className=\"flex items-center gap-6 mr-4\">\n          <Link\n            href=\"/\"\n            className={`nav-link limelight-nav ${isActive('/') ? 'nav-link-active' : ''}`}\n          >\n            Home\n          </Link>\n          <Link\n            href=\"/features\"\n            className={`nav-link limelight-nav ${isActive('/features') ? 'nav-link-active' : ''}`}\n          >\n            Features\n          </Link>\n          <Link\n            href=\"/pricing\"\n            className={`nav-link limelight-nav ${isActive('/pricing') ? 'nav-link-active' : ''}`}\n          >\n            Pricing\n          </Link>\n          <Link\n            href=\"/about\"\n            className={`nav-link ${isActive('/about') ? 'nav-link-active' : ''}`}\n          >\n            About\n          </Link>\n          <Link\n            href=\"/contact\"\n            className={`nav-link ${isActive('/contact') ? 'nav-link-active' : ''}`}\n          >\n            Contact\n          </Link>\n        </nav>\n        <Link href=\"/apply\">\n          <GetStartedButton size=\"sm\" />\n        </Link>\n      </div>\n      <Sheet>\n        <SheetTrigger asChild>\n          <Button variant=\"outline\" size=\"icon\" className=\"md:hidden border-brand-sage/30 bg-transparent hover:bg-brand-sage/10 transition-colors\">\n            <Menu className=\"h-6 w-6 text-neutral-off-white\" />\n            <span className=\"sr-only\">Toggle navigation menu</span>\n          </Button>\n        </SheetTrigger>\n        <SheetContent side=\"right\" className=\"bg-neutral-charcoal-dark/95 backdrop-blur-xl border-brand-sage/20 w-[300px] sm:w-[350px]\">\n          <SheetTitle className=\"sr-only\">Navigation Menu</SheetTitle>\n          <div className=\"flex flex-col space-y-6 mt-8\">\n            {/* Mobile Logo */}\n            <div className=\"flex items-center gap-3 pb-4 border-b border-brand-sage/20\">\n              <HeartIcon className=\"w-8 h-8 text-brand-sage\" />\n              <span className=\"text-2xl font-bold text-neutral-off-white font-tai-heritage\">Vierla</span>\n            </div>\n\n            {/* Navigation Links */}\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/\"\n                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${\n                  isActive('/')\n                    ? 'text-brand-gold bg-brand-gold/10'\n                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'\n                }`}\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/features\"\n                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${\n                  isActive('/features')\n                    ? 'text-brand-gold bg-brand-gold/10'\n                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'\n                }`}\n              >\n                Features\n              </Link>\n              <Link\n                href=\"/pricing\"\n                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${\n                  isActive('/pricing')\n                    ? 'text-brand-gold bg-brand-gold/10'\n                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'\n                }`}\n              >\n                Pricing\n              </Link>\n              <Link\n                href=\"/about\"\n                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${\n                  isActive('/about')\n                    ? 'text-brand-gold bg-brand-gold/10'\n                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'\n                }`}\n              >\n                About\n              </Link>\n              <Link\n                href=\"/contact\"\n                className={`text-lg font-medium transition-colors py-2 px-3 rounded-lg ${\n                  isActive('/contact')\n                    ? 'text-brand-gold bg-brand-gold/10'\n                    : 'text-neutral-off-white hover:text-brand-gold hover:bg-brand-gold/5'\n                }`}\n              >\n                Contact\n              </Link>\n            </div>\n\n            {/* CTA Button */}\n            <div className=\"pt-6 border-t border-brand-sage/20\">\n              <Link href=\"/apply\" className=\"block\">\n                <GetStartedButton className=\"w-full text-lg py-3\" />\n              </Link>\n            </div>\n          </div>\n        </SheetContent>\n      </Sheet>\n    </header>\n  );\n});\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYO,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,SAAS;IACxC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;QAAiG,OAAO;YACxH,YAAY;YACZ,aAAa;YACb,gBAAgB,CAAC,wCAAwC,CAAC;QAC5D;;0BACE,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAI,WAAU;gBAAgC,UAAU;;kCACjE,8OAAC;wBAAI,WAAU;wBAAgK,OAAO;4BACpL,YAAY;4BACZ,aAAa;4BACb,QAAQ;wBACV;kCACE,cAAA,8OAAC,kIAAA,CAAA,YAAS;4BAAC,WAAU;4BAAwB,OAAO;gCAAE,OAAO;4BAAiC;;;;;;;;;;;kCAEhG,8OAAC;wBAAK,WAAU;wBAAsE,OAAO;4BAAE,OAAO;wBAAkC;kCAAG;;;;;;;;;;;;0BAE7I,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,CAAC,uBAAuB,EAAE,SAAS,OAAO,oBAAoB,IAAI;0CAC9E;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,CAAC,uBAAuB,EAAE,SAAS,eAAe,oBAAoB,IAAI;0CACtF;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,CAAC,uBAAuB,EAAE,SAAS,cAAc,oBAAoB,IAAI;0CACrF;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,CAAC,SAAS,EAAE,SAAS,YAAY,oBAAoB,IAAI;0CACrE;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,CAAC,SAAS,EAAE,SAAS,cAAc,oBAAoB,IAAI;0CACvE;;;;;;;;;;;;kCAIH,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;4BAAC,MAAK;;;;;;;;;;;;;;;;;0BAG3B,8OAAC,0HAAA,CAAA,QAAK;;kCACJ,8OAAC,0HAAA,CAAA,eAAY;wBAAC,OAAO;kCACnB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,WAAU;;8CAC9C,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,0HAAA,CAAA,eAAY;wBAAC,MAAK;wBAAQ,WAAU;;0CACnC,8OAAC,0HAAA,CAAA,aAAU;gCAAC,WAAU;0CAAU;;;;;;0CAChC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAA8D;;;;;;;;;;;;kDAIhF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,2DAA2D,EACrE,SAAS,OACL,qCACA,sEACJ;0DACH;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,2DAA2D,EACrE,SAAS,eACL,qCACA,sEACJ;0DACH;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,2DAA2D,EACrE,SAAS,cACL,qCACA,sEACJ;0DACH;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,2DAA2D,EACrE,SAAS,YACL,qCACA,sEACJ;0DACH;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,2DAA2D,EACrE,SAAS,cACL,qCACA,sEACJ;0DACH;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAC5B,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/toast.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { cn } from '@/lib/utils';\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';\n\nexport type ToastType = 'success' | 'error' | 'warning' | 'info';\n\nexport interface Toast {\n  id: string;\n  type: ToastType;\n  title: string;\n  description?: string;\n  duration?: number;\n}\n\ninterface ToastContextType {\n  toasts: Toast[];\n  addToast: (toast: Omit<Toast, 'id'>) => void;\n  removeToast: (id: string) => void;\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n}\n\nexport function ToastProvider({ children }: { children: React.ReactNode }) {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const addToast = useCallback((toast: Omit<Toast, 'id'>) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const newToast = { ...toast, id };\n    \n    setToasts(prev => [...prev, newToast]);\n\n    // Auto remove after duration\n    const duration = toast.duration || 5000;\n    setTimeout(() => {\n      removeToast(id);\n    }, duration);\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n\n  return (\n    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>\n      {children}\n      <ToastContainer />\n    </ToastContext.Provider>\n  );\n}\n\nfunction ToastContainer() {\n  const { toasts, removeToast } = useToast();\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      {toasts.map((toast) => (\n        <ToastItem key={toast.id} toast={toast} onRemove={removeToast} />\n      ))}\n    </div>\n  );\n}\n\nfunction ToastItem({ toast, onRemove }: { toast: Toast; onRemove: (id: string) => void }) {\n  const icons = {\n    success: CheckCircle,\n    error: AlertCircle,\n    warning: AlertTriangle,\n    info: Info,\n  };\n\n  const colors = {\n    success: 'bg-green-500/90 border-green-400',\n    error: 'bg-red-500/90 border-red-400',\n    warning: 'bg-yellow-500/90 border-yellow-400',\n    info: 'bg-blue-500/90 border-blue-400',\n  };\n\n  const Icon = icons[toast.type];\n\n  return (\n    <div\n      className={cn(\n        'flex items-start gap-3 p-4 rounded-lg border backdrop-blur-md text-white shadow-lg min-w-[300px] max-w-[400px]',\n        colors[toast.type],\n        'animate-in slide-in-from-right-full duration-300'\n      )}\n    >\n      <Icon className=\"w-5 h-5 mt-0.5 flex-shrink-0\" />\n      <div className=\"flex-1\">\n        <h4 className=\"font-semibold text-sm\">{toast.title}</h4>\n        {toast.description && (\n          <p className=\"text-sm opacity-90 mt-1\">{toast.description}</p>\n        )}\n      </div>\n      <button\n        onClick={() => onRemove(toast.id)}\n        className=\"text-white/70 hover:text-white transition-colors\"\n      >\n        <X className=\"w-4 h-4\" />\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAsBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAG;QAEhC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;QAErC,6BAA6B;QAC7B,MAAM,WAAW,MAAM,QAAQ,IAAI;QACnC,WAAW;YACT,YAAY;QACd,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD,GAAG,EAAE;IAEL,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAU;QAAY;;YAC3D;0BACD,8OAAC;;;;;;;;;;;AAGP;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAAyB,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIhC;AAEA,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAoD;IACtF,MAAM,QAAQ;QACZ,SAAS,2NAAA,CAAA,cAAW;QACpB,OAAO,oNAAA,CAAA,cAAW;QAClB,SAAS,wNAAA,CAAA,gBAAa;QACtB,MAAM,kMAAA,CAAA,OAAI;IACZ;IAEA,MAAM,SAAS;QACb,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,OAAO,KAAK,CAAC,MAAM,IAAI,CAAC;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kHACA,MAAM,CAAC,MAAM,IAAI,CAAC,EAClB;;0BAGF,8OAAC;gBAAK,WAAU;;;;;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB,MAAM,KAAK;;;;;;oBACjD,MAAM,WAAW,kBAChB,8OAAC;wBAAE,WAAU;kCAA2B,MAAM,WAAW;;;;;;;;;;;;0BAG7D,8OAAC;gBACC,SAAS,IAAM,SAAS,MAAM,EAAE;gBAChC,WAAU;0BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/cookie-consent.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport { <PERSON><PERSON>, Setting<PERSON>, X } from 'lucide-react';\n\ninterface CookiePreferences {\n  necessary: boolean;\n  analytics: boolean;\n  marketing: boolean;\n}\n\nexport function CookieConsent() {\n  const [showBanner, setShowBanner] = useState(false);\n  const [showPreferences, setShowPreferences] = useState(false);\n  const [preferences, setPreferences] = useState<CookiePreferences>({\n    necessary: true, // Always required\n    analytics: false,\n    marketing: false\n  });\n\n  useEffect(() => {\n    // Check if user has already made a choice\n    const consent = localStorage.getItem('vierla-cookie-consent');\n    if (!consent) {\n      setShowBanner(true);\n    } else {\n      const savedPreferences = JSON.parse(consent);\n      setPreferences(savedPreferences);\n      applyCookieSettings(savedPreferences);\n    }\n  }, []);\n\n  const applyCookieSettings = (prefs: CookiePreferences) => {\n    // Apply analytics cookies\n    if (prefs.analytics) {\n      // Enable Google Analytics\n      if (typeof window !== 'undefined' && (window as any).gtag) {\n        (window as any).gtag('consent', 'update', {\n          'analytics_storage': 'granted'\n        });\n      }\n    } else {\n      // Disable analytics\n      if (typeof window !== 'undefined' && (window as any).gtag) {\n        (window as any).gtag('consent', 'update', {\n          'analytics_storage': 'denied'\n        });\n      }\n    }\n\n    // Apply marketing cookies\n    if (prefs.marketing) {\n      if (typeof window !== 'undefined' && (window as any).gtag) {\n        (window as any).gtag('consent', 'update', {\n          'ad_storage': 'granted'\n        });\n      }\n    } else {\n      if (typeof window !== 'undefined' && (window as any).gtag) {\n        (window as any).gtag('consent', 'update', {\n          'ad_storage': 'denied'\n        });\n      }\n    }\n  };\n\n  const handleAcceptAll = () => {\n    const allAccepted = {\n      necessary: true,\n      analytics: true,\n      marketing: true\n    };\n    setPreferences(allAccepted);\n    localStorage.setItem('vierla-cookie-consent', JSON.stringify(allAccepted));\n    applyCookieSettings(allAccepted);\n    setShowBanner(false);\n  };\n\n  const handleRejectAll = () => {\n    const onlyNecessary = {\n      necessary: true,\n      analytics: false,\n      marketing: false\n    };\n    setPreferences(onlyNecessary);\n    localStorage.setItem('vierla-cookie-consent', JSON.stringify(onlyNecessary));\n    applyCookieSettings(onlyNecessary);\n    setShowBanner(false);\n  };\n\n  const handleSavePreferences = () => {\n    localStorage.setItem('vierla-cookie-consent', JSON.stringify(preferences));\n    applyCookieSettings(preferences);\n    setShowBanner(false);\n    setShowPreferences(false);\n  };\n\n  if (!showBanner) return null;\n\n  return (\n    <>\n      {/* Cookie Banner */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50 p-4 backdrop-blur-md border-t bg-neutral-charcoal-dark/95 border-brand-sage/30\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"flex flex-col md:flex-row items-start md:items-center gap-4\">\n            <div className=\"flex items-center gap-3 flex-1\">\n              <Cookie className=\"w-6 h-6 flex-shrink-0 text-neutral-off-white\" />\n              <div>\n                <h3 className=\"font-semibold text-sm text-neutral-off-white\">We use cookies</h3>\n                <p className=\"text-sm text-brand-beige\">\n                  We use cookies to enhance your experience and analyze our traffic.\n                  <Link href=\"/privacy\" className=\"underline ml-1 text-neutral-off-white hover:text-brand-gold transition-colors\">\n                    Learn more\n                  </Link>\n                </p>\n              </div>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => setShowPreferences(true)}\n                className=\"px-4 py-2 text-sm rounded-lg transition-colors bg-neutral-charcoal-light/90 border border-brand-sage text-neutral-off-white hover:bg-brand-sage/20\"\n              >\n                <Settings className=\"w-4 h-4 inline mr-2\" />\n                Preferences\n              </button>\n              <button\n                onClick={handleRejectAll}\n                className=\"px-4 py-2 text-sm rounded-lg transition-colors bg-neutral-charcoal-light/90 border border-brand-sage text-neutral-off-white hover:bg-error/20\"\n              >\n                Reject All\n              </button>\n              <button\n                onClick={handleAcceptAll}\n                className=\"px-4 py-2 text-sm rounded-lg font-medium transition-colors bg-brand-gold border border-brand-gold text-neutral-charcoal-dark hover:bg-gold-600\"\n              >\n                Accept All\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Preferences Modal */}\n      {showPreferences && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-neutral-charcoal-dark/50 backdrop-blur-sm\">\n          <div className=\"backdrop-blur-md rounded-xl p-6 max-w-md w-full bg-neutral-charcoal-dark/95 border border-brand-sage/30\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"font-semibold text-lg text-neutral-off-white\">Cookie Preferences</h2>\n              <button\n                onClick={() => setShowPreferences(false)}\n                className=\"text-brand-beige hover:text-neutral-off-white transition-colors\"\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"font-medium text-neutral-off-white\">Necessary</h3>\n                  <p className=\"text-sm text-brand-beige\">Required for basic functionality</p>\n                </div>\n                <input\n                  type=\"checkbox\"\n                  checked={preferences.necessary}\n                  disabled\n                  className=\"w-4 h-4 accent-brand-sage\"\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"font-medium text-neutral-off-white\">Analytics</h3>\n                  <p className=\"text-sm text-brand-beige\">Help us improve our service</p>\n                </div>\n                <input\n                  type=\"checkbox\"\n                  checked={preferences.analytics}\n                  onChange={(e) => setPreferences(prev => ({ ...prev, analytics: e.target.checked }))}\n                  className=\"w-4 h-4 accent-brand-sage\"\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"font-medium text-neutral-off-white\">Marketing</h3>\n                  <p className=\"text-sm text-brand-beige\">Personalized ads and content</p>\n                </div>\n                <input\n                  type=\"checkbox\"\n                  checked={preferences.marketing}\n                  onChange={(e) => setPreferences(prev => ({ ...prev, marketing: e.target.checked }))}\n                  className=\"w-4 h-4 accent-brand-sage\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"flex gap-2 mt-6\">\n              <button\n                onClick={() => setShowPreferences(false)}\n                className=\"flex-1 px-4 py-2 text-sm rounded-lg transition-colors bg-neutral-charcoal-light/90 border border-brand-sage text-neutral-off-white hover:bg-brand-sage/20\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleSavePreferences}\n                className=\"flex-1 px-4 py-2 text-sm rounded-lg font-medium transition-colors bg-brand-gold border border-brand-gold text-neutral-charcoal-dark hover:bg-gold-600\"\n              >\n                Save\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AALA;;;;;AAaO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,WAAW;QACX,WAAW;QACX,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,CAAC,SAAS;YACZ,cAAc;QAChB,OAAO;YACL,MAAM,mBAAmB,KAAK,KAAK,CAAC;YACpC,eAAe;YACf,oBAAoB;QACtB;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,0BAA0B;QAC1B,IAAI,MAAM,SAAS,EAAE;YACnB,0BAA0B;YAC1B;;QAKF,OAAO;YACL,oBAAoB;YACpB;;QAKF;QAEA,0BAA0B;QAC1B,IAAI,MAAM,SAAS,EAAE;YACnB;;QAKF,OAAO;YACL;;QAKF;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,cAAc;YAClB,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,eAAe;QACf,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;QAC7D,oBAAoB;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,MAAM,gBAAgB;YACpB,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,eAAe;QACf,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;QAC7D,oBAAoB;QACpB,cAAc;IAChB;IAEA,MAAM,wBAAwB;QAC5B,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;QAC7D,oBAAoB;QACpB,cAAc;QACd,mBAAmB;IACrB;IAEA,IAAI,CAAC,YAAY,OAAO;IAExB,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAE,WAAU;;oDAA2B;kEAEtC,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAgF;;;;;;;;;;;;;;;;;;;;;;;;0CAMtH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG9C,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;sDAE1C,8OAAC;4CACC,MAAK;4CACL,SAAS,YAAY,SAAS;4CAC9B,QAAQ;4CACR,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;sDAE1C,8OAAC;4CACC,MAAK;4CACL,SAAS,YAAY,SAAS;4CAC9B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,WAAW,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CACjF,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;sDAE1C,8OAAC;4CACC,MAAK;4CACL,SAAS,YAAY,SAAS;4CAC9B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,WAAW,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CACjF,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}]}