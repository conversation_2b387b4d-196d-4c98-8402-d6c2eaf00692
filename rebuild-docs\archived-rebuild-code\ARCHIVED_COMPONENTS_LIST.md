# Archived Components List

The following components were moved to this archive folder during the cleanup process:

## UI Components (Unused)
- `accordion.tsx` - Accordion component from shadcn/ui (not used in current implementation)
- `background-beams.tsx` - Background beams effect (replaced by aurora background)
- `bento-grid.tsx` - Bento grid layout component (not used in current design)
- `dialog.tsx` - Dialog/modal component (not currently needed)
- `form.tsx` - Form components from shadcn/ui (using custom form implementation)
- `glowing-card.tsx` - Alternative glowing card implementation
- `golden-glow-card-with-margin.tsx` - Variant of golden glow card
- `golden-glow-card.tsx` - Alternative golden glow card implementation
- `golden-glow-effect.tsx` - Alternative golden glow effect
- `golden-glowing-effect.tsx` - Another golden glow effect variant
- `interactive-hover-button.tsx` - Interactive hover button (using shiny button instead)
- `tabs.tsx` - Tabs component from shadcn/ui (not used in current design)
- `text-reveal.tsx` - Text reveal animation component (not used in current pages)

## Reason for Archiving
These components were either:
1. Not being used in any pages or components
2. Replaced by better alternatives (e.g., aurora background instead of beams)
3. Duplicate implementations of similar functionality
4. Part of shadcn/ui library but not needed for current design

## Restoration
If any of these components are needed in the future, they can be restored from this archive folder.
