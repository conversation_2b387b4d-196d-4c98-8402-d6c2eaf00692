"use client"
import { useState } from 'react'
import { submitEmailSignup, submitContactForm, submitProfessionalApplication, checkBackendHealth } from '@/lib/api'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  duration?: number
}

export function TestingSuite() {
  const [isVisible, setIsVisible] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])

  const updateResult = (name: string, status: TestResult['status'], message: string, duration?: number) => {
    setResults(prev => prev.map(result => 
      result.name === name 
        ? { ...result, status, message, duration }
        : result
    ))
  }

  const runTests = async () => {
    setIsRunning(true)
    const testResults: TestResult[] = [
      { name: 'Backend Health Check', status: 'pending', message: 'Testing...' },
      { name: 'Email Signup API', status: 'pending', message: 'Testing...' },
      { name: 'Contact Form API', status: 'pending', message: 'Testing...' },
      { name: 'Professional Application API', status: 'pending', message: 'Testing...' },
      { name: 'Mobile Safari CSS', status: 'pending', message: 'Testing...' },
      { name: 'Analytics Tracking', status: 'pending', message: 'Testing...' }
    ]
    setResults(testResults)

    // Test 1: Backend Health Check
    try {
      const start = Date.now()
      const isHealthy = await checkBackendHealth()
      const duration = Date.now() - start
      updateResult(
        'Backend Health Check', 
        isHealthy ? 'success' : 'error',
        isHealthy ? `Backend is healthy (${duration}ms)` : 'Backend is not responding',
        duration
      )
    } catch (error) {
      updateResult('Backend Health Check', 'error', 'Health check failed')
    }

    // Test 2: Email Signup API
    try {
      const start = Date.now()
      const response = await submitEmailSignup('<EMAIL>', 'testing_suite')
      const duration = Date.now() - start
      updateResult(
        'Email Signup API',
        response.success ? 'success' : 'error',
        response.success ? `Email signup successful (${duration}ms)` : response.error || 'Unknown error',
        duration
      )
    } catch (error) {
      updateResult('Email Signup API', 'error', 'Email signup test failed')
    }

    // Test 3: Contact Form API
    try {
      const start = Date.now()
      const response = await submitContactForm({
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'This is a test message from the testing suite.',
        type: 'general'
      })
      const duration = Date.now() - start
      updateResult(
        'Contact Form API',
        response.success ? 'success' : 'error',
        response.success ? `Contact form successful (${duration}ms)` : response.error || 'Unknown error',
        duration
      )
    } catch (error) {
      updateResult('Contact Form API', 'error', 'Contact form test failed')
    }

    // Test 4: Professional Application API
    try {
      const start = Date.now()
      const response = await submitProfessionalApplication({
        firstName: 'Test',
        lastName: 'Professional',
        email: '<EMAIL>',
        phone: '************',
        businessName: 'Test Beauty Business',
        services: ['Hair Styling', 'Makeup'],
        experience: '3-5',
        certifications: [],
        serviceAreas: ['Test City'],
        availability: ['Monday', 'Tuesday'],
        travelRadius: '25',
        insurance: true,
        license: 'TEST123456',
        portfolio: 'https://example.com',
        rates: '$75-150',
        motivation: 'This is a test application from the testing suite.',
        references: 'Test references'
      })
      const duration = Date.now() - start
      updateResult(
        'Professional Application API',
        response.success ? 'success' : 'error',
        response.success ? `Application successful (${duration}ms)` : response.error || 'Unknown error',
        duration
      )
    } catch (error) {
      updateResult('Professional Application API', 'error', 'Application test failed')
    }

    // Test 5: Mobile Safari CSS
    try {
      const hasViewportFit = document.querySelector('meta[name="viewport"]')?.getAttribute('content')?.includes('viewport-fit=cover')
      const hasThemeColor = document.querySelector('meta[name="theme-color"]')?.getAttribute('content') === '#364035'
      const hasStatusBarStyle = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]')?.getAttribute('content') === 'black-translucent'
      
      const allPresent = hasViewportFit && hasThemeColor && hasStatusBarStyle
      updateResult(
        'Mobile Safari CSS',
        allPresent ? 'success' : 'error',
        allPresent ? 'All mobile Safari meta tags present' : 'Missing mobile Safari meta tags'
      )
    } catch (error) {
      updateResult('Mobile Safari CSS', 'error', 'CSS test failed')
    }

    // Test 6: Analytics Tracking
    try {
      const hasGtag = typeof (window as any).gtag === 'function'
      const hasFbq = typeof (window as any).fbq === 'function'
      const hasLocalStorage = typeof localStorage !== 'undefined'
      
      const analyticsWorking = hasLocalStorage // At minimum, localStorage should work
      updateResult(
        'Analytics Tracking',
        analyticsWorking ? 'success' : 'error',
        analyticsWorking 
          ? `Analytics ready (gtag: ${hasGtag}, fbq: ${hasFbq}, localStorage: ${hasLocalStorage})`
          : 'Analytics not working'
      )
    } catch (error) {
      updateResult('Analytics Tracking', 'error', 'Analytics test failed')
    }

    setIsRunning(false)
  }

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-20 bg-blue-600 text-white p-2 rounded-lg text-xs z-50"
      >
        🧪 Tests
      </button>

      {/* Testing Panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 bg-black/90 text-white p-4 rounded-lg text-xs max-w-md z-50 max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-bold">Testing Suite</h4>
            <button
              onClick={() => setIsVisible(false)}
              className="text-white/60 hover:text-white"
            >
              ✕
            </button>
          </div>

          <div className="mb-4">
            <button
              onClick={runTests}
              disabled={isRunning}
              className="w-full px-3 py-2 bg-blue-600 rounded text-sm disabled:opacity-50"
            >
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </button>
          </div>

          {results.length > 0 && (
            <div className="space-y-2">
              {results.map((result, index) => (
                <div key={index} className="border border-white/20 rounded p-2">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-semibold text-xs">{result.name}</span>
                    <span className={`text-xs ${
                      result.status === 'success' ? 'text-green-400' :
                      result.status === 'error' ? 'text-red-400' :
                      'text-yellow-400'
                    }`}>
                      {result.status === 'success' ? '✓' :
                       result.status === 'error' ? '✗' : '⏳'}
                    </span>
                  </div>
                  <div className="text-white/80 text-xs">
                    {result.message}
                  </div>
                  {result.duration && (
                    <div className="text-white/60 text-xs mt-1">
                      Duration: {result.duration}ms
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className="mt-4 text-white/60 text-xs">
            This testing suite verifies all major functionality including API endpoints, mobile fixes, and analytics.
          </div>
        </div>
      )}
    </>
  )
}
