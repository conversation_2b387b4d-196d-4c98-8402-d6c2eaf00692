"use client"
import { useState, useEffect } from 'react'
import { getPerformanceMetrics } from '@/components/performance-monitor'
import { getAnalyticsData } from '@/components/analytics'

interface PerformanceData {
  pageViews: any[]
  events: any[]
  performance: any[]
  customMetrics: any[]
}

export function PerformanceDashboard() {
  const [data, setData] = useState<PerformanceData | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return

    const loadData = () => {
      const performanceData = getPerformanceMetrics()
      const analyticsData = getAnalyticsData()
      
      if (performanceData || analyticsData) {
        setData({
          pageViews: analyticsData?.pageViews || [],
          events: analyticsData?.events || [],
          performance: performanceData?.coreWebVitals || [],
          customMetrics: performanceData?.customMetrics || []
        })
      }
    }

    loadData()
    const interval = setInterval(loadData, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  if (process.env.NODE_ENV !== 'development' || !data) {
    return null
  }

  const getAverageMetric = (metricName: string) => {
    const metrics = data.performance.filter(p => p.name === metricName)
    if (metrics.length === 0) return 0
    return Math.round(metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length)
  }

  const getRecentPageViews = () => {
    const last24Hours = Date.now() - (24 * 60 * 60 * 1000)
    return data.pageViews.filter(pv => pv.timestamp > last24Hours).length
  }

  const getTopEvents = () => {
    const eventCounts: Record<string, number> = {}
    data.events.forEach(event => {
      eventCounts[event.name] = (eventCounts[event.name] || 0) + 1
    })
    return Object.entries(eventCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
  }

  const getLatestCustomMetrics = () => {
    if (data.customMetrics.length === 0) return null
    return data.customMetrics[data.customMetrics.length - 1]
  }

  const latestMetrics = getLatestCustomMetrics()

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 left-4 bg-black/80 text-white p-2 rounded-lg text-xs z-50"
      >
        📊 Performance
      </button>

      {/* Dashboard */}
      {isVisible && (
        <div className="fixed bottom-16 left-4 bg-black/90 text-white p-4 rounded-lg text-xs max-w-sm max-h-96 overflow-y-auto z-50">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-bold">Performance Dashboard</h4>
            <button
              onClick={() => setIsVisible(false)}
              className="text-white/60 hover:text-white"
            >
              ✕
            </button>
          </div>

          {/* Core Web Vitals */}
          <div className="mb-4">
            <h5 className="font-semibold mb-2">Core Web Vitals</h5>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>LCP:</span>
                <span className={getAverageMetric('largest-contentful-paint') < 2500 ? 'text-green-400' : 'text-yellow-400'}>
                  {getAverageMetric('largest-contentful-paint')}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span>FID:</span>
                <span className={getAverageMetric('first-input') < 100 ? 'text-green-400' : 'text-yellow-400'}>
                  {getAverageMetric('first-input')}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span>CLS:</span>
                <span className={getAverageMetric('layout-shift') < 0.1 ? 'text-green-400' : 'text-yellow-400'}>
                  {(getAverageMetric('layout-shift') / 1000).toFixed(3)}
                </span>
              </div>
            </div>
          </div>

          {/* Custom Metrics */}
          {latestMetrics && (
            <div className="mb-4">
              <h5 className="font-semibold mb-2">Load Times</h5>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>TTFB:</span>
                  <span className={latestMetrics.ttfb < 600 ? 'text-green-400' : 'text-yellow-400'}>
                    {latestMetrics.ttfb}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>DOM Ready:</span>
                  <span>{latestMetrics.domContentLoaded}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Load:</span>
                  <span>{latestMetrics.totalLoadTime}ms</span>
                </div>
              </div>
            </div>
          )}

          {/* Analytics */}
          <div className="mb-4">
            <h5 className="font-semibold mb-2">Analytics (24h)</h5>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Page Views:</span>
                <span className="text-blue-400">{getRecentPageViews()}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Events:</span>
                <span className="text-purple-400">{data.events.length}</span>
              </div>
            </div>
          </div>

          {/* Top Events */}
          <div className="mb-4">
            <h5 className="font-semibold mb-2">Top Events</h5>
            <div className="space-y-1">
              {getTopEvents().map(([event, count]) => (
                <div key={event} className="flex justify-between">
                  <span className="truncate">{event}:</span>
                  <span className="text-orange-400">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Device Info */}
          {latestMetrics && (
            <div className="mb-4">
              <h5 className="font-semibold mb-2">Device Info</h5>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Connection:</span>
                  <span className="text-cyan-400">{latestMetrics.connection}</span>
                </div>
                <div className="text-xs text-white/60 break-all">
                  {latestMetrics.userAgent.split(' ')[0]}
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={() => {
                localStorage.removeItem('vierla-performance')
                localStorage.removeItem('vierla-custom-metrics')
                localStorage.removeItem('vierla-page-views')
                localStorage.removeItem('vierla-events')
                setData({ pageViews: [], events: [], performance: [], customMetrics: [] })
              }}
              className="px-2 py-1 bg-red-600 rounded text-xs"
            >
              Clear Data
            </button>
            <button
              onClick={() => {
                const exportData = {
                  timestamp: new Date().toISOString(),
                  ...data
                }
                console.log('Performance Data Export:', exportData)
                navigator.clipboard?.writeText(JSON.stringify(exportData, null, 2))
              }}
              className="px-2 py-1 bg-blue-600 rounded text-xs"
            >
              Export
            </button>
          </div>
        </div>
      )}
    </>
  )
}
