{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shimmer-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n}\n\nconst ShimmerButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor,\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background,\n      className,\n      children,\n      size = \"md\",\n      variant = \"primary\",\n      ...props\n    },\n    ref,\n  ) => {\n    // Size-based styling\n    const sizeClasses = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-base\",\n      lg: \"px-8 py-4 text-lg\"\n    };\n\n    // Color variants with customization support\n    const getColors = () => {\n      const baseColors = {\n        primary: {\n          bg: background || \"var(--master-button-primary-bg)\",\n          shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        },\n        secondary: {\n          bg: background || \"var(--master-button-secondary-bg)\",\n          shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        },\n        accent: {\n          bg: background || \"var(--master-brand-accent)\",\n          shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        }\n      };\n      return baseColors[variant];\n    };\n\n    const colors = getColors();\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": colors.shimmer,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": colors.bg,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          sizeClasses[size],\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* spark container */}\n        <div\n          className={cn(\n            \"-z-30 blur-[2px]\",\n            \"absolute inset-0 overflow-visible [container-type:size]\",\n          )}\n        >\n          {/* spark */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            {/* spark before */}\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n        {children}\n\n        {/* Highlight */}\n        <div\n          className={cn(\n            \"insert-0 absolute size-full\",\n            \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            // transition\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            // on hover\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            // on click\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n          )}\n        />\n\n        {/* backdrop */}\n        <div\n          className={cn(\n            \"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\",\n          )}\n        />\n      </button>\n    );\n  },\n);\n\nShimmerButton.displayName = \"ShimmerButton\";\n\nexport { ShimmerButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACpC,CACE,EACE,YAAY,EACZ,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,GAAG,OACJ,EACD;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4CAA4C;IAC5C,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,WAAW;gBACT,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,QAAQ;gBACN,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB,OAAO,OAAO;YACjC,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ,OAAO,EAAE;QACnB;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+MACA,qFACA,WAAW,CAAC,KAAK,EACjB;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oBACA;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;YAGlB;0BAGD,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+BACA,oFACA,aAAa;gBACb,yDACA,WAAW;gBACX,oDACA,WAAW;gBACX;;;;;;0BAKJ,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKV;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/provider-app/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport { LayoutTemplate, FileText, Users, BarChart2, Calendar, CreditCard, Globe, Palette, Zap, Shield } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function ProviderApp() {\n  return (\n    <div className=\"page-provider min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n      \n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-notable\">\n            FOR BEAUTY PROFESSIONALS - EVERYTHING YOU NEED TO RUN YOUR BUSINESS\n          </h1>\n          <p className=\"text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-sans\">\n            Join our curated network of top-tier beauty professionals. Build your business with powerful tools designed for your success.\n          </p>\n          \n          <Link href=\"/apply\" className=\"mb-8 inline-block\">\n            <ShimmerButton\n              size=\"lg\"\n              background=\"var(--master-action-primary)\"\n              shimmerColor=\"var(--master-brand-accent)\"\n              className=\"px-8 py-4 text-lg font-medium text-mantle-50\"\n            >\n              <span className=\"flex items-center\">\n                Apply to Join Vierla\n                <FileText className=\"ml-2 w-5 h-5\" />\n              </span>\n            </ShimmerButton>\n          </Link>\n        </div>\n      </section>\n\n      {/* Business Tools Section */}\n      <section className=\"relative z-10 py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16\">\n            {[\n              {\n                icon: Globe,\n                title: \"Digital Store & Website Builder\",\n                description: \"Create stunning, professional websites and branded online presence with our AI-powered builder.\",\n                features: [\"• Drag & drop interface\", \"• AI content generation\", \"• Portfolio gallery\", \"• Custom domains\"]\n              },\n              {\n                icon: FileText,\n                title: \"Smart Invoicing\",\n                description: \"Streamline your billing process with intelligent invoicing features.\",\n                features: [\"• Automated reminders\", \"• Multiple currencies\", \"• Payment tracking\", \"• Professional templates\"]\n              },\n              {\n                icon: Users,\n                title: \"Integrated CRM\",\n                description: \"Manage customer relationships and grow your business effectively.\",\n                features: [\"• Customer database\", \"• Sales pipeline\", \"• Task automation\", \"• Journey tracking\"]\n              },\n              {\n                icon: BarChart2,\n                title: \"Business Analytics\",\n                description: \"Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends.\",\n                features: [\"• Revenue tracking\", \"• Customer insights\", \"• Booking analytics\", \"• Growth metrics\"]\n              }\n            ].map((tool, index) => (\n              <div key={index}>\n                <GoldenGlowingCardContainer>\n                  <Card className=\"bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl p-6 h-full\">\n                    <CardHeader className=\"text-left\">\n                      <div className=\"w-16 h-16 mb-4 rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30\">\n                        <tool.icon className=\"w-8 h-8 text-sage drop-shadow-lg\" />\n                      </div>\n                      <CardTitle className=\"text-xl font-bold text-light-off-white drop-shadow-lg text-left font-tai-heritage\">{tool.title}</CardTitle>\n                    </CardHeader>\n                    <CardContent className=\"text-left\">\n                      <p className=\"text-warm-beige leading-relaxed text-sm mb-4 drop-shadow-sm font-sans\">\n                        {tool.description}\n                      </p>\n                      <div className=\"space-y-2\">\n                        {tool.features.map((feature, idx) => (\n                          <div key={idx} className=\"text-warm-beige/70 text-xs flex items-center justify-start drop-shadow-sm font-sans\">\n                            <span className=\"w-1.5 h-1.5 rounded-full mr-2 bg-sage drop-shadow-sm\"></span>\n                            {feature}\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                </GoldenGlowingCardContainer>\n              </div>\n            ))}\n          </div>\n\n\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n            {[\n              {\n                icon: Calendar,\n                title: \"Booking Management\",\n                description: \"Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling.\",\n                features: [\"• Calendar sync\", \"• Auto reminders\", \"• Easy rescheduling\", \"• Availability control\"]\n              },\n              {\n                icon: CreditCard,\n                title: \"Payment Processing\",\n                description: \"Secure payment handling with multiple payment methods and direct deposits to your preferred account.\",\n                features: [\"• Multiple payment methods\", \"• Secure transactions\", \"• Direct deposits\", \"• Transaction history\"]\n              }\n            ].map((feature, index) => (\n              <div key={index}>\n                <GoldenGlowingCardContainer>\n                  <Card className=\"bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl overflow-hidden h-full flex flex-col\">\n                    <CardContent className=\"p-8 flex flex-col h-full\">\n                      <div className=\"text-left flex-grow flex flex-col\">\n                        <div className=\"w-16 h-16 mb-4 rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30\">\n                          <feature.icon className=\"w-8 h-8 text-sage drop-shadow-lg\" />\n                        </div>\n                        <h3 className=\"text-2xl font-bold text-light-off-white mb-4 drop-shadow-lg font-tai-heritage\">\n                          {feature.title}\n                        </h3>\n                        <p className=\"text-warm-beige leading-relaxed mb-6 flex-grow drop-shadow-sm font-sans\">\n                          {feature.description}\n                        </p>\n                        <div className=\"space-y-2 mt-auto\">\n                          {feature.features.map((item, idx) => (\n                            <div key={idx} className=\"text-warm-beige/70 text-sm drop-shadow-sm font-sans\">\n                              {item}\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </GoldenGlowingCardContainer>\n              </div>\n            ))}\n          </div>\n\n          {/* Coming Soon Section */}\n          <div className=\"text-center mt-16\">\n            <Card className=\"bg-light-charcoal backdrop-blur-md rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto border border-sage/30\">\n              <CardContent>\n                <h3 className=\"text-4xl font-black text-light-off-white mb-6 drop-shadow-lg font-notable\">COMING SOON</h3>\n                <p className=\"text-xl text-warm-beige mb-8 max-w-2xl mx-auto drop-shadow-sm font-sans\">\n                  We're building something special for beauty professionals. Join our waitlist to be the first to know when we launch.\n                </p>\n                \n                {/* App Store Buttons */}\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n                  <div className=\"flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75\">\n                    <svg className=\"w-8 h-8 mr-3\" viewBox=\"0 0 24 24\" fill=\"white\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <div className=\"text-left\">\n                      <div className=\"text-xs text-gray-300\">Coming Soon to</div>\n                      <div className=\"text-lg font-semibold text-light-off-white font-sans\">Apple App Store</div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75\">\n                    <svg className=\"w-8 h-8 mr-3\" viewBox=\"0 0 24 24\" fill=\"white\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <div className=\"text-left\">\n                      <div className=\"text-xs text-gray-300\">Coming Soon to</div>\n                      <div className=\"text-lg font-semibold text-light-off-white font-sans\">Google Play Store</div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex justify-center mt-8\">\n                  <Link href=\"/apply\">\n                    <ShimmerButton\n                      size=\"lg\"\n                      background=\"#B8956A\"\n                      shimmerColor=\"#E5D4A1\"\n                      className=\"px-8 py-4 text-lg font-medium text-[#2D2A26]\"\n                    >\n                      <span className=\"flex items-center\">\n                        Apply to Join Vierla\n                        <FileText className=\"ml-2 w-5 h-5\" />\n                      </span>\n                    </ShimmerButton>\n                  </Link>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqG;;;;;;sCAGnH,8OAAC;4BAAE,WAAU;sCAAsG;;;;;;sCAInH,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAC5B,cAAA,8OAAC,sIAAA,CAAA,gBAAa;gCACZ,MAAK;gCACL,YAAW;gCACX,cAAa;gCACb,WAAU;0CAEV,cAAA,8OAAC;oCAAK,WAAU;;wCAAoB;sDAElC,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM,oMAAA,CAAA,QAAK;oCACX,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAA2B;wCAA2B;wCAAuB;qCAAmB;gCAC7G;gCACA;oCACE,MAAM,8MAAA,CAAA,WAAQ;oCACd,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAyB;wCAAyB;wCAAsB;qCAA2B;gCAChH;gCACA;oCACE,MAAM,oMAAA,CAAA,QAAK;oCACX,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAuB;wCAAoB;wCAAqB;qCAAqB;gCAClG;gCACA;oCACE,MAAM,gOAAA,CAAA,YAAS;oCACf,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAsB;wCAAuB;wCAAuB;qCAAmB;gCACpG;6BACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;8CACC,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;kDACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC,yHAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAEvB,8OAAC,yHAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqF,KAAK,KAAK;;;;;;;;;;;;8DAEtH,8OAAC,yHAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;sEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,8OAAC;oEAAc,WAAU;;sFACvB,8OAAC;4EAAK,WAAU;;;;;;wEACf;;mEAFO;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAfZ;;;;;;;;;;sCA8Bd,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM,0MAAA,CAAA,WAAQ;oCACd,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAmB;wCAAoB;wCAAuB;qCAAyB;gCACpG;gCACA;oCACE,MAAM,kNAAA,CAAA,aAAU;oCAChB,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAA8B;wCAAyB;wCAAqB;qCAAwB;gCACjH;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;8CACC,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;kDACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,QAAQ,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,oBAC3B,8OAAC;oEAAc,WAAU;8EACtB;mEADO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhBd;;;;;;;;;;sCA8Bd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAG,WAAU;sDAA4E;;;;;;sDAC1F,8OAAC;4CAAE,WAAU;sDAA0E;;;;;;sDAKvF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAe,SAAQ;4DAAY,MAAK;sEACrD,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,8OAAC;oEAAI,WAAU;8EAAuD;;;;;;;;;;;;;;;;;;8DAI1E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAe,SAAQ;4DAAY,MAAK;sEACrD,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,8OAAC;oEAAI,WAAU;8EAAuD;;;;;;;;;;;;;;;;;;;;;;;;sDAK5E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,sIAAA,CAAA,gBAAa;oDACZ,MAAK;oDACL,YAAW;oDACX,cAAa;oDACb,WAAU;8DAEV,cAAA,8OAAC;wDAAK,WAAU;;4DAAoB;0EAElC,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C", "debugId": null}}]}