

# **Vierla.com: Implementation Specifics Guide**

This document provides detailed technical instructions for implementing components, features, and backend integrations for the Vierla.com rebuild.

---

### **1\. Design System Implementation**

* **File:** app/globals.css  
* Action: Define the following CSS variables within the :root selector. These will be used by Tailwind CSS and shadcn/ui components.css  
  :root {  
  \--background: 0 0% 3.9%;  
  \--foreground: 0 0% 98%;  
  /\*... other shadcn default colors... \*/

/\* Vierla Brand Colors /  
\--primary: 262.1 83.3% 57.8%; / Vierla Purple \*/  
\--primary-foreground: 210 20% 98%;  
/\*... other color definitions... \*/

/\* Border Radius \*/  
\--radius: 0.5rem;  
}  
.dark {  
/\*... dark theme overrides... \*/  
}

\---

\#\#\# \*\*2. Core Component Customization\*\*

\*   \*\*Objective:\*\* Ensure all base components reflect the Vierla brand.  
\*   \*\*Workflow:\*\*  
    1\.  Add a component via \`npx shadcn-ui@latest add \[component\]\`.  
    2\.  Open the generated file (e.g., \`components/ui/button.tsx\`).  
    3\.  Modify the \`className\` attributes to use the Vierla brand colors. For example, change \`bg-primary\` to use the newly defined \`--primary\` variable.  
    4\.  Ensure all components use \`rounded-md\` to respect the global \`--radius\` variable.

\---

\#\#\# \*\*3. Backend Form Integration (\`/apply\` page)\*\*

\*   \*\*Objective:\*\* Ensure the new multi-step form correctly submits data to the existing backend without requiring any backend changes.  
\*   \*\*Endpoint:\*\* The form must submit a \`POST\` request to \`/api/apply\`. (This is a placeholder; please confirm the actual endpoint).  
\*   \*\*Data Structure:\*\* The body of the \`POST\` request must be a JSON object with the following structure. The keys must match exactly.  
    \`\`\`json  
    {  
      "fullName": "...",  
      "email": "...",  
      "password": "...",  
      "companyName": "...",  
      "companySize": "...",  
      "industry": "..."  
    }  
    \`\`\`  
\*   \*\*Implementation:\*\* Use the native \`fetch\` API or a library like \`axios\` within the form's submission handler to send the data. Handle success and error states appropriately by displaying messages to the user.

\---

\#\#\# \*\*4. Performance & Resource Optimization\*\*

\*   \*\*Objective:\*\* Ensure the self-hosted site is fast and efficient.  
\*   \*\*Image Optimization:\*\* Since the site is self-hosted, install the \`sharp\` library (\`pnpm add sharp\`) to enable high-performance, production-ready image optimization with Next.js's \`\<Image\>\` component.  
\*   \*\*Animation Performance:\*\* The animated components (\`Aurora Background\`, \`Shiny Button\`) are powered by Framer Motion. They are generally performant as they use hardware-accelerated CSS properties. Limit their use to the specific high-impact areas defined in the roadmap to avoid overuse.  
\*   \*\*Standalone Output:\*\* In \`next.config.mjs\`, ensure the output mode is set to \`standalone\` to create a smaller deployment bundle.  
    \`\`\`javascript  
    /\*\* @type {import('next').NextConfig} \*/  
    const nextConfig \= {  
      output: 'standalone',  
    };  
    export default nextConfig;  
    \`\`\`  
\*   \*\*Dynamic Imports:\*\* For components that are heavy or not immediately visible (e.g., complex modals), consider using \`next/dynamic\` to code-split and load them lazily.  
