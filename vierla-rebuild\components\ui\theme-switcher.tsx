"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { Sun, Moon, Monitor } from "lucide-react"

export function ThemeSwitcher() {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // useEffect only runs on the client, so now we can safely show the UI
  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="w-9 h-9 bg-transparent hover:bg-brand-sage/10 transition-colors border-0"
      >
        <Sun className="h-4 w-4 text-neutral-off-white" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    )
  }

  const cycleTheme = () => {
    // Only switch between light and dark mode
    if (resolvedTheme === "light") {
      setTheme("dark")
    } else {
      setTheme("light")
    }
  }

  const getIcon = () => {
    // Show icon based on resolved theme (actual current theme)
    if (resolvedTheme === "light") {
      return <Sun className="h-4 w-4 text-neutral-off-white transition-all" />
    } else {
      return <Moon className="h-4 w-4 text-neutral-off-white transition-all" />
    }
  }

  const getTooltipText = () => {
    if (resolvedTheme === "light") {
      return "Switch to dark mode"
    } else {
      return "Switch to light mode"
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={cycleTheme}
      className="w-9 h-9 bg-transparent hover:bg-brand-sage/10 transition-all duration-300 group border-0"
      title={getTooltipText()}
    >
      <div className="relative">
        {getIcon()}
        {/* Subtle glow effect on hover */}
        <div className="absolute inset-0 rounded-full bg-brand-gold/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />
      </div>
      <span className="sr-only">{getTooltipText()}</span>
    </Button>
  )
}
