

# **Vierla.com Website Rebuild: Roadmap**

This document outlines the complete roadmap for rebuilding the Vierla.com website from the ground up. The project is located in a new directory, and the old project at /services-app-web-v1/services-app-web should be used as a reference for configuration, backend integration, and deployment specifics.

**Reference Documents:**

* **Site Structure & Content:** Vierla-Site-Structure.md  
* **Implementation Specifics:** Vierla-Implementation-Specifics.md  
* **Component Reference Code:** Vierla-Component-Code.md

---

### **Task 1: Project Initialization & Setup**

**Goal:** Create a new, clean Next.js project with all necessary dependencies and configurations.

* **Subtask 1.1:** Initialize a new Next.js 15 project using pnpm. Use the same settings as the reference project (TypeScript, ESLint, Tailwind CSS, App Router).  
* **Subtask 1.2:** Install core dependencies. Reference the package.json in the old project and install lucide-react, class-variance-authority, clsx, tailwind-merge, and any other essential packages.  
* **Subtask 1.3:** Configure Tailwind CSS. Copy the contents of tailwind.config.ts and postcss.config.mjs from the reference project to ensure consistency.  
* **Subtask 1.4:** Initialize shadcn/ui. Run npx shadcn-ui@latest init and configure it according to the reference project's setup (e.g., globals.css location, component alias).  
* **Subtask 1.5:** Set up project structure. Create the necessary directories: components/ui, components/marketing, app, lib, etc.

---

### **Task 2: Design System & Core Components**

**Goal:** Establish the foundational design tokens and create the core, reusable UI components.

* **Subtask 2.1:** Define Design Tokens. Open app/globals.css and define the color palette (primary purple, neutrals, semantic colors) and border-radius values as CSS variables, as specified in Vierla-Implementation-Specifics.md.  
* **Subtask 2.2:** Add Core shadcn/ui Components. Use the shadcn/ui CLI to add the base components for: button, input, card, dialog, label, form.  
* **Subtask 2.3:** Customize Core Components. Modify the code of each component added in the previous step to apply the custom styles (colors, radii) from the design tokens. This creates the unique "Vierla" version of each component. Refer to Vierla-Implementation-Specifics.md for details.

---

### **Task 3: Page Construction: Homepage (/)**

**Goal:** Build the main landing page using the finalized component selections.

* **Subtask 3.1:** Create Homepage Layout. Create the file app/page.tsx and set up the basic page structure.  
* **Subtask 3.2:** Implement Global Background. Add the Aurora Background component. Reference Vierla-Component-Code.md for the code and Vierla-Implementation-Specifics.md for placement.  
* **Subtask 3.3:** Implement Navigation Bar. Add the shadcnblocks-com-navbar1 component. Populate it with links as defined in Vierla-Site-Structure.md.  
* **Subtask 3.4:** Build the Hero Section. Combine the Aurora Background with a content block. Use the headline and sub-headline from Vierla-Site-Structure.md.  
* **Subtask 3.5:** Build the Features Section. Implement the Bento Grid layout. For each grid item, use a Card component with the Glowing Effect. Populate with content from Vierla-Site-Structure.md.  
* **Subtask 3.6:** Implement the Primary CTA. Add the Shiny Button with the text "Get Started Free".  
* **Subtask 3.7:** Implement the Footer. Add the Footer component by mdadul and populate its links according to Vierla-Site-Structure.md.

---

### **Task 4: Page Construction: Features (/features)**

**Goal:** Build the page dedicated to detailing Vierla's product suite.

* **Subtask 4.1:** Create Features Page Layout. Create the file app/features/page.tsx.  
* **Subtask 4.2:** Implement Tab Navigation. Use the shadcn/ui Tabs component to create tabs for "Website Builder," "CRM," "Invoicing," etc., as specified in Vierla-Site-Structure.md.  
* **Subtask 4.3:** Populate Tab Content. For each tab, create a detailed layout using Card components to describe the feature's benefits and functionalities. Use content from Vierla-Site-Structure.md.

---

### **Task 5: Page Construction: Apply Form (/apply)**

**Goal:** Rebuild the application form and ensure it integrates with the existing backend.

* **Subtask 5.1:** Create Apply Page Layout. Create the file app/apply/page.tsx.  
* **Subtask 5.2:** Implement the Multi-Step Form. Add the Multistep Form component from Vierla-Component-Code.md.  
* **Subtask 5.3:** Style the Form. Ensure all inputs, labels, and buttons within the form use the custom-styled core components created in Task 2\.  
* **Subtask 5.4:** Integrate Backend Logic. Implement the form submission logic. Refer to Vierla-Implementation-Specifics.md for the exact API endpoint and the required JSON structure for the POST request. This must match the reference project's implementation perfectly.

---

### **Task 6: Finalization & Deployment Prep**

**Goal:** Ensure the application is production-ready and matches the existing deployment configuration.

* **Subtask 6.1:** Copy Deployment Files. Copy the Dockerfile, docker-compose.yml, and any Nginx configuration files from the reference project /services-app-web-v1/services-app-web into the root of the new project.  
* **Subtask 6.2:** Verify Build Scripts. Check the package.json scripts (build, start, lint) to ensure they are correct for a production deployment.  
* **Subtask 6.3:** Final Review. Perform a final check of all pages for responsiveness, functionality, and visual consistency against the design system.