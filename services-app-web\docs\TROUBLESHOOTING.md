# Troubleshooting Guide: Vierla Docker Deployment

This guide helps diagnose and fix common issues when deploying the Vierla application with <PERSON><PERSON> and nginx reverse proxy.

## 🔍 Quick Diagnosis Commands

### Check Container Status
```bash
# Check if container is running and healthy
docker compose ps

# Check container health details
docker inspect vierla-web --format='{{.State.Health.Status}}'

# View health check logs
docker inspect vierla-web --format='{{json .State.Health.Log}}' | jq .
```

### Check Application Logs
```bash
# View recent logs
docker compose logs --tail=50 vierla-web

# Follow logs in real-time
docker compose logs -f vierla-web

# Check for specific errors
docker compose logs vierla-web | grep -i error
```

### Test Connectivity
```bash
# Test container internal health endpoint
docker exec vierla-web curl -f http://localhost:3000/api/health

# Test from host (if port is bound to localhost)
curl -f http://127.0.0.1:3000/api/health

# Test nginx proxy (replace with your domain)
curl -I http://your-domain.com/api/health
```

## 🚨 Common Issues and Solutions

### Issue 1: Container Shows as "Unhealthy"

**Symptoms:**
- `docker compose ps` shows container as "unhealthy"
- Container keeps restarting

**Diagnosis:**
```bash
# Check health check logs
docker inspect vierla-web --format='{{json .State.Health.Log}}' | jq .

# Test health endpoint manually
docker exec vierla-web curl -v http://localhost:3000/api/health
```

**Solutions:**
1. **Missing curl in container:**
   ```bash
   # Check if curl is installed
   docker exec vierla-web which curl
   ```
   If missing, rebuild the container (our Dockerfile includes curl).

2. **Application not binding to correct interface:**
   - Ensure `HOSTNAME=0.0.0.0` is set in environment
   - Check if Next.js is listening on all interfaces

3. **Health endpoint not responding:**
   ```bash
   # Test main page instead
   docker exec vierla-web curl -f http://localhost:3000/
   ```

### Issue 2: 502 Bad Gateway from Nginx

**Symptoms:**
- Nginx returns 502 Bad Gateway
- Application works when accessed directly

**Diagnosis:**
```bash
# Check if container is reachable from host
curl -I http://127.0.0.1:3000/

# Check nginx error logs
sudo tail -f /var/log/nginx/error.log

# Check if port is bound correctly
sudo netstat -tlnp | grep :3000
```

**Solutions:**
1. **Wrong upstream configuration:**
   - For host-based nginx: Use `127.0.0.1:3000`
   - For Docker-based nginx: Use `vierla-web:3000`

2. **Port binding issues:**
   ```yaml
   # In docker-compose.yml, ensure:
   ports:
     - "127.0.0.1:3000:3000"  # Binds to localhost only
   ```

3. **Network connectivity:**
   ```bash
   # Ensure external network exists
   docker network create web
   
   # Check if container is on correct network
   docker network inspect web
   ```

### Issue 3: Container Exits Immediately

**Symptoms:**
- Container starts then exits with code 0 or 1
- No health check status (container not running long enough)

**Diagnosis:**
```bash
# Check exit reason
docker compose logs vierla-web

# Check if build completed successfully
docker compose build --no-cache vierla-web
```

**Solutions:**
1. **Build failures:**
   ```bash
   # Rebuild without cache
   docker compose build --no-cache
   
   # Check for Node.js/npm errors during build
   docker compose build vierla-web 2>&1 | grep -i error
   ```

2. **Missing files:**
   - Ensure `.next/standalone` directory exists after build
   - Check if `server.js` is present in standalone output

3. **Permission issues:**
   ```bash
   # Check file ownership in container
   docker run --rm -it vierla-web ls -la /app/
   ```

### Issue 4: Static Assets Not Loading

**Symptoms:**
- Page loads but CSS/JS/images are missing
- 404 errors for static assets

**Solutions:**
1. **Missing static files copy:**
   ```dockerfile
   # Ensure Dockerfile includes:
   COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
   ```

2. **Nginx caching issues:**
   ```bash
   # Clear nginx cache if using proxy_cache
   sudo rm -rf /var/cache/nginx/*
   sudo systemctl reload nginx
   ```

### Issue 5: SSL/HTTPS Issues

**Symptoms:**
- SSL certificate errors
- Mixed content warnings

**Solutions:**
1. **Certificate path issues:**
   ```bash
   # Check certificate files exist
   sudo ls -la /etc/letsencrypt/live/your-domain.com/
   
   # Test nginx configuration
   sudo nginx -t
   ```

2. **Let's Encrypt renewal:**
   ```bash
   # Renew certificates
   sudo certbot renew --dry-run
   
   # Force renewal if needed
   sudo certbot renew --force-renewal
   ```

## 🔧 Advanced Debugging

### Enable Debug Logging

1. **Next.js Debug Mode:**
   ```yaml
   # In docker-compose.yml
   environment:
     - DEBUG=*
     - NODE_ENV=production
   ```

2. **Nginx Debug Logging:**
   ```nginx
   # In nginx configuration
   error_log /var/log/nginx/error.log debug;
   ```

### Performance Monitoring

```bash
# Check container resource usage
docker stats vierla-web

# Check memory usage inside container
docker exec vierla-web cat /proc/meminfo

# Check disk usage
docker exec vierla-web df -h
```

### Network Debugging

```bash
# Check container networking
docker exec vierla-web netstat -tlnp

# Test DNS resolution
docker exec vierla-web nslookup your-domain.com

# Check iptables rules (if using UFW)
sudo iptables -L -n
```

## 📋 Deployment Checklist

Before deploying, ensure:

- [ ] External network `web` exists: `docker network create web`
- [ ] Domain DNS points to your server
- [ ] Ports 80/443 are open in firewall
- [ ] SSL certificates are valid and accessible
- [ ] Nginx configuration uses correct upstream (127.0.0.1:3000 for host-based)
- [ ] Docker and Docker Compose are latest versions
- [ ] Sufficient disk space for Docker images and logs

## 🆘 Emergency Recovery

If deployment fails completely:

```bash
# Stop everything
docker compose down

# Remove containers and images
docker compose down --rmi all --volumes

# Clean up Docker system
docker system prune -af --volumes

# Rebuild from scratch
docker compose build --no-cache
docker compose up -d

# Check logs immediately
docker compose logs -f vierla-web
```

## 📞 Getting Help

If issues persist:

1. **Collect diagnostic information:**
   ```bash
   # Create diagnostic report
   echo "=== System Info ===" > debug.log
   uname -a >> debug.log
   docker --version >> debug.log
   docker compose version >> debug.log
   
   echo "=== Container Status ===" >> debug.log
   docker compose ps >> debug.log
   
   echo "=== Container Logs ===" >> debug.log
   docker compose logs --tail=100 vierla-web >> debug.log
   
   echo "=== Health Check ===" >> debug.log
   docker inspect vierla-web --format='{{json .State.Health}}' >> debug.log
   ```

2. **Check documentation:**
   - Review `DEPLOYMENT.md` for setup instructions
   - Check `DEPLOYMENT_GUIDE.md` for detailed configuration

3. **Common solutions:**
   - Restart Docker daemon: `sudo systemctl restart docker`
   - Restart nginx: `sudo systemctl restart nginx`
   - Check firewall: `sudo ufw status`
   - Verify DNS: `nslookup your-domain.com`
