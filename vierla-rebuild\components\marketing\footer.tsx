import React from "react";
import Link from "next/link";

export const Footer = React.memo(function Footer() {
  return (
    <footer className="bg-neutral-charcoal-dark/70 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/60 border-t border-brand-sage/10 py-8 sm:py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mobile: Single column, Tablet: 2 columns, Desktop: 4 columns */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {/* Brand Section - Full width on mobile */}
          <div className="sm:col-span-2 lg:col-span-1">
            <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-neutral-off-white drop-shadow-lg font-tai-heritage">V<PERSON><PERSON></h2>
            <p className="text-brand-beige text-sm sm:text-base drop-shadow-sm font-sans max-w-xs">
              Ontario's Premier Beauty Marketplace connecting you with verified professionals.
            </p>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="font-semibold mb-3 sm:mb-4 text-neutral-off-white drop-shadow-lg font-tai-heritage text-base sm:text-lg">Product</h3>
            <ul className="space-y-2 text-sm sm:text-base">
              <li><Link href="/features" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">Features</Link></li>
              <li><Link href="/pricing" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">Pricing</Link></li>
              <li><Link href="/providers" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">For Providers</Link></li>
              <li><Link href="/apply" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">Apply</Link></li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-semibold mb-3 sm:mb-4 text-neutral-off-white drop-shadow-lg font-tai-heritage text-base sm:text-lg">Company</h3>
            <ul className="space-y-2 text-sm sm:text-base">
              <li><Link href="/about" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">About Us</Link></li>
              <li><Link href="/contact" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">Contact Us</Link></li>
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h3 className="font-semibold mb-3 sm:mb-4 text-neutral-off-white drop-shadow-lg font-tai-heritage text-base sm:text-lg">Legal</h3>
            <ul className="space-y-2 text-sm sm:text-base">
              <li><Link href="/privacy" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">Privacy Policy</Link></li>
              <li><Link href="/terms" className="text-brand-beige/70 hover:text-brand-gold drop-shadow-sm font-sans transition-colors">Terms of Service</Link></li>
            </ul>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-8 sm:mt-10 border-t border-brand-sage/20 pt-6 sm:pt-8 text-center text-brand-beige/60 text-xs sm:text-sm drop-shadow-sm">
          <p className="font-sans">© {new Date().getFullYear()} Vierla, Inc. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
});
