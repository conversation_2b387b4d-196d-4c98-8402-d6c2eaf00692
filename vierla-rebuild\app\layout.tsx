import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>, <PERSON>, <PERSON><PERSON>_Display, <PERSON>_Script } from "next/font/google";
import "./globals.css";
import { Navbar } from "@/components/marketing/navbar";
import { Footer } from "@/components/marketing/footer";
import { ToastProvider } from "@/components/ui/toast";
import { <PERSON>ieConsent } from "@/components/ui/cookie-consent";
import { ThemeProvider } from "@/components/providers/theme-provider";

// Jost alternative - Inter for UI and body text (modern, geometric sans-serif)
const jost = Inter({
  variable: "--font-jost",
  subsets: ["latin"],
  display: "swap",
});

// Notable alternative - Oswald for H1 headlines (high-impact, uppercase font)
const notable = <PERSON>({
  variable: "--font-notable",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

// Tai Heritage Pro alternative - Playfair Display for H2/H3 section titles (elegant serif)
const taiHeritage = Playfair_Display({
  variable: "--font-tai-heritage",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

// Farsan alternative - Dancing Script for optional accent/signature text (script-like)
const farsan = Dancing_Script({
  variable: "--font-farsan",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Vierla - The AI-Powered Platform for Modern Business",
  description: "Build, manage, and grow your business with our all-in-one platform featuring AI website builder, smart invoicing, integrated CRM, and actionable analytics.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${jost.variable} ${notable.variable} ${taiHeritage.variable} ${farsan.variable} antialiased
                   bg-[var(--aurora-bg)] dark:bg-[var(--aurora-bg-dark)] text-light-off-white transition-colors duration-300`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange={false}
        >
          <ToastProvider>
            <Navbar />
            <main className="min-h-screen pt-16">
              {children}
            </main>
            <Footer />
            <CookieConsent />
          </ToastProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}