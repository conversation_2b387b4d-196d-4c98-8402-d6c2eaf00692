{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/text-shimmer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TextShimmer = registerClientReference(\n    function() { throw new Error(\"Attempted to call TextShimmer() from the server but TextShimmer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/text-shimmer.tsx <module evaluation>\",\n    \"TextShimmer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/text-shimmer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TextShimmer = registerClientReference(\n    function() { throw new Error(\"Attempted to call TextShimmer() from the server but TextShimmer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/text-shimmer.tsx\",\n    \"TextShimmer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const WordPullUp = registerClientReference(\n    function() { throw new Error(\"Attempted to call WordPullUp() from the server but WordPullUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/word-pull-up.tsx <module evaluation>\",\n    \"WordPullUp\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,gEACA", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const WordPullUp = registerClientReference(\n    function() { throw new Error(\"Attempted to call WordPullUp() from the server but WordPullUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/word-pull-up.tsx\",\n    \"WordPullUp\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4CACA", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MarqueeAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarqueeAnimation() from the server but MarqueeAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/marquee-effect.tsx <module evaluation>\",\n    \"MarqueeAnimation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kEACA", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MarqueeAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarqueeAnimation() from the server but MarqueeAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/marquee-effect.tsx\",\n    \"MarqueeAnimation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8CACA", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/page.tsx"], "sourcesContent": ["import { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport { BentoCard, BentoGrid } from \"@/components/ui/bento-grid\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { TextShimmer } from \"@/components/ui/text-shimmer\";\nimport { WordPullUp } from \"@/components/ui/word-pull-up\";\nimport { MarqueeAnimation } from \"@/components/ui/marquee-effect\";\nimport { LayoutTemplate, Search, Calendar, Sparkles, Smartphone, Clock, Home as HomeIcon, Star, Shield, Scissors, Palette, Sparkle, Flower2, Brush, Zap, Eye, Heart } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"page-home min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20 pt-24 sm:pt-28 lg:pt-32\">\n        <div className=\"text-center max-w-7xl mx-auto\">\n          {/* Text Shimmer for main heading */}\n          <div className=\"mb-6 sm:mb-8\">\n            <TextShimmer\n              as=\"h1\"\n              duration={3}\n              className=\"hero-title text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-tight sm:leading-none text-light-off-white drop-shadow-lg font-notable\"\n            >\n              SELF-CARE, SIMPLIFIED\n            </TextShimmer>\n          </div>\n\n          {/* Text Shimmer for description */}\n          <div className=\"mb-6 sm:mb-8\">\n            <WordPullUp\n              as=\"p\"\n              words=\"The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business.\"\n              className=\"hero-subtitle text-lg sm:text-xl md:text-2xl text-warm-beige leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm font-sans px-4\"\n              delay={0.5}\n            />\n          </div>\n\n          {/* Marquee effect for launching soon - responsive text sizing */}\n          <div className=\"mb-8 sm:mb-12 overflow-hidden py-2 sm:py-4\">\n            <MarqueeAnimation\n              direction=\"left\"\n              baseVelocity={-1}\n              className=\"text-light-off-white text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold drop-shadow-lg bg-transparent py-2 sm:py-4 font-sans\"\n            >\n              Launching Soon in Toronto & Ottawa - Ontario's Premier Beauty Marketplace\n            </MarqueeAnimation>\n          </div>\n\n\n          {/* Dual Call-to-Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-12 sm:mb-16 px-4\">\n            {/* Commented out Find Your Perfect Stylist button */}\n            {/* <Link\n              href=\"/customer-app\"\n              className=\"group flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105 min-w-[280px] justify-center bg-primary text-primary-foreground\"\n            >\n              <Search className=\"mr-3 w-6 h-6\" />\n              Find Your Perfect Stylist\n              <Sparkles className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n            </Link> */}\n\n            <Link href=\"/providers\" className=\"w-full sm:w-auto\">\n              <ShinyButton size=\"lg\" className=\"group gap-2 w-full sm:w-auto min-w-[280px] sm:min-w-[320px] justify-center\">\n                <LayoutTemplate className=\"w-5 h-5 md:w-6 md:h-6 flex-shrink-0\" />\n                <span className=\"flex-1 px-2 sm:px-4 text-base sm:text-lg\">Grow Your Business</span>\n                <svg className=\"w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </ShinyButton>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works Section - Expanded */}\n      <section className=\"relative z-10 py-12 sm:py-16 lg:py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12 sm:mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-light-off-white mb-4 sm:mb-6 drop-shadow-lg font-tai-heritage\">\n              How Vierla Works\n            </h2>\n            <p className=\"text-lg sm:text-xl text-warm-beige max-w-4xl mx-auto drop-shadow-sm mb-6 sm:mb-8 font-sans px-4\">\n              Experience beauty services like never before. Our platform connects you with verified professionals who deliver exceptional results, whether at your location or their studio.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center px-4\">\n              <Link href=\"/apply\" className=\"w-full sm:w-auto\">\n                <ShinyButton size=\"md\" className=\"px-6 sm:px-8 py-3 text-base sm:text-lg font-semibold w-full sm:w-auto\">\n                  Book Your Service\n                </ShinyButton>\n              </Link>\n              <button className=\"px-6 sm:px-8 py-3 text-base sm:text-lg font-semibold bg-transparent border border-sage rounded-lg text-light-off-white hover:bg-sage/20 transition-all duration-300 drop-shadow-lg font-sans w-full sm:w-auto\">\n                Watch Demo\n              </button>\n            </div>\n          </div>\n\n          {/* Main Process Steps - Responsive Grid */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto mb-12 sm:mb-16\">\n            {/* 1. Discover */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-4 sm:p-6\">\n                <div className=\"w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30\">\n                  <Search className=\"w-8 h-8 sm:w-10 sm:h-10 text-sage\" />\n                </div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage\">1. Discover</h3>\n                <p className=\"text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans\">\n                  Browse our curated network of verified beauty professionals. Filter by service type, location, availability, and ratings.\n                </p>\n                <ul className=\"text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans\">\n                  <li>• 500+ verified professionals</li>\n                  <li>• Real customer reviews</li>\n                  <li>• Portfolio galleries</li>\n                  <li>• Instant availability</li>\n                </ul>\n              </div>\n            </GoldenGlowingCardContainer>\n\n            {/* 2. Book */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-4 sm:p-6\">\n                <div className=\"w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30\">\n                  <Calendar className=\"w-8 h-8 sm:w-10 sm:h-10 text-sage\" />\n                </div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage\">2. Book</h3>\n                <p className=\"text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans\">\n                  Select your preferred service, date, and time. Choose between mobile service or studio visit based on your preference.\n                </p>\n                <ul className=\"text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans\">\n                  <li>• Real-time scheduling</li>\n                  <li>• Mobile or studio options</li>\n                  <li>• Instant confirmation</li>\n                  <li>• Easy rescheduling</li>\n                </ul>\n              </div>\n            </GoldenGlowingCardContainer>\n\n            {/* 3. Pay */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-4 sm:p-6\">\n                <div className=\"w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30\">\n                  <svg className=\"w-8 h-8 sm:w-10 sm:h-10 text-sage\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage\">3. Secure Payment</h3>\n                <p className=\"text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans\">\n                  Complete your booking with our secure payment system. Multiple payment options with full protection and easy refunds.\n                </p>\n                <ul className=\"text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans\">\n                  <li>• Bank-level security</li>\n                  <li>• Multiple payment methods</li>\n                  <li>• Transparent pricing</li>\n                  <li>• Money-back guarantee</li>\n                </ul>\n              </div>\n            </GoldenGlowingCardContainer>\n\n            {/* 4. Relax */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-4 sm:p-6\">\n                <div className=\"w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30\">\n                  <Sparkles className=\"w-8 h-8 sm:w-10 sm:h-10 text-sage\" />\n                </div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage\">4. Enjoy</h3>\n                <p className=\"text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans\">\n                  Relax and enjoy your premium beauty service. Our professionals arrive with everything needed for an exceptional experience.\n                </p>\n                <ul className=\"text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans\">\n                  <li>• Professional equipment</li>\n                  <li>• Premium products</li>\n                  <li>• Personalized service</li>\n                  <li>• Follow-up care tips</li>\n                </ul>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n\n          {/* Additional Benefits */}\n          <div className=\"max-w-6xl mx-auto\">\n            <GoldenGlowingCardContainer>\n              <div className=\"p-4 sm:p-6 lg:p-8 text-center\">\n                <h3 className=\"text-2xl sm:text-3xl font-bold text-light-off-white mb-4 sm:mb-6 drop-shadow-lg font-tai-heritage\">Why Choose Vierla?</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\">\n                  <div>\n                    <div className=\"w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border border-sage/30\">\n                      <svg className=\"w-6 h-6 sm:w-8 sm:h-8 text-sage\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"text-lg sm:text-xl font-semibold text-light-off-white mb-2 font-tai-heritage\">Verified Professionals</h4>\n                    <p className=\"text-sm sm:text-base text-warm-beige font-sans\">All professionals are background-checked, licensed, and insured for your peace of mind.</p>\n                  </div>\n                  <div>\n                    <div className=\"w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border border-sage/30\">\n                      <svg className=\"w-6 h-6 sm:w-8 sm:h-8 text-sage\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"text-lg sm:text-xl font-semibold text-light-off-white mb-2 font-tai-heritage\">Flexible Scheduling</h4>\n                    <p className=\"text-warm-beige font-sans\">Book services that fit your schedule, with same-day availability and easy rescheduling options.</p>\n                  </div>\n                  <div>\n                    <div className=\"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border border-sage/30\">\n                      <svg className=\"w-8 h-8 text-sage\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"text-xl font-semibold text-light-off-white mb-2 font-tai-heritage\">Satisfaction Guaranteed</h4>\n                    <p className=\"text-warm-beige font-sans\">Not happy with your service? We'll make it right with our 100% satisfaction guarantee.</p>\n                  </div>\n                </div>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Services Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl font-black text-light-off-white mb-6 drop-shadow-lg font-notable\">PREMIUM BEAUTY SERVICES</h2>\n            <p className=\"text-xl text-warm-beige max-w-4xl mx-auto drop-shadow-sm mb-4 font-sans\">\n              Experience luxury beauty services delivered by certified professionals. From quick touch-ups to complete transformations - we bring the salon experience to you.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4 text-sm text-warm-beige/80\">\n              <span className=\"px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans\">\n                <Clock className=\"w-4 h-4\" /> Same-day availability\n              </span>\n              <span className=\"px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans\">\n                <HomeIcon className=\"w-4 h-4\" /> Mobile & studio options\n              </span>\n              <span className=\"px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans\">\n                <Star className=\"w-4 h-4\" /> 4.9+ average rating\n              </span>\n              <span className=\"px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans\">\n                <Shield className=\"w-4 h-4\" /> Fully insured professionals\n              </span>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 max-w-7xl mx-auto\">\n            {[\n              {\n                title: \"Master Barbers\",\n                subtitle: \"Precision cuts & grooming\",\n                services: [\"• Signature cuts & fades\", \"• Expert beard sculpting\", \"• Luxury hot towel shaves\", \"• Modern styling\"],\n              },\n              {\n                title: \"Glam Makeup\",\n                subtitle: \"Flawless looks for any occasion\",\n                services: [\"• Red-carpet ready glam\", \"• Bridal perfection\", \"• Special event makeup\"],\n              },\n              {\n                title: \"Hair Salons\",\n                subtitle: \"Complete hair transformations\",\n                services: [\"• Color & cut specialists\", \"• Luxury blowouts\", \"• Keratin treatments\", \"• Creative styling\"],\n              },\n              {\n                title: \"Loc Specialists\",\n                subtitle: \"Expert loc care & styling\",\n                services: [\"• Professional maintenance\", \"• Precision retwisting\", \"• Creative loc styling\", \"• Repair & restoration\"],\n              },\n              {\n                title: \"Braid Artists\",\n                subtitle: \"Protective & decorative styles\",\n                services: [\"• Designer box braids\", \"• Intricate cornrows\", \"• French braid mastery\", \"• Custom protective styles\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Nail Studios\",\n                subtitle: \"Manicure & pedicure perfection\",\n                services: [\"• Luxury manicures\", \"• Spa pedicures\", \"• Custom nail art\", \"• Long-lasting gel\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Skincare Experts\",\n                subtitle: \"Clinical-grade treatments\",\n                services: [\"• Deep cleansing facials\", \"• Professional peels\", \"• Advanced microneedling\", \"• Hydrating treatments\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Massage Therapy\",\n                subtitle: \"Therapeutic & relaxation\",\n                services: [\"• Stress-relief massage\", \"• Deep tissue therapy\", \"• Hot stone luxury\", \"• Aromatherapy bliss\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Brows\",\n                services: [\"• Eyebrow shaping\", \"• Threading\", \"• Tinting\", \"• Microblading\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Henna\",\n                services: [\"• Traditional henna\", \"• Bridal designs\", \"• Body art\", \"• Custom patterns\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Waxing\",\n                services: [\"• Full body wax\", \"• Brazilian wax\", \"• Facial wax\", \"• Leg wax\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Laser Hair Removal\",\n                services: [\"• Full body laser\", \"• Facial laser\", \"• Bikini laser\", \"• Underarm laser\"],\n                mobileHidden: true,\n              },\n            ].map((service, index) => (\n              <div key={index} className={service.mobileHidden ? \"hidden md:block\" : \"\"}>\n                <GoldenGlowingCardContainer>\n                  <div className=\"text-center h-full flex flex-col justify-between opacity-70\">\n                    <div>\n                      <h3 className=\"text-xl font-bold text-light-off-white mb-4 drop-shadow-lg font-tai-heritage\">\n                        {service.title}\n                      </h3>\n                      {service.subtitle && (\n                        <p className=\"text-sm text-sage mb-3 font-sans italic\">{service.subtitle}</p>\n                      )}\n                      <div className=\"text-warm-beige/80 text-sm leading-relaxed text-left drop-shadow-sm font-sans\">\n                        {service.services.map((serviceItem, idx) => (\n                          <div key={idx} className=\"mb-1\">\n                            {serviceItem}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </GoldenGlowingCardContainer>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Competitor Comparison Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl font-black text-light-off-white mb-6 drop-shadow-lg font-notable\">\n              WHY CHOOSE VIERLA OVER OTHERS?\n            </h2>\n            <p className=\"text-xl text-warm-beige max-w-4xl mx-auto drop-shadow-sm font-sans\">\n              See how Vierla stands out from traditional booking platforms and competitors\n            </p>\n          </div>\n\n          <div className=\"max-w-6xl mx-auto\">\n            <GoldenGlowingCardContainer>\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full text-left\">\n                  <thead>\n                    <tr className=\"border-b border-sage/20\">\n                      <th className=\"py-4 px-6 text-light-off-white font-bold text-lg font-tai-heritage\">Features</th>\n                      <th className=\"py-4 px-6 text-center\">\n                        <div className=\"flex flex-col items-center\">\n                          <span className=\"text-muted-gold font-bold text-lg mb-1 font-tai-heritage\">Vierla</span>\n                          <span className=\"text-xs bg-muted-gold/20 px-2 py-1 rounded-full text-muted-gold font-sans\">Premium</span>\n                        </div>\n                      </th>\n                      <th className=\"py-4 px-6 text-center text-warm-beige/70 font-medium font-sans\">Traditional Salons</th>\n                      <th className=\"py-4 px-6 text-center text-warm-beige/70 font-medium font-sans\">Other Platforms</th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"text-warm-beige/80 font-sans\">\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Mobile Service Available</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Limited</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Verified Professionals</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Basic</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Same-Day Booking</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Rare</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Transparent Pricing</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Variable</td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Hidden Fees</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Money-Back Guarantee</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                    </tr>\n                    <tr>\n                      <td className=\"py-4 px-6 font-medium\">24/7 Customer Support</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Business Hours</td>\n                      <td className=\"py-4 px-6 text-center text-muted-gold\">Limited</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </GoldenGlowingCardContainer>\n\n            <div className=\"flex flex-col items-center justify-center text-center mt-12\">\n              <Link href=\"/apply\" className=\"inline-block\">\n                <ShinyButton size=\"lg\" className=\"px-8 py-4 text-lg font-semibold\">\n                  Experience the Vierla Difference\n                </ShinyButton>\n              </Link>\n              <p className=\"text-warm-beige/70 mt-4 text-sm font-sans\">Join thousands of satisfied customers who chose Vierla</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Coming Soon to Mobile Apps Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center py-8\">\n                <div className=\"w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30\">\n                  <Smartphone className=\"w-10 h-10 text-sage\" />\n                </div>\n                <h2 className=\"text-3xl md:text-4xl font-black text-light-off-white mb-4 drop-shadow-lg font-tai-heritage\">\n                  Coming Soon to Mobile\n                </h2>\n                <p className=\"text-lg md:text-xl text-warm-beige mb-6 max-w-2xl mx-auto drop-shadow-sm font-sans\">\n                  Get ready for the ultimate beauty experience on your phone. Our mobile apps for Android and iOS are launching soon!\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n                  <div className=\"flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75\">\n                    <svg className=\"w-8 h-8 mr-3 text-neutral-off-white\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <div className=\"text-left\">\n                      <div className=\"text-xs text-brand-beige/70\">Coming Soon to</div>\n                      <div className=\"text-lg font-semibold text-neutral-off-white\">Apple App Store</div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75\">\n                    <svg className=\"w-8 h-8 mr-3 text-neutral-off-white\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <div className=\"text-left\">\n                      <div className=\"text-xs text-brand-beige/70\">Coming Soon to</div>\n                      <div className=\"text-lg font-semibold text-neutral-off-white\">Google Play Store</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,cAAW;gCACV,IAAG;gCACH,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uIAAA,CAAA,aAAU;gCACT,IAAG;gCACH,OAAM;gCACN,WAAU;gCACV,OAAO;;;;;;;;;;;sCAKX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sIAAA,CAAA,mBAAgB;gCACf,WAAU;gCACV,cAAc,CAAC;gCACf,WAAU;0CACX;;;;;;;;;;;sCAOH,8OAAC;4BAAI,WAAU;sCAWb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC,oIAAA,CAAA,UAAW;oCAAC,MAAK;oCAAK,WAAU;;sDAC/B,8OAAC,0NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;4CAAqF,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5I,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiH;;;;;;8CAG/H,8OAAC;oCAAE,WAAU;8CAAkG;;;;;;8CAG/G,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAC5B,cAAA,8OAAC,oIAAA,CAAA,UAAW;gDAAC,MAAK;gDAAK,WAAU;0DAAwE;;;;;;;;;;;sDAI3G,8OAAC;4CAAO,WAAU;sDAAgN;;;;;;;;;;;;;;;;;;sCAOtO,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DAAmG;;;;;;0DACjH,8OAAC;gDAAE,WAAU;0DAA6F;;;;;;0DAG1G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAMV,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DAAmG;;;;;;0DACjH,8OAAC;gDAAE,WAAU;0DAA6F;;;;;;0DAG1G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAMV,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAoC,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC3F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAmG;;;;;;0DACjH,8OAAC;gDAAE,WAAU;0DAA6F;;;;;;0DAG1G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAMV,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DAAmG;;;;;;0DACjH,8OAAC;gDAAE,WAAU;0DAA6F;;;;;;0DAG1G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;0CACzB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAClH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAkC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAG,WAAU;sEAA+E;;;;;;sEAC7F,8OAAC;4DAAE,WAAU;sEAAiD;;;;;;;;;;;;8DAEhE,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAkC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAG,WAAU;sEAA+E;;;;;;sEAC7F,8OAAC;4DAAE,WAAU;sEAA4B;;;;;;;;;;;;8DAE3C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAoB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC3E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAG,WAAU;sEAAoE;;;;;;sEAClF,8OAAC;4DAAE,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4E;;;;;;8CAC1F,8OAAC;oCAAE,WAAU;8CAA0E;;;;;;8CAGvF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAE/B,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,mMAAA,CAAA,OAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAElC,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAE9B,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;sCAKpC,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAA4B;wCAA4B;wCAA6B;qCAAmB;gCACrH;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAA2B;wCAAuB;qCAAyB;gCACxF;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAA6B;wCAAqB;wCAAwB;qCAAqB;gCAC5G;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAA8B;wCAA0B;wCAA0B;qCAAyB;gCACxH;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAAyB;wCAAwB;wCAA0B;qCAA6B;oCACnH,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAAsB;wCAAmB;wCAAqB;qCAAqB;oCAC9F,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAA4B;wCAAwB;wCAA4B;qCAAyB;oCACpH,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,UAAU;wCAAC;wCAA2B;wCAAyB;wCAAsB;qCAAuB;oCAC5G,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAqB;wCAAe;wCAAa;qCAAiB;oCAC7E,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAuB;wCAAoB;wCAAc;qCAAoB;oCACxF,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAmB;wCAAmB;wCAAgB;qCAAY;oCAC7E,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAqB;wCAAkB;wCAAkB;qCAAmB;oCACvF,cAAc;gCAChB;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;oCAAgB,WAAW,QAAQ,YAAY,GAAG,oBAAoB;8CACrE,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;kDACzB,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;oDAEf,QAAQ,QAAQ,kBACf,8OAAC;wDAAE,WAAU;kEAA2C,QAAQ,QAAQ;;;;;;kEAE1E,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,oBAClC,8OAAC;gEAAc,WAAU;0EACtB;+DADO;;;;;;;;;;;;;;;;;;;;;;;;;;mCAZZ;;;;;;;;;;;;;;;;;;;;;0BA2BlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4E;;;;;;8CAG1F,8OAAC;oCAAE,WAAU;8CAAqE;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DACC,cAAA,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAAqE;;;;;;0EACnF,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAA2D;;;;;;sFAC3E,8OAAC;4EAAK,WAAU;sFAA4E;;;;;;;;;;;;;;;;;0EAGhG,8OAAC;gEAAG,WAAU;0EAAiE;;;;;;0EAC/E,8OAAC;gEAAG,WAAU;0EAAiE;;;;;;;;;;;;;;;;;8DAGnF,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA6B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACpF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;;;;;;;sEAExD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;;;;;;;sEAExD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA6B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACpF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;;;;;;;sEAExD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;;;;;;;sEAExD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA6B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACpF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA6B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACpF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sEAI3E,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAwB;;;;;;8EACtC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;wEAA4B,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACnF,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,8OAAC;oEAAG,WAAU;8EAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAC5B,cAAA,8OAAC,oIAAA,CAAA,UAAW;gDAAC,MAAK;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAIrE,8OAAC;4CAAE,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAG,WAAU;kDAA6F;;;;;;kDAG3G,8OAAC;wCAAE,WAAU;kDAAqF;;;;;;kDAGlG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAsC,SAAQ;wDAAY,MAAK;kEAC5E,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA8B;;;;;;0EAC7C,8OAAC;gEAAI,WAAU;0EAA+C;;;;;;;;;;;;;;;;;;0DAGlE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAsC,SAAQ;wDAAY,MAAK;kEAC5E,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA8B;;;;;;0EAC7C,8OAAC;gEAAI,WAAU;0EAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYpF", "debugId": null}}]}