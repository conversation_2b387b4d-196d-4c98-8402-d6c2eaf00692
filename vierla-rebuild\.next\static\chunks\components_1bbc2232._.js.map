{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// A full-page wrapper component for the aurora background\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <main\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center bg-[var(--aurora-bg)] dark:bg-[var(--aurora-bg-dark)] text-slate-950 transition-bg\",\n        className\n      )}\n      {...props}\n    >\n      <AuroraBackgroundLayer showRadialGradient={showRadialGradient} />\n      {children}\n    </main>\n  );\n};\n\n// A memoized background-only layer for embedding in existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div\n        className={cn(\n          \"fixed inset-0 -z-10 overflow-hidden\",\n          className\n        )}\n      >\n        <div\n          className={cn(\n            `aurora-background\n            [--white-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-light)_0%,var(--aurora-stripe-light)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-light)_16%)]\n            [--dark-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-dark)_0%,var(--aurora-stripe-dark)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-dark)_16%)]\n            [--aurora:repeating-linear-gradient(100deg,var(--aurora-flow-1)_10%,var(--aurora-flow-2)_15%,var(--aurora-flow-3)_20%,var(--aurora-flow-4)_25%,var(--aurora-flow-5)_30%)]\n            [background-image:var(--white-gradient),var(--aurora)]\n            dark:[background-image:var(--dark-gradient),var(--aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--white-gradient),var(--aurora)]\n            after:dark:[background-image:var(--dark-gradient),var(--aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform\n            transform-gpu`,\n            showRadialGradient &&\n              `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mJACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAsB,oBAAoB;;;;;;YAC1C;;;;;;;AAGP;KAlBa;AAqBN,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EACtC;QAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uCACA;kBAGF,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,iwCAgBD,sBACG;;;;;;;;;;;AAKb;MAvCW;AA0Cb,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        bg: backgroundColor || \"var(--master-button-primary-bg)\",\n        shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        text: textColor || \"var(--master-button-primary-text)\"\n      },\n      secondary: {\n        bg: backgroundColor || \"var(--master-button-secondary-bg)\",\n        shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        text: textColor || \"var(--master-button-secondary-text)\"\n      },\n      accent: {\n        bg: backgroundColor || \"var(--master-brand-accent)\",\n        shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        text: textColor || \"var(--master-text-on-dark)\"\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          color: colors.text,\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C;QAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4CAA4C;IAC5C,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;YACA,WAAW;gBACT,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;YACA,QAAQ;gBACN,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO,OAAO,IAAI;oBAClB,WACE,AAAC,0BAAiG,OAAxE,OAAO,OAAO,EAAC,2DAAwE,OAAf,OAAO,OAAO,EAAC;gBACrH;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,AAAC,0BAAgE,OAAvC,OAAO,OAAO,EAAC,0BAA+D,OAAvC,OAAO,OAAO,EAAC,0BAAuC,OAAf,OAAO,OAAO,EAAC;gBACrI;gBACA,WAAU;;;;;;;;;;;;AAIlB;KAnGa;uCAqGE", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/dropdown-select.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ChevronDown, Check } from 'lucide-react';\n\nexport interface DropdownOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\nexport interface DropdownSelectProps {\n  options: DropdownOption[];\n  value?: string;\n  placeholder?: string;\n  onChange?: (value: string) => void;\n  className?: string;\n  disabled?: boolean;\n  error?: string;\n  label?: string;\n  required?: boolean;\n}\n\nexport function DropdownSelect({\n  options,\n  value,\n  placeholder = \"Select an option\",\n  onChange,\n  className,\n  disabled = false,\n  error,\n  label,\n  required = false,\n}: DropdownSelectProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedValue, setSelectedValue] = useState(value || '');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const selectedOption = options.find(option => option.value === selectedValue);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  useEffect(() => {\n    if (value !== undefined) {\n      setSelectedValue(value);\n    }\n  }, [value]);\n\n  const handleSelect = (optionValue: string) => {\n    setSelectedValue(optionValue);\n    setIsOpen(false);\n    onChange?.(optionValue);\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent) => {\n    if (disabled) return;\n\n    switch (event.key) {\n      case 'Enter':\n      case ' ':\n        event.preventDefault();\n        setIsOpen(!isOpen);\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        break;\n      case 'ArrowDown':\n        event.preventDefault();\n        if (!isOpen) {\n          setIsOpen(true);\n        } else {\n          // Focus next option\n          const currentIndex = options.findIndex(opt => opt.value === selectedValue);\n          const nextIndex = Math.min(currentIndex + 1, options.length - 1);\n          if (options[nextIndex] && !options[nextIndex].disabled) {\n            handleSelect(options[nextIndex].value);\n          }\n        }\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        if (isOpen) {\n          const currentIndex = options.findIndex(opt => opt.value === selectedValue);\n          const prevIndex = Math.max(currentIndex - 1, 0);\n          if (options[prevIndex] && !options[prevIndex].disabled) {\n            handleSelect(options[prevIndex].value);\n          }\n        }\n        break;\n    }\n  };\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      {label && (\n        <label className=\"block text-sm font-medium text-mantle-100 mb-2 font-sans\">\n          {label}\n          {required && <span className=\"text-error ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div ref={dropdownRef} className=\"relative\">\n        <button\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          onKeyDown={handleKeyDown}\n          disabled={disabled}\n          className={cn(\n            \"relative w-full rounded-lg px-4 py-3 text-left font-sans transition-all duration-200\",\n            \"bg-light-charcoal border border-sage/30 text-light-off-white\",\n            \"focus:outline-none focus:ring-2 focus:ring-muted-gold/30 focus:border-muted-gold\",\n            \"hover:border-sage/50\",\n            disabled && \"opacity-50 cursor-not-allowed\",\n            error && \"border-error focus:ring-error/30\",\n            isOpen && \"ring-2 ring-muted-gold/30 border-muted-gold\"\n          )}\n          aria-haspopup=\"listbox\"\n          aria-expanded={isOpen}\n          aria-labelledby={label ? `${label}-label` : undefined}\n        >\n          <span className=\"block truncate\">\n            {selectedOption ? selectedOption.label : placeholder}\n          </span>\n          <span className=\"absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none\">\n            <ChevronDown\n              className={cn(\n                \"w-4 h-4 text-mantle-300 transition-transform duration-200\",\n                isOpen && \"rotate-180\"\n              )}\n            />\n          </span>\n        </button>\n\n        {isOpen && (\n          <div className=\"absolute z-50 w-full mt-1 bg-neutral-charcoal-dark/80 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/70 border border-brand-sage/20 rounded-lg shadow-2xl max-h-60 overflow-auto\">\n            <ul role=\"listbox\" className=\"py-1\">\n              {options.map((option) => (\n                <li\n                  key={option.value}\n                  role=\"option\"\n                  aria-selected={selectedValue === option.value}\n                  className={cn(\n                    \"relative cursor-pointer select-none py-2 pl-10 pr-4 text-light-off-white font-sans\",\n                    \"hover:bg-light-charcoal focus:bg-light-charcoal\",\n                    option.disabled && \"opacity-50 cursor-not-allowed\",\n                    selectedValue === option.value && \"bg-light-charcoal\"\n                  )}\n                  onClick={() => !option.disabled && handleSelect(option.value)}\n                >\n                  <span className=\"block truncate\">{option.label}</span>\n                  {selectedValue === option.value && (\n                    <span className=\"absolute inset-y-0 left-0 flex items-center pl-3\">\n                      <Check className=\"w-4 h-4 text-terracotta\" />\n                    </span>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-1 text-sm text-error font-sans\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAwBO,SAAS,eAAe,KAUT;QAVS,EAC7B,OAAO,EACP,KAAK,EACL,cAAc,kBAAkB,EAChC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACI,GAVS;;IAW7B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IAC5D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;+DAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;4CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;mCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,WAAW;gBACvB,iBAAiB;YACnB;QACF;mCAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB,UAAU;QACV,qBAAA,+BAAA,SAAW;IACb;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU;QAEd,OAAQ,MAAM,GAAG;YACf,KAAK;YACL,KAAK;gBACH,MAAM,cAAc;gBACpB,UAAU,CAAC;gBACX;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,CAAC,QAAQ;oBACX,UAAU;gBACZ,OAAO;oBACL,oBAAoB;oBACpB,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBAC5D,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG,QAAQ,MAAM,GAAG;oBAC9D,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACtD,aAAa,OAAO,CAAC,UAAU,CAAC,KAAK;oBACvC;gBACF;gBACA;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,QAAQ;oBACV,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBAC5D,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG;oBAC7C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACtD,aAAa,OAAO,CAAC,UAAU,CAAC,KAAK;oBACvC;gBACF;gBACA;QACJ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B,uBACC,6LAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;0BAInD,6LAAC;gBAAI,KAAK;gBAAa,WAAU;;kCAC/B,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,CAAC,YAAY,UAAU,CAAC;wBACvC,WAAW;wBACX,UAAU;wBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wFACA,gEACA,oFACA,wBACA,YAAY,iCACZ,SAAS,oCACT,UAAU;wBAEZ,iBAAc;wBACd,iBAAe;wBACf,mBAAiB,QAAQ,AAAC,GAAQ,OAAN,OAAM,YAAU;;0CAE5C,6LAAC;gCAAK,WAAU;0CACb,iBAAiB,eAAe,KAAK,GAAG;;;;;;0CAE3C,6LAAC;gCAAK,WAAU;0CACd,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6DACA,UAAU;;;;;;;;;;;;;;;;;oBAMjB,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,MAAK;4BAAU,WAAU;sCAC1B,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,MAAK;oCACL,iBAAe,kBAAkB,OAAO,KAAK;oCAC7C,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sFACA,mDACA,OAAO,QAAQ,IAAI,iCACnB,kBAAkB,OAAO,KAAK,IAAI;oCAEpC,SAAS,IAAM,CAAC,OAAO,QAAQ,IAAI,aAAa,OAAO,KAAK;;sDAE5D,6LAAC;4CAAK,WAAU;sDAAkB,OAAO,KAAK;;;;;;wCAC7C,kBAAkB,OAAO,KAAK,kBAC7B,6LAAC;4CAAK,WAAU;sDACd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;mCAdhB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;YAwB5B,uBACC,6LAAC;gBAAE,WAAU;0BAAqC;;;;;;;;;;;;AAI1D;GAzJgB;KAAA", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/multistep-form.tsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { cn } from \"@/lib/utils\";\nimport { DropdownSelect, type DropdownOption } from \"@/components/ui/dropdown-select\";\n\ninterface ApplicationData {\n  // Personal Information\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone: string;\n\n  // Professional Information\n  businessName: string;\n  services: string[];\n  experience: string;\n  certifications: string[];\n\n  // Location & Availability\n  serviceAreas: string[];\n  availability: string[];\n  travelRadius: string;\n\n  // Business Details\n  insurance: boolean;\n  license: string;\n  portfolio: string;\n  rates: string;\n\n  // Additional Information\n  motivation: string;\n  references: string;\n}\n\nconst serviceOptions = [\n  'Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Blowouts',\n  'Makeup Application', 'Bridal Makeup', 'Special Event Makeup',\n  'Manicures', 'Pedicures', 'Nail Art', 'Gel Polish',\n  'Eyebrow Shaping', 'Eyebrow Threading', 'Eyebrow Tinting',\n  'Eyelash Extensions', 'Lash Lifts', 'Lash Tinting',\n  'Braiding', 'Box Braids', 'Cornrows', 'Protective Styles',\n  'Loc Maintenance', 'Loc Styling', 'Retwisting',\n  'Beard Trimming', 'Hot Towel Shaves', 'Men\\'s Grooming'\n];\n\nconst fadeInUp = {\n  hidden: { opacity: 0, y: 20 },\n  visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },\n};\n\nconst contentVariants = {\n  hidden: { opacity: 0, x: 50 },\n  visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },\n  exit: { opacity: 0, x: -50, transition: { duration: 0.2 } },\n};\n\nconst steps = [\n  { id: 1, name: \"Personal Information\", stepWord: \"Personal\", fields: [\"firstName\", \"lastName\", \"email\", \"phone\"] },\n  { id: 2, name: \"Professional Details\", stepWord: \"Professional\", fields: [\"businessName\", \"services\", \"experience\", \"certifications\"] },\n  { id: 3, name: \"Service Areas\", stepWord: \"Location\", fields: [\"serviceAreas\", \"availability\", \"travelRadius\"] },\n  { id: 4, name: \"Business Information\", stepWord: \"Business\", fields: [\"insurance\", \"license\", \"portfolio\", \"rates\"] },\n  { id: 5, name: \"Additional Details\", stepWord: \"Details\", fields: [\"motivation\", \"references\"] },\n  { id: 6, name: \"Review & Submit\", stepWord: \"Review\", fields: [] },\n];\n\nexport function MultiStepForm() {\n  const [currentStep, setCurrentStep] = useState(0);\n\n  // Experience options for dropdown\n  const experienceOptions: DropdownOption[] = [\n    { value: '0-1', label: 'Less than 1 year' },\n    { value: '1-3', label: '1-3 years' },\n    { value: '3-5', label: '3-5 years' },\n    { value: '5-10', label: '5-10 years' },\n    { value: '10+', label: '10+ years' }\n  ];\n\n  const [formData, setFormData] = useState<ApplicationData>({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    phone: \"\",\n    businessName: \"\",\n    services: [],\n    experience: \"\",\n    certifications: [],\n    serviceAreas: [],\n    availability: [],\n    travelRadius: \"\",\n    insurance: false,\n    license: \"\",\n    portfolio: \"\",\n    rates: \"\",\n    motivation: \"\",\n    references: \"\",\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n\n  const next = () => setCurrentStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));\n  const prev = () => setCurrentStep((prev) => (prev > 0 ? prev - 1 : prev));\n\n  const updateFormData = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleArrayToggle = (field: keyof ApplicationData, value: string) => {\n    const currentArray = formData[field] as string[];\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value];\n    updateFormData(field, newArray);\n  };\n\n  // Check if step is valid for next button\n  const isStepValid = () => {\n    switch (currentStep) {\n      case 0:\n        return formData.firstName.trim() !== \"\" && formData.lastName.trim() !== \"\" && formData.email.trim() !== \"\";\n      case 1:\n        return formData.businessName.trim() !== \"\" && formData.services.length > 0;\n      case 2:\n        return formData.serviceAreas.length > 0;\n      case 3:\n        return formData.portfolio.trim() !== \"\";\n      case 4:\n        return formData.motivation.trim() !== \"\";\n      default:\n        return true;\n    }\n  };\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true);\n    try {\n      // Submit to the exact same API endpoint as SOURCE\n      const response = await fetch('/api/professional-application', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...formData,\n          timestamp: new Date().toISOString()\n        }),\n      });\n\n      if (response.ok) {\n        setSubmitSuccess(true);\n      } else {\n        throw new Error('Submission failed');\n      }\n    } catch (error) {\n      // Fallback to localStorage (same as SOURCE)\n      const existingApplications = JSON.parse(localStorage.getItem('vierla-applications') || '[]');\n      existingApplications.push({\n        ...formData,\n        timestamp: new Date().toISOString(),\n        status: 'pending'\n      });\n      localStorage.setItem('vierla-applications', JSON.stringify(existingApplications));\n      setSubmitSuccess(true);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (submitSuccess) {\n    return (\n      <Card className=\"w-full max-w-2xl mx-auto bg-white/10 backdrop-blur-md border border-white/20\">\n        <CardContent className=\"p-8 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4 text-primary drop-shadow-lg\">Application Submitted!</h2>\n          <p className=\"text-vierla-text/80 mb-6 drop-shadow-sm\">\n            Thank you for your application. We'll review it and get back to you within 2-3 business days.\n          </p>\n          <Button asChild>\n            <a href=\"/\">Return to Homepage</a>\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      {/* Progress Indicator */}\n      <motion.div\n        className=\"mb-8\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <div className=\"flex justify-between mb-2\">\n          {steps.map((step, index) => (\n            <motion.div\n              key={index}\n              className=\"flex flex-col items-center\"\n              whileHover={{ scale: 1.1 }}\n            >\n              <motion.div\n                className={cn(\n                  \"w-4 h-4 rounded-full cursor-pointer transition-colors duration-300\",\n                  index < currentStep\n                    ? \"bg-sage\"\n                    : index === currentStep\n                      ? \"bg-sage ring-4 ring-sage/20\"\n                      : \"bg-light-charcoal\",\n                )}\n                onClick={() => {\n                  // Only allow going back or to completed steps\n                  if (index <= currentStep) {\n                    setCurrentStep(index);\n                  }\n                }}\n                whileTap={{ scale: 0.95 }}\n              />\n              <motion.span\n                className={cn(\n                  \"text-xs mt-1.5 hidden sm:block text-light-off-white drop-shadow-sm font-sans\",\n                  index === currentStep\n                    ? \"text-sage font-medium\"\n                    : \"text-light-off-white/60\",\n                )}\n              >\n                {step.stepWord}\n              </motion.span>\n            </motion.div>\n          ))}\n        </div>\n        <div className=\"w-full bg-light-charcoal h-1.5 rounded-full overflow-hidden mt-2\">\n          <motion.div\n            className=\"h-full bg-sage\"\n            initial={{ width: 0 }}\n            animate={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}\n            transition={{ duration: 0.3 }}\n          />\n        </div>\n        <p className=\"text-center text-sm text-light-off-white drop-shadow-sm mt-2 font-sans\">\n          Step {currentStep + 1} of {steps.length}: {steps[currentStep].name}\n        </p>\n      </motion.div>\n\n      <Card className=\"w-full bg-light-charcoal border border-sage/30 shadow-2xl rounded-3xl\">\n        <CardHeader>\n          <CardTitle className=\"text-light-off-white drop-shadow-lg font-tai-heritage\">{steps[currentStep].name}</CardTitle>\n          <CardDescription className=\"text-warm-beige drop-shadow-sm font-sans\">\n            {currentStep === 0 && \"Let's start with your basic information\"}\n            {currentStep === 1 && \"Tell us about your professional background\"}\n            {currentStep === 2 && \"Where do you provide services?\"}\n            {currentStep === 3 && \"Business credentials and portfolio\"}\n            {currentStep === 4 && \"Help us understand your motivation\"}\n            {currentStep === 5 && \"Review your information before submitting\"}\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={currentStep}\n              initial={{ x: 300, opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: -300, opacity: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              {/* Step 1: Personal Information */}\n              {currentStep === 0 && (\n                <div className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"firstName\" className=\"text-vierla-text drop-shadow-sm\">First Name</Label>\n                      <Input\n                        id=\"firstName\"\n                        value={formData.firstName}\n                        onChange={(e) => updateFormData('firstName', e.target.value)}\n                        placeholder=\"Enter your first name\"\n                        className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                        required\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"lastName\" className=\"text-vierla-text drop-shadow-sm\">Last Name</Label>\n                      <Input\n                        id=\"lastName\"\n                        value={formData.lastName}\n                        onChange={(e) => updateFormData('lastName', e.target.value)}\n                        placeholder=\"Enter your last name\"\n                        className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                        required\n                      />\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"email\" className=\"text-vierla-text drop-shadow-sm\">Email Address</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={(e) => updateFormData('email', e.target.value)}\n                      placeholder=\"Enter your email address\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"phone\" className=\"text-vierla-text drop-shadow-sm\">Phone Number</Label>\n                    <Input\n                      id=\"phone\"\n                      type=\"tel\"\n                      value={formData.phone}\n                      onChange={(e) => updateFormData('phone', e.target.value)}\n                      placeholder=\"Enter your phone number\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      required\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 2: Professional Details */}\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"businessName\" className=\"text-vierla-text drop-shadow-sm\">Business Name</Label>\n                    <Input\n                      id=\"businessName\"\n                      value={formData.businessName}\n                      onChange={(e) => updateFormData('businessName', e.target.value)}\n                      placeholder=\"Enter your business name\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-vierla-text drop-shadow-sm\">Services Offered</Label>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto\">\n                      {serviceOptions.map((service) => (\n                        <div key={service} className=\"flex items-center space-x-2 p-2 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors\">\n                          <Checkbox\n                            id={service}\n                            checked={formData.services.includes(service)}\n                            onCheckedChange={() => handleArrayToggle('services', service)}\n                          />\n                          <Label htmlFor={service} className=\"text-sm text-vierla-text drop-shadow-sm cursor-pointer\">{service}</Label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <DropdownSelect\n                      label=\"Years of Experience\"\n                      options={experienceOptions}\n                      value={formData.experience}\n                      onChange={(value) => updateFormData('experience', value)}\n                      placeholder=\"Select your experience level\"\n                      required\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 3: Service Areas */}\n              {currentStep === 2 && (\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-vierla-text drop-shadow-sm\">Service Areas</Label>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\n                      {['Toronto Downtown', 'North York', 'Scarborough', 'Etobicoke', 'Mississauga', 'Brampton', 'Ottawa Downtown', 'Kanata', 'Orleans', 'Nepean'].map((area) => (\n                        <div key={area} className=\"flex items-center space-x-2 p-2 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors\">\n                          <Checkbox\n                            id={area}\n                            checked={formData.serviceAreas.includes(area)}\n                            onCheckedChange={() => handleArrayToggle('serviceAreas', area)}\n                          />\n                          <Label htmlFor={area} className=\"text-sm text-vierla-text drop-shadow-sm cursor-pointer\">{area}</Label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"travelRadius\" className=\"text-vierla-text drop-shadow-sm\">Travel Radius (km)</Label>\n                    <Input\n                      id=\"travelRadius\"\n                      value={formData.travelRadius}\n                      onChange={(e) => updateFormData('travelRadius', e.target.value)}\n                      placeholder=\"e.g., 25\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 4: Business Information */}\n              {currentStep === 3 && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id=\"insurance\"\n                      checked={formData.insurance}\n                      onCheckedChange={(checked) => updateFormData('insurance', checked)}\n                    />\n                    <Label htmlFor=\"insurance\" className=\"text-vierla-text drop-shadow-sm\">I have professional liability insurance</Label>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"license\" className=\"text-vierla-text drop-shadow-sm\">License/Certification Numbers</Label>\n                    <Input\n                      id=\"license\"\n                      value={formData.license}\n                      onChange={(e) => updateFormData('license', e.target.value)}\n                      placeholder=\"Enter relevant license numbers\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"portfolio\" className=\"text-vierla-text drop-shadow-sm\">Portfolio/Website URL</Label>\n                    <Input\n                      id=\"portfolio\"\n                      value={formData.portfolio}\n                      onChange={(e) => updateFormData('portfolio', e.target.value)}\n                      placeholder=\"https://your-portfolio.com\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"rates\" className=\"text-vierla-text drop-shadow-sm\">Starting Rates</Label>\n                    <Textarea\n                      id=\"rates\"\n                      value={formData.rates}\n                      onChange={(e) => updateFormData('rates', e.target.value)}\n                      placeholder=\"Describe your pricing structure\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      rows={3}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 5: Additional Details */}\n              {currentStep === 4 && (\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"motivation\" className=\"text-vierla-text drop-shadow-sm\">Why do you want to join Vierla?</Label>\n                    <Textarea\n                      id=\"motivation\"\n                      value={formData.motivation}\n                      onChange={(e) => updateFormData('motivation', e.target.value)}\n                      placeholder=\"Tell us about your motivation and goals\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      rows={4}\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"references\" className=\"text-vierla-text drop-shadow-sm\">References (Optional)</Label>\n                    <Textarea\n                      id=\"references\"\n                      value={formData.references}\n                      onChange={(e) => updateFormData('references', e.target.value)}\n                      placeholder=\"Professional references or client testimonials\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      rows={3}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 6: Review & Submit */}\n              {currentStep === 5 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold text-vierla-text drop-shadow-lg\">Review Your Application</h3>\n                  <div className=\"space-y-2 text-sm text-vierla-text/90 drop-shadow-sm\">\n                    <p><strong>Name:</strong> {formData.firstName} {formData.lastName}</p>\n                    <p><strong>Email:</strong> {formData.email}</p>\n                    <p><strong>Phone:</strong> {formData.phone}</p>\n                    <p><strong>Business:</strong> {formData.businessName || 'Not specified'}</p>\n                    <p><strong>Services:</strong> {formData.services.join(', ') || 'None selected'}</p>\n                    <p><strong>Experience:</strong> {formData.experience || 'Not specified'}</p>\n                    <p><strong>Service Areas:</strong> {formData.serviceAreas.join(', ') || 'None selected'}</p>\n                    <p><strong>Insurance:</strong> {formData.insurance ? 'Yes' : 'No'}</p>\n                  </div>\n                  <div className=\"bg-white/10 p-4 rounded-lg border border-white/20\">\n                    <p className=\"text-sm text-vierla-text/70 drop-shadow-sm\">\n                      By submitting this application, you agree to our Terms of Service and Privacy Policy.\n                      We'll review your application and contact you within 2-3 business days.\n                    </p>\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          <div className=\"flex justify-between pt-6 pb-4\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <ShinyButton\n                type=\"button\"\n                variant=\"secondary\"\n                onClick={prev}\n                disabled={currentStep === 0}\n                size=\"lg\"\n                className=\"px-8 py-3 text-lg font-medium rounded-2xl\"\n              >\n                Back\n              </ShinyButton>\n            </motion.div>\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              {currentStep === steps.length - 1 ? (\n                <ShimmerButton\n                  type=\"button\"\n                  size=\"lg\"\n                  background=\"#B8956A\"\n                  shimmerColor=\"#E5D4A1\"\n                  className=\"px-8 py-3 text-lg font-medium text-[#2D2A26] rounded-2xl\"\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                >\n                  {isSubmitting ? \"Submitting...\" : \"Submit Application\"}\n                </ShimmerButton>\n              ) : (\n                <ShinyButton\n                  type=\"button\"\n                  onClick={next}\n                  disabled={!isStepValid()}\n                  size=\"lg\"\n                  className=\"px-8 py-3 text-lg font-medium rounded-2xl\"\n                >\n                  Next\n                </ShinyButton>\n              )}\n            </motion.div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAbA;;;;;;;;;;;;;AA4CA,MAAM,iBAAiB;IACrB;IAAgB;IAAgB;IAAiB;IACjD;IAAsB;IAAiB;IACvC;IAAa;IAAa;IAAY;IACtC;IAAmB;IAAqB;IACxC;IAAsB;IAAc;IACpC;IAAY;IAAc;IAAY;IACtC;IAAmB;IAAe;IAClC;IAAkB;IAAoB;CACvC;AAED,MAAM,WAAW;IACf,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC7D;AAEA,MAAM,kBAAkB;IACtB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;IAC3D,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;QAAI,YAAY;YAAE,UAAU;QAAI;IAAE;AAC5D;AAEA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,MAAM;QAAwB,UAAU;QAAY,QAAQ;YAAC;YAAa;YAAY;YAAS;SAAQ;IAAC;IACjH;QAAE,IAAI;QAAG,MAAM;QAAwB,UAAU;QAAgB,QAAQ;YAAC;YAAgB;YAAY;YAAc;SAAiB;IAAC;IACtI;QAAE,IAAI;QAAG,MAAM;QAAiB,UAAU;QAAY,QAAQ;YAAC;YAAgB;YAAgB;SAAe;IAAC;IAC/G;QAAE,IAAI;QAAG,MAAM;QAAwB,UAAU;QAAY,QAAQ;YAAC;YAAa;YAAW;YAAa;SAAQ;IAAC;IACpH;QAAE,IAAI;QAAG,MAAM;QAAsB,UAAU;QAAW,QAAQ;YAAC;YAAc;SAAa;IAAC;IAC/F;QAAE,IAAI;QAAG,MAAM;QAAmB,UAAU;QAAU,QAAQ,EAAE;IAAC;CAClE;AAEM,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kCAAkC;IAClC,MAAM,oBAAsC;QAC1C;YAAE,OAAO;YAAO,OAAO;QAAmB;QAC1C;YAAE,OAAO;YAAO,OAAO;QAAY;QACnC;YAAE,OAAO;YAAO,OAAO;QAAY;QACnC;YAAE,OAAO;YAAQ,OAAO;QAAa;QACrC;YAAE,OAAO;YAAO,OAAO;QAAY;KACpC;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,cAAc;QACd,UAAU,EAAE;QACZ,YAAY;QACZ,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,cAAc;QACd,WAAW;QACX,SAAS;QACT,WAAW;QACX,OAAO;QACP,YAAY;QACZ,YAAY;IACd;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,OAAO,IAAM,eAAe,CAAC,OAAU,OAAO,MAAM,MAAM,GAAG,IAAI,OAAO,IAAI;IAClF,MAAM,OAAO,IAAM,eAAe,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI;IAEnE,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,MAAM,eAAe,QAAQ,CAAC,MAAM;QACpC,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAC5B,eAAe,OAAO;IACxB;IAEA,yCAAyC;IACzC,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,SAAS,CAAC,IAAI,OAAO,MAAM,SAAS,QAAQ,CAAC,IAAI,OAAO,MAAM,SAAS,KAAK,CAAC,IAAI,OAAO;YAC1G,KAAK;gBACH,OAAO,SAAS,YAAY,CAAC,IAAI,OAAO,MAAM,SAAS,QAAQ,CAAC,MAAM,GAAG;YAC3E,KAAK;gBACH,OAAO,SAAS,YAAY,CAAC,MAAM,GAAG;YACxC,KAAK;gBACH,OAAO,SAAS,SAAS,CAAC,IAAI,OAAO;YACvC,KAAK;gBACH,OAAO,SAAS,UAAU,CAAC,IAAI,OAAO;YACxC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,kDAAkD;YAClD,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,4CAA4C;YAC5C,MAAM,uBAAuB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACvF,qBAAqB,IAAI,CAAC;gBACxB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAC3D,iBAAiB;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,eAAe;QACjB,qBACE,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAGvD,6LAAC,8HAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,6LAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;;;;;;;;;;;;IAKtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sEACA,QAAQ,cACJ,YACA,UAAU,cACR,gCACA;wCAER,SAAS;4CACP,8CAA8C;4CAC9C,IAAI,SAAS,aAAa;gDACxB,eAAe;4CACjB;wCACF;wCACA,UAAU;4CAAE,OAAO;wCAAK;;;;;;kDAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gFACA,UAAU,cACN,0BACA;kDAGL,KAAK,QAAQ;;;;;;;+BA7BX;;;;;;;;;;kCAkCX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,AAAC,GAA2C,OAAzC,AAAC,cAAc,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK,KAAI;4BAAG;4BACjE,YAAY;gCAAE,UAAU;4BAAI;;;;;;;;;;;kCAGhC,6LAAC;wBAAE,WAAU;;4BAAyE;4BAC9E,cAAc;4BAAE;4BAAK,MAAM,MAAM;4BAAC;4BAAG,KAAK,CAAC,YAAY,CAAC,IAAI;;;;;;;;;;;;;0BAItE,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;0CAAyD,KAAK,CAAC,YAAY,CAAC,IAAI;;;;;;0CACrG,6LAAC,4HAAA,CAAA,kBAAe;gCAAC,WAAU;;oCACxB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;;;;;;;;;;;;;kCAG1B,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,GAAG;wCAAK,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,MAAM;wCAAE,GAAG,CAAC;wCAAK,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;;wCAG3B,gBAAgB,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAAkC;;;;;;8EACvE,6LAAC,6HAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,OAAO,SAAS,SAAS;oEACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;oEAC3D,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;;;;;;;sEAGZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;8EAAkC;;;;;;8EACtE,6LAAC,6HAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,OAAO,SAAS,QAAQ;oEACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC1D,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;;;;;;;;;;;;;8DAId,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAkC;;;;;;sEACnE,6LAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAkC;;;;;;sEACnE,6LAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;wCAOf,gBAAgB,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAkC;;;;;;sEAC1E,6LAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC9D,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAkC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;oEAAkB,WAAU;;sFAC3B,6LAAC,gIAAA,CAAA,WAAQ;4EACP,IAAI;4EACJ,SAAS,SAAS,QAAQ,CAAC,QAAQ,CAAC;4EACpC,iBAAiB,IAAM,kBAAkB,YAAY;;;;;;sFAEvD,6LAAC,6HAAA,CAAA,QAAK;4EAAC,SAAS;4EAAS,WAAU;sFAA0D;;;;;;;mEANrF;;;;;;;;;;;;;;;;8DAWhB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,0IAAA,CAAA,iBAAc;wDACb,OAAM;wDACN,SAAS;wDACT,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,QAAU,eAAe,cAAc;wDAClD,aAAY;wDACZ,QAAQ;;;;;;;;;;;;;;;;;wCAOf,gBAAgB,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAkC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAoB;gEAAc;gEAAe;gEAAa;gEAAe;gEAAY;gEAAmB;gEAAU;gEAAW;6DAAS,CAAC,GAAG,CAAC,CAAC,qBAChJ,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC,gIAAA,CAAA,WAAQ;4EACP,IAAI;4EACJ,SAAS,SAAS,YAAY,CAAC,QAAQ,CAAC;4EACxC,iBAAiB,IAAM,kBAAkB,gBAAgB;;;;;;sFAE3D,6LAAC,6HAAA,CAAA,QAAK;4EAAC,SAAS;4EAAM,WAAU;sFAA0D;;;;;;;mEANlF;;;;;;;;;;;;;;;;8DAWhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAkC;;;;;;sEAC1E,6LAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC9D,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;wCAOjB,gBAAgB,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,SAAS,SAAS,SAAS;4DAC3B,iBAAiB,CAAC,UAAY,eAAe,aAAa;;;;;;sEAE5D,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAkC;;;;;;;;;;;;8DAEzE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAkC;;;;;;sEACrE,6LAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;4DACzD,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAkC;;;;;;sEACvE,6LAAC,6HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC3D,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAkC;;;;;;sEACnE,6LAAC,gIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;;;;;;;wCAOb,gBAAgB,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAa,WAAU;sEAAkC;;;;;;sEACxE,6LAAC,gIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,UAAU;4DAC1B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC5D,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;8DAGV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAa,WAAU;sEAAkC;;;;;;sEACxE,6LAAC,gIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,UAAU;4DAC1B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC5D,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;;;;;;;wCAOb,gBAAgB,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAc;gEAAE,SAAS,SAAS;gEAAC;gEAAE,SAAS,QAAQ;;;;;;;sEACjE,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAe;gEAAE,SAAS,KAAK;;;;;;;sEAC1C,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAe;gEAAE,SAAS,KAAK;;;;;;;sEAC1C,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAkB;gEAAE,SAAS,YAAY,IAAI;;;;;;;sEACxD,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAkB;gEAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,SAAS;;;;;;;sEAC/D,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAoB;gEAAE,SAAS,UAAU,IAAI;;;;;;;sEACxD,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAuB;gEAAE,SAAS,YAAY,CAAC,IAAI,CAAC,SAAS;;;;;;;sEACxE,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAAmB;gEAAE,SAAS,SAAS,GAAG,QAAQ;;;;;;;;;;;;;8DAE/D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;mCA5N3D;;;;;;;;;;0CAsOT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,uIAAA,CAAA,UAAW;4CACV,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,gBAAgB;4CAC1B,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;kDAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAEvB,gBAAgB,MAAM,MAAM,GAAG,kBAC9B,6LAAC,yIAAA,CAAA,gBAAa;4CACZ,MAAK;4CACL,MAAK;4CACL,YAAW;4CACX,cAAa;4CACb,WAAU;4CACV,SAAS;4CACT,UAAU;sDAET,eAAe,kBAAkB;;;;;iEAGpC,6LAAC,uIAAA,CAAA,UAAW;4CACV,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxdgB;KAAA", "debugId": null}}]}