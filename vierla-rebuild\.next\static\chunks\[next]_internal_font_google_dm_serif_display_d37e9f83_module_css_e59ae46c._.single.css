/* [next]/internal/font/google/dm_serif_display_d37e9f83.module.css [app-client] (css) */
@font-face {
  font-family: DM Serif Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/ac00c4f8aaa19a6f-s.56cd4ce7.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: DM Serif Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/90944b9a202e8502-s.p.b23d43ef.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: DM Serif Display Fallback;
  src: local(Times New Roman);
  ascent-override: 94.37%;
  descent-override: 30.51%;
  line-gap-override: 0.0%;
  size-adjust: 109.78%;
}

.dm_serif_display_d37e9f83-module__dQpVfW__className {
  font-family: DM Serif Display, DM Serif Display Fallback;
  font-style: normal;
  font-weight: 400;
}

.dm_serif_display_d37e9f83-module__dQpVfW__variable {
  --font-dm-serif-display: "DM Serif Display", "DM Serif Display Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_dm_serif_display_d37e9f83_module_css_e59ae46c._.single.css.map*/