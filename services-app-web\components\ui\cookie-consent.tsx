"use client"
import { useState, useEffect } from 'react'
import Link from 'next/link'

interface CookiePreferences {
  necessary: boolean
  analytics: boolean
  marketing: boolean
}

export function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false)
  const [showPreferences, setShowPreferences] = useState(false)
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always required
    analytics: false,
    marketing: false
  })

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('vierla-cookie-consent')
    if (!consent) {
      setShowBanner(true)
    } else {
      const savedPreferences = JSON.parse(consent)
      setPreferences(savedPreferences)
      applyCookieSettings(savedPreferences)
    }
  }, [])

  const applyCookieSettings = (prefs: CookiePreferences) => {
    // Apply analytics cookies
    if (prefs.analytics) {
      // Enable Google Analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'analytics_storage': 'granted'
        })
      }
    } else {
      // Disable analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'analytics_storage': 'denied'
        })
      }
    }

    // Apply marketing cookies
    if (prefs.marketing) {
      // Enable marketing cookies (Facebook Pixel, etc.)
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'ad_storage': 'granted',
          'ad_user_data': 'granted',
          'ad_personalization': 'granted'
        })
      }
    } else {
      // Disable marketing cookies
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          'ad_storage': 'denied',
          'ad_user_data': 'denied',
          'ad_personalization': 'denied'
        })
      }
    }
  }

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true
    }
    setPreferences(allAccepted)
    localStorage.setItem('vierla-cookie-consent', JSON.stringify(allAccepted))
    applyCookieSettings(allAccepted)
    setShowBanner(false)
  }

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      analytics: false,
      marketing: false
    }
    setPreferences(onlyNecessary)
    localStorage.setItem('vierla-cookie-consent', JSON.stringify(onlyNecessary))
    applyCookieSettings(onlyNecessary)
    setShowBanner(false)
  }

  const handleSavePreferences = () => {
    localStorage.setItem('vierla-cookie-consent', JSON.stringify(preferences))
    applyCookieSettings(preferences)
    setShowBanner(false)
    setShowPreferences(false)
  }

  const handlePreferenceChange = (type: keyof CookiePreferences) => {
    if (type === 'necessary') return // Can't disable necessary cookies
    
    setPreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  if (!showBanner) return null

  return (
    <>
      {/* Cookie Banner */}
      <div
        className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-black/80 backdrop-blur-md border-t border-white/20 shadow-2xl"
        role="dialog"
        aria-labelledby="cookie-banner-title"
        aria-describedby="cookie-banner-description"
        style={{
          background: 'linear-gradient(135deg, rgba(0,0,0,0.9), rgba(54,64,53,0.9))',
          boxShadow: '0 -10px 25px rgba(0,0,0,0.5), inset 0 1px 0 rgba(255,255,255,0.1)'
        }}
      >
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <h3
                id="cookie-banner-title"
                className="text-lg font-semibold text-white mb-2 drop-shadow-lg"
              >
                We use cookies to enhance your experience
              </h3>
              <p
                id="cookie-banner-description"
                className="text-white/90 text-sm leading-relaxed drop-shadow-md"
              >
                We use cookies and similar technologies to provide, protect, and improve our services.
                By clicking &quot;Accept All&quot;, you consent to our use of cookies for analytics and marketing.
                You can manage your preferences or learn more in our{' '}
                <Link
                  href="/privacy"
                  className="text-blue-300 hover:text-blue-100 underline focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-1 focus:ring-offset-transparent rounded drop-shadow-sm"
                  aria-label="Read our privacy policy"
                >
                  Privacy Policy
                </Link>.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 lg:ml-6">
              <button
                onClick={() => setShowPreferences(true)}
                className="px-4 py-2 text-sm font-medium text-white bg-white/20 hover:bg-white/30 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent backdrop-blur-sm border border-white/30 drop-shadow-lg"
                aria-label="Customize cookie preferences"
              >
                Customize
              </button>
              <button
                onClick={handleRejectAll}
                className="px-4 py-2 text-sm font-medium text-white bg-white/20 hover:bg-white/30 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent backdrop-blur-sm border border-white/30 drop-shadow-lg"
                aria-label="Reject all optional cookies"
              >
                Reject All
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-6 py-2 text-sm font-medium text-black bg-white hover:bg-white/90 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/70 focus:ring-offset-2 focus:ring-offset-transparent drop-shadow-lg font-semibold"
                aria-label="Accept all cookies"
              >
                Accept All
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Cookie Preferences Modal */}
      {showPreferences && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-md"
          role="dialog"
          aria-labelledby="preferences-modal-title"
          aria-modal="true"
        >
          <div
            className="rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-white/20"
            style={{
              background: 'linear-gradient(135deg, rgba(0,0,0,0.95), rgba(54,64,53,0.95))',
              boxShadow: '0 25px 50px rgba(0,0,0,0.7), inset 0 1px 0 rgba(255,255,255,0.1)'
            }}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2
                  id="preferences-modal-title"
                  className="text-2xl font-bold text-white drop-shadow-lg"
                >
                  Cookie Preferences
                </h2>
                <button
                  onClick={() => setShowPreferences(false)}
                  className="p-2 text-white/70 hover:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent backdrop-blur-sm bg-white/10 hover:bg-white/20 transition-colors"
                  aria-label="Close preferences modal"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                <p className="text-white/90 drop-shadow-sm">
                  Manage your cookie preferences below. You can enable or disable different types of cookies
                  except for necessary cookies which are required for the website to function properly.
                </p>

                {/* Necessary Cookies */}
                <div className="border border-white/30 rounded-lg p-4 bg-white/10 backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-white drop-shadow-sm">Necessary Cookies</h3>
                    <div className="flex items-center">
                      <span className="text-sm text-white/70 mr-2 drop-shadow-sm">Always Active</span>
                      <div className="w-12 h-6 bg-green-500 rounded-full relative shadow-lg">
                        <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 shadow-sm"></div>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-white/80 drop-shadow-sm">
                    These cookies are essential for the website to function and cannot be disabled.
                    They include authentication, security, and basic functionality.
                  </p>
                </div>

                {/* Analytics Cookies */}
                <div className="border border-white/30 rounded-lg p-4 bg-white/10 backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-white drop-shadow-sm">Analytics Cookies</h3>
                    <button
                      onClick={() => handlePreferenceChange('analytics')}
                      className={`w-12 h-6 rounded-full relative transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent shadow-lg ${
                        preferences.analytics ? 'bg-blue-500' : 'bg-white/30'
                      }`}
                      aria-label={`${preferences.analytics ? 'Disable' : 'Enable'} analytics cookies`}
                      role="switch"
                      aria-checked={preferences.analytics}
                    >
                      <div className={`w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform shadow-sm ${
                        preferences.analytics ? 'translate-x-6' : 'translate-x-0.5'
                      }`}></div>
                    </button>
                  </div>
                  <p className="text-sm text-white/80 drop-shadow-sm">
                    These cookies help us understand how visitors interact with our website by collecting
                    and reporting information anonymously.
                  </p>
                </div>

                {/* Marketing Cookies */}
                <div className="border border-white/30 rounded-lg p-4 bg-white/10 backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-white drop-shadow-sm">Marketing Cookies</h3>
                    <button
                      onClick={() => handlePreferenceChange('marketing')}
                      className={`w-12 h-6 rounded-full relative transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent shadow-lg ${
                        preferences.marketing ? 'bg-blue-500' : 'bg-white/30'
                      }`}
                      aria-label={`${preferences.marketing ? 'Disable' : 'Enable'} marketing cookies`}
                      role="switch"
                      aria-checked={preferences.marketing}
                    >
                      <div className={`w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform shadow-sm ${
                        preferences.marketing ? 'translate-x-6' : 'translate-x-0.5'
                      }`}></div>
                    </button>
                  </div>
                  <p className="text-sm text-white/80 drop-shadow-sm">
                    These cookies are used to deliver personalized advertisements and measure the effectiveness
                    of advertising campaigns.
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 mt-8">
                <button
                  onClick={handleRejectAll}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-white/20 hover:bg-white/30 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent backdrop-blur-sm border border-white/30 drop-shadow-lg"
                >
                  Reject All
                </button>
                <button
                  onClick={handleSavePreferences}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent drop-shadow-lg"
                >
                  Save Preferences
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="flex-1 px-4 py-2 text-sm font-medium text-black bg-white hover:bg-white/90 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-white/70 focus:ring-offset-2 focus:ring-offset-transparent drop-shadow-lg font-semibold"
                >
                  Accept All
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
