import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import { ShimmerButton } from "@/components/ui/shimmer-button";
import { LayoutTemplate, FileText, Users, BarChart2, Calendar, CreditCard, Globe, Palette, Zap, Shield } from "lucide-react";
import Link from "next/link";

export default function ProviderApp() {
  return (
    <div className="page-provider min-h-screen relative overflow-hidden">
      <AuroraBackgroundLayer />
      
      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-notable">
            FOR BEAUTY PROFESSIONALS - EVERYTHING YOU NEED TO RUN YOUR BUSINESS
          </h1>
          <p className="text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-sans">
            Join our curated network of top-tier beauty professionals. Build your business with powerful tools designed for your success.
          </p>
          
          <Link href="/apply" className="mb-8 inline-block">
            <ShimmerButton
              size="lg"
              background="var(--master-action-primary)"
              shimmerColor="var(--master-brand-accent)"
              className="px-8 py-4 text-lg font-medium text-mantle-50"
            >
              <span className="flex items-center">
                Apply to Join Vierla
                <FileText className="ml-2 w-5 h-5" />
              </span>
            </ShimmerButton>
          </Link>
        </div>
      </section>

      {/* Business Tools Section */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16">
            {[
              {
                icon: Globe,
                title: "Digital Store & Website Builder",
                description: "Create stunning, professional websites and branded online presence with our AI-powered builder.",
                features: ["• Drag & drop interface", "• AI content generation", "• Portfolio gallery", "• Custom domains"]
              },
              {
                icon: FileText,
                title: "Smart Invoicing",
                description: "Streamline your billing process with intelligent invoicing features.",
                features: ["• Automated reminders", "• Multiple currencies", "• Payment tracking", "• Professional templates"]
              },
              {
                icon: Users,
                title: "Integrated CRM",
                description: "Manage customer relationships and grow your business effectively.",
                features: ["• Customer database", "• Sales pipeline", "• Task automation", "• Journey tracking"]
              },
              {
                icon: BarChart2,
                title: "Business Analytics",
                description: "Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends.",
                features: ["• Revenue tracking", "• Customer insights", "• Booking analytics", "• Growth metrics"]
              }
            ].map((tool, index) => (
              <div key={index}>
                <GoldenGlowingCardContainer>
                  <Card className="bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl p-6 h-full">
                    <CardHeader className="text-left">
                      <div className="w-16 h-16 mb-4 rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30">
                        <tool.icon className="w-8 h-8 text-sage drop-shadow-lg" />
                      </div>
                      <CardTitle className="text-xl font-bold text-light-off-white drop-shadow-lg text-left font-tai-heritage">{tool.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="text-left">
                      <p className="text-warm-beige leading-relaxed text-sm mb-4 drop-shadow-sm font-sans">
                        {tool.description}
                      </p>
                      <div className="space-y-2">
                        {tool.features.map((feature, idx) => (
                          <div key={idx} className="text-warm-beige/70 text-xs flex items-center justify-start drop-shadow-sm font-sans">
                            <span className="w-1.5 h-1.5 rounded-full mr-2 bg-sage drop-shadow-sm"></span>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </GoldenGlowingCardContainer>
              </div>
            ))}
          </div>



          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                icon: Calendar,
                title: "Booking Management",
                description: "Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling.",
                features: ["• Calendar sync", "• Auto reminders", "• Easy rescheduling", "• Availability control"]
              },
              {
                icon: CreditCard,
                title: "Payment Processing",
                description: "Secure payment handling with multiple payment methods and direct deposits to your preferred account.",
                features: ["• Multiple payment methods", "• Secure transactions", "• Direct deposits", "• Transaction history"]
              }
            ].map((feature, index) => (
              <div key={index}>
                <GoldenGlowingCardContainer>
                  <Card className="bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl overflow-hidden h-full flex flex-col">
                    <CardContent className="p-8 flex flex-col h-full">
                      <div className="text-left flex-grow flex flex-col">
                        <div className="w-16 h-16 mb-4 rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30">
                          <feature.icon className="w-8 h-8 text-sage drop-shadow-lg" />
                        </div>
                        <h3 className="text-2xl font-bold text-light-off-white mb-4 drop-shadow-lg font-tai-heritage">
                          {feature.title}
                        </h3>
                        <p className="text-warm-beige leading-relaxed mb-6 flex-grow drop-shadow-sm font-sans">
                          {feature.description}
                        </p>
                        <div className="space-y-2 mt-auto">
                          {feature.features.map((item, idx) => (
                            <div key={idx} className="text-warm-beige/70 text-sm drop-shadow-sm font-sans">
                              {item}
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </GoldenGlowingCardContainer>
              </div>
            ))}
          </div>

          {/* Coming Soon Section */}
          <div className="text-center mt-16">
            <Card className="bg-light-charcoal backdrop-blur-md rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto border border-sage/30">
              <CardContent>
                <h3 className="text-4xl font-black text-light-off-white mb-6 drop-shadow-lg font-notable">COMING SOON</h3>
                <p className="text-xl text-warm-beige mb-8 max-w-2xl mx-auto drop-shadow-sm font-sans">
                  We're building something special for beauty professionals. Join our waitlist to be the first to know when we launch.
                </p>
                
                {/* App Store Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                  <div className="flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75">
                    <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="white">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    <div className="text-left">
                      <div className="text-xs text-gray-300">Coming Soon to</div>
                      <div className="text-lg font-semibold text-light-off-white font-sans">Apple App Store</div>
                    </div>
                  </div>

                  <div className="flex items-center px-6 py-3 bg-black/50 rounded-xl border border-white/20 cursor-not-allowed opacity-75">
                    <svg className="w-8 h-8 mr-3" viewBox="0 0 24 24" fill="white">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                    </svg>
                    <div className="text-left">
                      <div className="text-xs text-gray-300">Coming Soon to</div>
                      <div className="text-lg font-semibold text-light-off-white font-sans">Google Play Store</div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center mt-8">
                  <Link href="/apply">
                    <ShimmerButton
                      size="lg"
                      background="#B8956A"
                      shimmerColor="#E5D4A1"
                      className="px-8 py-4 text-lg font-medium text-[#2D2A26]"
                    >
                      <span className="flex items-center">
                        Apply to Join Vierla
                        <FileText className="ml-2 w-5 h-5" />
                      </span>
                    </ShimmerButton>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
