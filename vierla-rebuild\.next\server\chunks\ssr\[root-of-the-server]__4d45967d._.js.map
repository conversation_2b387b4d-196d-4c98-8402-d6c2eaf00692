{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/about/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Users, Target, Lightbulb, Heart } from \"lucide-react\";\n\nexport default function About() {\n  return (\n    <div className=\"page-about min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-notable\">\n            WE'RE ON A MISSION TO EMPOWER ENTREPRENEURS\n          </h1>\n          <p className=\"text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-sans\">\n            Building the future of business operations, one entrepreneur at a time.\n          </p>\n        </div>\n      </section>\n\n      {/* Our Mission Section */}\n      <section className=\"relative z-10 py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"grid gap-8 lg:grid-cols-2 items-center\">\n              <div className=\"space-y-4\">\n                <GoldenGlowingCardContainer>\n                  <Card className=\"bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl p-8\">\n                    <div className=\"flex items-center space-x-2 mb-6\">\n                      <Target className=\"h-8 w-8 text-sage drop-shadow-lg\" />\n                      <h2 className=\"text-3xl font-bold text-light-off-white drop-shadow-lg font-tai-heritage\">Our Mission</h2>\n                    </div>\n                    <p className=\"text-lg text-warm-beige leading-relaxed drop-shadow-sm mb-4 font-sans\">\n                      We started Vierla because we saw too many small business owners and freelancers struggling to manage a dozen different software subscriptions. It's costly, complex, and time-consuming. Our mission is to consolidate all the essential tools into a single, intelligent, and affordable platform, giving entrepreneurs their time back so they can focus on what they do best: growing their business.\n                    </p>\n                    <p className=\"text-lg text-warm-beige leading-relaxed drop-shadow-sm font-sans\">\n                      Every feature we build is designed with the modern entrepreneur in mind - from the solo freelancer just starting out to the growing agency scaling their operations. We believe that powerful business tools shouldn't require a technical degree or a massive budget to use effectively.\n                    </p>\n                  </Card>\n                </GoldenGlowingCardContainer>\n              </div>\n              <div className=\"flex items-center justify-center\">\n                <div className=\"w-20 h-20 mx-auto rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30\">\n                  <Heart className=\"w-10 h-10 text-sage drop-shadow-lg\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Our Vision Section */}\n      <section className=\"container space-y-6 py-8 md:py-12 lg:py-24\">\n        <div className=\"mx-auto max-w-4xl\">\n          <div className=\"grid gap-8 lg:grid-cols-2 items-center\">\n            <div className=\"flex items-center justify-center lg:order-1\">\n              <div className=\"w-20 h-20 mx-auto rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30\">\n                <Lightbulb className=\"w-10 h-10 text-sage drop-shadow-lg\" />\n              </div>\n            </div>\n            <div className=\"space-y-4 lg:order-2\">\n              <GoldenGlowingCardContainer>\n                <Card className=\"bg-light-charcoal backdrop-blur-md border border-sage/30 shadow-xl rounded-2xl p-8\">\n                  <div className=\"flex items-center space-x-2 mb-6\">\n                    <Lightbulb className=\"h-8 w-8 text-sage drop-shadow-lg\" />\n                    <h2 className=\"text-3xl font-bold text-light-off-white drop-shadow-lg font-tai-heritage\">Our Vision</h2>\n                  </div>\n                  <p className=\"text-lg text-warm-beige leading-relaxed drop-shadow-sm mb-4 font-sans\">\n                    We envision a future where starting and running a business is radically simpler. By leveraging the power of AI, we aim to automate administrative busywork and provide powerful insights that were once only available to large corporations. We're building the operating system for the next generation of business.\n                  </p>\n                  <p className=\"text-lg text-warm-beige leading-relaxed drop-shadow-sm font-sans\">\n                    Our vision extends beyond just software - we're creating an ecosystem where entrepreneurs can thrive, connect, and grow together. We believe that when we remove the barriers to business success, we unlock human potential and drive innovation across every industry.\n                  </p>\n                </Card>\n              </GoldenGlowingCardContainer>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Meet the Team Section */}\n      <section className=\"container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto\">\n        <div className=\"mx-auto max-w-6xl\">\n          <div className=\"text-center mb-12\">\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\n              <Users className=\"h-8 w-8 text-sage drop-shadow-lg\" />\n              <h2 className=\"text-3xl font-bold text-light-off-white drop-shadow-lg font-tai-heritage\">Meet the Team</h2>\n            </div>\n            <div className=\"w-full h-px bg-gradient-to-r from-transparent via-sage/20 to-transparent mb-6\"></div>\n            <p className=\"text-lg text-warm-beige max-w-2xl mx-auto drop-shadow-sm font-sans\">\n              We're a passionate group of entrepreneurs, developers, and designers united by our mission to simplify business operations.\n            </p>\n          </div>\n\n          <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3 justify-center\">\n            {[1, 2, 3, 4, 5, 6].map((member) => (\n              <GoldenGlowingCardContainer key={member}>\n                <Card className=\"text-center bg-light-charcoal backdrop-blur-md border border-sage/20 shadow-xl rounded-2xl\">\n                  <CardHeader className=\"text-center\">\n                    <div className=\"w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-4 bg-sage/20 border-2 border-sage/30\">\n                      <Users className=\"h-12 w-12 text-sage drop-shadow-lg\" />\n                    </div>\n                    <CardTitle className=\"text-light-off-white drop-shadow-lg text-center font-tai-heritage\">Team Member {member}</CardTitle>\n                    <CardDescription className=\"text-sage drop-shadow-sm text-center font-sans\">Position Title</CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"text-center\">\n                    <p className=\"text-sm text-warm-beige drop-shadow-sm text-center font-sans\">\n                      Brief bio and background information will be added here.\n                    </p>\n                  </CardContent>\n                </Card>\n              </GoldenGlowingCardContainer>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto\">\n        <div className=\"mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center\">\n          <div className=\"w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-6\"></div>\n          <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-light-off-white drop-shadow-lg text-center font-tai-heritage\">\n            Ready to join our mission?\n          </h2>\n          <p className=\"max-w-[42rem] leading-normal text-warm-beige sm:text-xl sm:leading-8 drop-shadow-sm text-center font-sans\">\n            Be part of the future of business operations. Start your journey with Vierla today.\n          </p>\n          <div className=\"flex justify-center\">\n            <ShinyButton size=\"lg\" className=\"px-8 py-4 text-lg\">\n              <a href=\"/apply\">Get Started</a>\n            </ShinyButton>\n          </div>\n          <div className=\"w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mt-6\"></div>\n        </div>\n      </section>\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqG;;;;;;sCAGnH,8OAAC;4BAAE,WAAU;sCAAsG;;;;;;;;;;;;;;;;;0BAOvH,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;kDACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAG,WAAU;sEAA2E;;;;;;;;;;;;8DAE3F,8OAAC;oDAAE,WAAU;8DAAwE;;;;;;8DAGrF,8OAAC;oDAAE,WAAU;8DAAmE;;;;;;;;;;;;;;;;;;;;;;8CAMtF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAG,WAAU;kEAA2E;;;;;;;;;;;;0DAE3F,8OAAC;gDAAE,WAAU;0DAAwE;;;;;;0DAGrF,8OAAC;gDAAE,WAAU;0DAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAA2E;;;;;;;;;;;;8CAE3F,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAqE;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,uBACvB,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,yHAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC,yHAAA,CAAA,YAAS;wDAAC,WAAU;;4DAAoE;4DAAa;;;;;;;kEACtG,8OAAC,yHAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAiD;;;;;;;;;;;;0DAE9E,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAE,WAAU;8DAA+D;;;;;;;;;;;;;;;;;mCAVjD;;;;;;;;;;;;;;;;;;;;;0BAsBzC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAG,WAAU;sCAAgI;;;;;;sCAG9I,8OAAC;4BAAE,WAAU;sCAA4G;;;;;;sCAGzH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,UAAW;gCAAC,MAAK;gCAAK,WAAU;0CAC/B,cAAA,8OAAC;oCAAE,MAAK;8CAAS;;;;;;;;;;;;;;;;sCAGrB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}]}