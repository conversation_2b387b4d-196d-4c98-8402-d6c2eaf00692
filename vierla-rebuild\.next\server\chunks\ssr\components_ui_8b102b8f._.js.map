{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// A full-page wrapper component for the aurora background\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <main\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center bg-[var(--aurora-bg)] dark:bg-[var(--aurora-bg-dark)] text-slate-950 transition-bg\",\n        className\n      )}\n      {...props}\n    >\n      <AuroraBackgroundLayer showRadialGradient={showRadialGradient} />\n      {children}\n    </main>\n  );\n};\n\n// A memoized background-only layer for embedding in existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div\n        className={cn(\n          \"fixed inset-0 -z-10 overflow-hidden\",\n          className\n        )}\n      >\n        <div\n          className={cn(\n            `aurora-background\n            [--white-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-light)_0%,var(--aurora-stripe-light)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-light)_16%)]\n            [--dark-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-dark)_0%,var(--aurora-stripe-dark)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-dark)_16%)]\n            [--aurora:repeating-linear-gradient(100deg,var(--aurora-flow-1)_10%,var(--aurora-flow-2)_15%,var(--aurora-flow-3)_20%,var(--aurora-flow-4)_25%,var(--aurora-flow-5)_30%)]\n            [background-image:var(--white-gradient),var(--aurora)]\n            dark:[background-image:var(--dark-gradient),var(--aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--white-gradient),var(--aurora)]\n            after:dark:[background-image:var(--dark-gradient),var(--aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform\n            transform-gpu`,\n            showRadialGradient &&\n              `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB,CAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mJACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAsB,oBAAoB;;;;;;YAC1C;;;;;;;AAGP;AAGO,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACtC,CAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uCACA;kBAGF,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;;yBAeY,CAAC,EACd,sBACE,CAAC,0EAA0E,CAAC;;;;;;;;;;;AAKxF;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/glowing-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useCallback, useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { animate } from \"motion/react\";\n\ninterface GlowingEffectProps {\n  blur?: number;\n  inactiveZone?: number;\n  proximity?: number;\n  spread?: number;\n  variant?: \"default\" | \"white\" | \"sage\";\n  glow?: boolean;\n  className?: string;\n  disabled?: boolean;\n  movementDuration?: number;\n  borderWidth?: number;\n}\n\nconst GlowingEffect = memo(\n  ({\n    blur = 0,\n    inactiveZone = 0.7,\n    proximity = 0,\n    spread = 20,\n    variant = \"sage\",\n    glow = false,\n    className,\n    movementDuration = 2,\n    borderWidth = 1,\n    disabled = true,\n  }: GlowingEffectProps) => {\n    const containerRef = useRef<HTMLDivElement>(null);\n    const lastPosition = useRef({ x: 0, y: 0 });\n    const animationFrameRef = useRef<number>(0);\n\n    const handleMove = useCallback(\n      (e?: MouseEvent | { x: number; y: number }) => {\n        if (!containerRef.current) return;\n\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n\n        animationFrameRef.current = requestAnimationFrame(() => {\n          const element = containerRef.current;\n          if (!element) return;\n\n          const { left, top, width, height } = element.getBoundingClientRect();\n          const mouseX = e?.x ?? lastPosition.current.x;\n          const mouseY = e?.y ?? lastPosition.current.y;\n\n          if (e) {\n            lastPosition.current = { x: mouseX, y: mouseY };\n          }\n\n          const center = [left + width * 0.5, top + height * 0.5];\n          const distanceFromCenter = Math.hypot(\n            mouseX - center[0],\n            mouseY - center[1]\n          );\n          const inactiveRadius = 0.5 * Math.min(width, height) * inactiveZone;\n\n          if (distanceFromCenter < inactiveRadius) {\n            element.style.setProperty(\"--active\", \"0\");\n            return;\n          }\n\n          const isActive =\n            mouseX > left - proximity &&\n            mouseX < left + width + proximity &&\n            mouseY > top - proximity &&\n            mouseY < top + height + proximity;\n\n          element.style.setProperty(\"--active\", isActive ? \"1\" : \"0\");\n\n          if (!isActive) return;\n\n          const currentAngle =\n            parseFloat(element.style.getPropertyValue(\"--start\")) || 0;\n          let targetAngle =\n            (180 * Math.atan2(mouseY - center[1], mouseX - center[0])) /\n              Math.PI +\n            90;\n\n          const angleDiff = ((targetAngle - currentAngle + 180) % 360) - 180;\n          const newAngle = currentAngle + angleDiff;\n\n          animate(currentAngle, newAngle, {\n            duration: movementDuration,\n            ease: [0.16, 1, 0.3, 1],\n            onUpdate: (value) => {\n              element.style.setProperty(\"--start\", String(value));\n            },\n          });\n        });\n      },\n      [inactiveZone, proximity, movementDuration]\n    );\n\n    useEffect(() => {\n      if (disabled) return;\n\n      const handleScroll = () => handleMove();\n      const handlePointerMove = (e: PointerEvent) => handleMove(e);\n\n      window.addEventListener(\"scroll\", handleScroll, { passive: true });\n      document.body.addEventListener(\"pointermove\", handlePointerMove, {\n        passive: true,\n      });\n\n      return () => {\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n        window.removeEventListener(\"scroll\", handleScroll);\n        document.body.removeEventListener(\"pointermove\", handlePointerMove);\n      };\n    }, [handleMove, disabled]);\n\n    // Define gradient variants suitable for green background\n    const getGradient = () => {\n      switch (variant) {\n        case \"white\":\n          return `repeating-conic-gradient(\n            from 236.84deg at 50% 50%,\n            var(--black),\n            var(--black) calc(25% / var(--repeating-conic-gradient-times))\n          )`;\n        case \"sage\":\n          return `radial-gradient(circle, #CA8A04 10%, #CA8A0400 20%),\n            radial-gradient(circle at 40% 40%, #E1E6E1 5%, #E1E6E100 15%),\n            radial-gradient(circle at 60% 60%, #CA8A04 10%, #CA8A0400 20%),\n            radial-gradient(circle at 40% 60%, #8B9A8C 10%, #8B9A8C00 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              #CA8A04 0%,\n              #E1E6E1 calc(25% / var(--repeating-conic-gradient-times)),\n              #CA8A04 calc(50% / var(--repeating-conic-gradient-times)),\n              #8B9A8C calc(75% / var(--repeating-conic-gradient-times)),\n              #CA8A04 calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n        default:\n          return `radial-gradient(circle, #dd7bbb 10%, #dd7bbb00 20%),\n            radial-gradient(circle at 40% 40%, #d79f1e 5%, #d79f1e00 15%),\n            radial-gradient(circle at 60% 60%, #5a922c 10%, #5a922c00 20%), \n            radial-gradient(circle at 40% 60%, #4c7894 10%, #4c789400 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              #dd7bbb 0%,\n              #d79f1e calc(25% / var(--repeating-conic-gradient-times)),\n              #5a922c calc(50% / var(--repeating-conic-gradient-times)), \n              #4c7894 calc(75% / var(--repeating-conic-gradient-times)),\n              #dd7bbb calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n      }\n    };\n\n    return (\n      <>\n        <div\n          className={cn(\n            \"pointer-events-none absolute -inset-px hidden rounded-[inherit] border opacity-0 transition-opacity\",\n            glow && \"opacity-100\",\n            variant === \"white\" && \"border-white\",\n            disabled && \"!block\"\n          )}\n        />\n        <div\n          ref={containerRef}\n          style={\n            {\n              \"--blur\": `${blur}px`,\n              \"--spread\": spread,\n              \"--start\": \"0\",\n              \"--active\": \"0\",\n              \"--glowingeffect-border-width\": `${borderWidth}px`,\n              \"--repeating-conic-gradient-times\": \"5\",\n              \"--gradient\": getGradient(),\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity\",\n            glow && \"opacity-100\",\n            blur > 0 && \"blur-[var(--blur)] \",\n            className,\n            disabled && \"!hidden\"\n          )}\n        >\n          <div\n            className={cn(\n              \"glow\",\n              \"rounded-[inherit]\",\n              'after:content-[\"\"] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',\n              \"after:[border:var(--glowingeffect-border-width)_solid_transparent]\",\n              \"after:[background:var(--gradient)] after:[background-attachment:fixed]\",\n              \"after:opacity-[var(--active)] after:transition-opacity after:duration-300\",\n              \"after:[mask-clip:padding-box,border-box]\",\n              \"after:[mask-composite:intersect]\",\n              \"after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]\"\n            )}\n          />\n        </div>\n      </>\n    );\n  }\n);\n\nGlowingEffect.displayName = \"GlowingEffect\";\n\nexport { GlowingEffect };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAmBA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACvB,CAAC,EACC,OAAO,CAAC,EACR,eAAe,GAAG,EAClB,YAAY,CAAC,EACb,SAAS,EAAE,EACX,UAAU,MAAM,EAChB,OAAO,KAAK,EACZ,SAAS,EACT,mBAAmB,CAAC,EACpB,cAAc,CAAC,EACf,WAAW,IAAI,EACI;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,kBAAkB,OAAO,EAAE;YAC7B,qBAAqB,kBAAkB,OAAO;QAChD;QAEA,kBAAkB,OAAO,GAAG,sBAAsB;YAChD,MAAM,UAAU,aAAa,OAAO;YACpC,IAAI,CAAC,SAAS;YAEd,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,qBAAqB;YAClE,MAAM,SAAS,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;YAE7C,IAAI,GAAG;gBACL,aAAa,OAAO,GAAG;oBAAE,GAAG;oBAAQ,GAAG;gBAAO;YAChD;YAEA,MAAM,SAAS;gBAAC,OAAO,QAAQ;gBAAK,MAAM,SAAS;aAAI;YACvD,MAAM,qBAAqB,KAAK,KAAK,CACnC,SAAS,MAAM,CAAC,EAAE,EAClB,SAAS,MAAM,CAAC,EAAE;YAEpB,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,OAAO,UAAU;YAEvD,IAAI,qBAAqB,gBAAgB;gBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY;gBACtC;YACF;YAEA,MAAM,WACJ,SAAS,OAAO,aAChB,SAAS,OAAO,QAAQ,aACxB,SAAS,MAAM,aACf,SAAS,MAAM,SAAS;YAE1B,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,MAAM;YAEvD,IAAI,CAAC,UAAU;YAEf,MAAM,eACJ,WAAW,QAAQ,KAAK,CAAC,gBAAgB,CAAC,eAAe;YAC3D,IAAI,cACF,AAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,EAAE,IACtD,KAAK,EAAE,GACT;YAEF,MAAM,YAAY,AAAC,CAAC,cAAc,eAAe,GAAG,IAAI,MAAO;YAC/D,MAAM,WAAW,eAAe;YAEhC,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU;gBAC9B,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAK;iBAAE;gBACvB,UAAU,CAAC;oBACT,QAAQ,KAAK,CAAC,WAAW,CAAC,WAAW,OAAO;gBAC9C;YACF;QACF;IACF,GACA;QAAC;QAAc;QAAW;KAAiB;IAG7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,eAAe,IAAM;QAC3B,MAAM,oBAAoB,CAAC,IAAoB,WAAW;QAE1D,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe,mBAAmB;YAC/D,SAAS;QACX;QAEA,OAAO;YACL,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YACA,OAAO,mBAAmB,CAAC,UAAU;YACrC,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;QACnD;IACF,GAAG;QAAC;QAAY;KAAS;IAEzB,yDAAyD;IACzD,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC;;;;WAIP,CAAC;YACJ,KAAK;gBACH,OAAO,CAAC;;;;;;;;;;;aAWL,CAAC;YACN;gBACE,OAAO,CAAC;;;;;;;;;;;aAWL,CAAC;QACR;IACF;IAEA,qBACE;;0BACE,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA,QAAQ,eACR,YAAY,WAAW,gBACvB,YAAY;;;;;;0BAGhB,8OAAC;gBACC,KAAK;gBACL,OACE;oBACE,UAAU,GAAG,KAAK,EAAE,CAAC;oBACrB,YAAY;oBACZ,WAAW;oBACX,YAAY;oBACZ,gCAAgC,GAAG,YAAY,EAAE,CAAC;oBAClD,oCAAoC;oBACpC,cAAc;gBAChB;gBAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yFACA,QAAQ,eACR,OAAO,KAAK,uBACZ,WACA,YAAY;0BAGd,cAAA,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,QACA,qBACA,sHACA,sEACA,0EACA,6EACA,4CACA,oCACA;;;;;;;;;;;;;AAMZ;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { GlowingEffect } from \"./glowing-effect\";\n\ninterface GoldenGlowingCardContainerProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = React.memo(({\n  children,\n  className,\n}) => {\n  return (\n    <div className={cn(\"relative h-full group\", className)}>\n      {/* Outer container with glowing effect */}\n      <div className=\"relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3\">\n        <GlowingEffect\n          spread={40}\n          glow={true}\n          disabled={false}\n          proximity={64}\n          inactiveZone={0.01}\n          borderWidth={3}\n          variant=\"sage\"\n        />\n\n        {/* Inner container with margin - Enhanced glassmorphism */}\n        <div className=\"relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] bg-neutral-charcoal-light/20 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-light/15 p-6 shadow-xl border-brand-sage/10\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n});\n\nexport default GoldenGlowingCardContainer;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,MAAM,2CAAwE,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAC/F,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBAE1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,gBAAa;oBACZ,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,cAAc;oBACd,aAAa;oBACb,SAAQ;;;;;;8BAIV,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        bg: backgroundColor || \"var(--master-button-primary-bg)\",\n        shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        text: textColor || \"var(--master-button-primary-text)\"\n      },\n      secondary: {\n        bg: backgroundColor || \"var(--master-button-secondary-bg)\",\n        shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        text: textColor || \"var(--master-button-secondary-text)\"\n      },\n      accent: {\n        bg: backgroundColor || \"var(--master-brand-accent)\",\n        shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        text: textColor || \"var(--master-text-on-dark)\"\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          color: colors.text,\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4CAA4C;IAC5C,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;YACA,WAAW;gBACT,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;YACA,QAAQ;gBACN,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO,OAAO,IAAI;oBAClB,WACE,CAAC,uBAAuB,EAAE,OAAO,OAAO,CAAC,uDAAuD,EAAE,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC7I;0BAEC;;;;;;0BAEH,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,CAAC,uBAAuB,EAAE,OAAO,OAAO,CAAC,sBAAsB,EAAE,OAAO,OAAO,CAAC,sBAAsB,EAAE,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC7J;gBACA,WAAU;;;;;;;;;;;;AAIlB;uCAEe", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/text-shimmer.tsx"], "sourcesContent": ["'use client';\nimport React, { useMemo, type JSX } from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface TextShimmerProps {\n  children: string;\n  as?: React.ElementType;\n  className?: string;\n  duration?: number;\n  spread?: number;\n}\n\nexport const TextShimmer = React.memo(function TextShimmer({\n  children,\n  as: Component = 'p',\n  className,\n  duration = 2,\n  spread = 2,\n}: TextShimmerProps) {\n  const MotionComponent = motion.create(Component as keyof JSX.IntrinsicElements);\n\n  const dynamicSpread = useMemo(() => {\n    return children.length * spread;\n  }, [children, spread]);\n\n  return (\n    <MotionComponent\n      className={cn(\n        'relative inline-block bg-[length:250%_100%,auto] bg-clip-text',\n        'text-transparent [--base-color:var(--global-text-shimmer-base)] [--base-gradient-color:var(--global-text-shimmer-highlight)]',\n        '[--bg:linear-gradient(90deg,#0000_calc(50%-var(--spread)),var(--base-gradient-color),#0000_calc(50%+var(--spread)))] [background-repeat:no-repeat,padding-box]',\n        'dark:[--base-color:var(--global-text-shimmer-base)] dark:[--base-gradient-color:var(--global-text-shimmer-highlight)]',\n        className\n      )}\n      initial={{ backgroundPosition: '100% center' }}\n      animate={{ backgroundPosition: '0% center' }}\n      transition={{\n        repeat: Infinity,\n        duration,\n        ease: 'linear',\n      }}\n      style={\n        {\n          '--spread': `${dynamicSpread}px`,\n          backgroundImage: `var(--bg), linear-gradient(var(--base-color), var(--base-color))`,\n        } as React.CSSProperties\n      }\n    >\n      {children}\n    </MotionComponent>\n  );\n});\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAaO,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,SAAS,YAAY,EACzD,QAAQ,EACR,IAAI,YAAY,GAAG,EACnB,SAAS,EACT,WAAW,CAAC,EACZ,SAAS,CAAC,EACO;IACjB,MAAM,kBAAkB,0LAAA,CAAA,SAAM,CAAC,MAAM,CAAC;IAEtC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,SAAS,MAAM,GAAG;IAC3B,GAAG;QAAC;QAAU;KAAO;IAErB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA,gIACA,kKACA,yHACA;QAEF,SAAS;YAAE,oBAAoB;QAAc;QAC7C,SAAS;YAAE,oBAAoB;QAAY;QAC3C,YAAY;YACV,QAAQ;YACR;YACA,MAAM;QACR;QACA,OACE;YACE,YAAY,GAAG,cAAc,EAAE,CAAC;YAChC,iBAAiB,CAAC,gEAAgE,CAAC;QACrF;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, Variants } from \"framer-motion\";\nimport { JSX } from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\ninterface WordPullUpProps {\n  words: string;\n  delayMultiple?: number;\n  wrapperFramerProps?: Variants;\n  framerProps?: Variants;\n  className?: string;\n  delay?: number;\n  as?: keyof JSX.IntrinsicElements;\n}\n\nfunction WordPullUp({\n  words,\n  wrapperFramerProps = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  },\n  framerProps = {\n    hidden: { y: 20, opacity: 0 },\n    show: { y: 0, opacity: 1 },\n  },\n  className,\n  delay = 0,\n  as = \"h1\",\n}: WordPullUpProps) {\n  const MotionComponent = motion.create(as as keyof JSX.IntrinsicElements);\n\n  return (\n    <MotionComponent\n      variants={wrapperFramerProps}\n      initial=\"hidden\"\n      animate=\"show\"\n      transition={{ delay }}\n      className={cn(\n        \"font-display text-center text-4xl font-bold leading-[5rem] tracking-[-0.02em] drop-shadow-sm text-white\",\n        className,\n      )}\n    >\n      {words.split(\" \").map((word, i) => (\n        <motion.span\n          key={i}\n          variants={framerProps}\n          style={{ display: \"inline-block\", paddingRight: \"8px\" }}\n        >\n          {word === \"\" ? <span>&nbsp;</span> : word}\n        </motion.span>\n      ))}\n    </MotionComponent>\n  );\n}\n\nexport { WordPullUp };\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AALA;;;;AAiBA,SAAS,WAAW,EAClB,KAAK,EACL,qBAAqB;IACnB,QAAQ;QAAE,SAAS;IAAE;IACrB,MAAM;QACJ,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF,CAAC,EACD,cAAc;IACZ,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,MAAM;QAAE,GAAG;QAAG,SAAS;IAAE;AAC3B,CAAC,EACD,SAAS,EACT,QAAQ,CAAC,EACT,KAAK,IAAI,EACO;IAChB,MAAM,kBAAkB,0LAAA,CAAA,SAAM,CAAC,MAAM,CAAC;IAEtC,qBACE,8OAAC;QACC,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAY;YAAE;QAAM;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2GACA;kBAGD,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,kBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,UAAU;gBACV,OAAO;oBAAE,SAAS;oBAAgB,cAAc;gBAAM;0BAErD,SAAS,mBAAK,8OAAC;8BAAK;;;;;2BAAgB;eAJhC;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef } from \"react\";\nimport {\n  motion,\n  useScroll,\n  useSpring,\n  useTransform,\n  useMotionValue,\n  useVelocity,\n  useAnimationFrame,\n} from \"framer-motion\";\nimport { wrap } from \"@motionone/utils\";\nimport { cn } from \"@/lib/utils\";\n\ntype MarqueeAnimationProps = {\n  children: string;\n  className?: string;\n  direction?: \"left\" | \"right\";\n  baseVelocity: number;\n};\n\nfunction MarqueeAnimation({\n  children,\n  className,\n  direction = \"left\",\n  baseVelocity = 10,\n}: MarqueeAnimationProps) {\n  const baseX = useMotionValue(0);\n  const { scrollY } = useScroll();\n  const scrollVelocity = useVelocity(scrollY);\n  const smoothVelocity = useSpring(scrollVelocity, {\n    damping: 50,\n    stiffness: 400,\n  });\n  const velocityFactor = useTransform(smoothVelocity, [0, 1000], [0, 0], {\n    clamp: false,\n  });\n\n  const x = useTransform(baseX, (v) => `${wrap(-20, -45, v)}%`);\n\n  const directionFactor = useRef<number>(1);\n  useAnimationFrame((t, delta) => {\n    let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n    if (direction == \"left\") {\n      directionFactor.current = 1;\n    } else if (direction == \"right\") {\n      directionFactor.current = -1;\n    }\n\n    moveBy += directionFactor.current * moveBy * velocityFactor.get();\n\n    baseX.set(baseX.get() + moveBy);\n  });\n\n  return (\n    <div className=\"overflow-hidden max-w-[100vw] text-nowrap flex-nowrap flex relative\">\n      <motion.div\n        className={cn(\n          \"font-bold uppercase text-5xl flex flex-nowrap text-nowrap *:block *:me-10 text-white drop-shadow-lg\",\n          className\n        )}\n        style={{ x }}\n      >\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n      </motion.div>\n    </div>\n  );\n}\n\nexport { MarqueeAnimation };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAbA;;;;;;AAsBA,SAAS,iBAAiB,EACxB,QAAQ,EACR,SAAS,EACT,YAAY,MAAM,EAClB,eAAe,EAAE,EACK;IACtB,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,iBAAiB,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM,iBAAiB,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QAC/C,SAAS;QACT,WAAW;IACb;IACA,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG;KAAE,EAAE;QACrE,OAAO;IACT;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,OAAO,CAAC,IAAM,GAAG,CAAA,GAAA,0JAAA,CAAA,OAAI,AAAD,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IAE5D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IACvC,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,GAAG;QACpB,IAAI,SAAS,gBAAgB,OAAO,GAAG,eAAe,CAAC,QAAQ,IAAI;QAEnE,IAAI,aAAa,QAAQ;YACvB,gBAAgB,OAAO,GAAG;QAC5B,OAAO,IAAI,aAAa,SAAS;YAC/B,gBAAgB,OAAO,GAAG,CAAC;QAC7B;QAEA,UAAU,gBAAgB,OAAO,GAAG,SAAS,eAAe,GAAG;QAE/D,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA;YAEF,OAAO;gBAAE;YAAE;;8BAEX,8OAAC;8BAAM;;;;;;8BACP,8OAAC;8BAAM;;;;;;8BACP,8OAAC;8BAAM;;;;;;8BACP,8OAAC;8BAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}