{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// A full-page wrapper component for the aurora background\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <main\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center bg-[var(--aurora-bg)] dark:bg-[var(--aurora-bg-dark)] text-slate-950 transition-bg\",\n        className\n      )}\n      {...props}\n    >\n      <AuroraBackgroundLayer showRadialGradient={showRadialGradient} />\n      {children}\n    </main>\n  );\n};\n\n// A memoized background-only layer for embedding in existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div\n        className={cn(\n          \"fixed inset-0 -z-10 overflow-hidden\",\n          className\n        )}\n      >\n        <div\n          className={cn(\n            `aurora-background\n            [--white-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-light)_0%,var(--aurora-stripe-light)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-light)_16%)]\n            [--dark-gradient:repeating-linear-gradient(100deg,var(--aurora-stripe-dark)_0%,var(--aurora-stripe-dark)_7%,transparent_10%,transparent_12%,var(--aurora-stripe-dark)_16%)]\n            [--aurora:repeating-linear-gradient(100deg,var(--aurora-flow-1)_10%,var(--aurora-flow-2)_15%,var(--aurora-flow-3)_20%,var(--aurora-flow-4)_25%,var(--aurora-flow-5)_30%)]\n            [background-image:var(--white-gradient),var(--aurora)]\n            dark:[background-image:var(--dark-gradient),var(--aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--white-gradient),var(--aurora)]\n            after:dark:[background-image:var(--dark-gradient),var(--aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform\n            transform-gpu`,\n            showRadialGradient &&\n              `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mJACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAsB,oBAAoB;;;;;;YAC1C;;;;;;;AAGP;KAlBa;AAqBN,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EACtC;QAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uCACA;kBAGF,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,iwCAgBD,sBACG;;;;;;;;;;;AAKb;MAvCW;AA0Cb,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        bg: backgroundColor || \"var(--master-button-primary-bg)\",\n        shimmer: shimmerColor || \"var(--master-brand-accent)\",\n        text: textColor || \"var(--master-button-primary-text)\"\n      },\n      secondary: {\n        bg: backgroundColor || \"var(--master-button-secondary-bg)\",\n        shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        text: textColor || \"var(--master-button-secondary-text)\"\n      },\n      accent: {\n        bg: backgroundColor || \"var(--master-brand-accent)\",\n        shimmer: shimmerColor || \"var(--master-brand-primary)\",\n        text: textColor || \"var(--master-text-on-dark)\"\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          color: colors.text,\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C;QAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4CAA4C;IAC5C,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;YACA,WAAW;gBACT,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;YACA,QAAQ;gBACN,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa;YACrB;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO,OAAO,IAAI;oBAClB,WACE,AAAC,0BAAiG,OAAxE,OAAO,OAAO,EAAC,2DAAwE,OAAf,OAAO,OAAO,EAAC;gBACrH;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,AAAC,0BAAgE,OAAvC,OAAO,OAAO,EAAC,0BAA+D,OAAvC,OAAO,OAAO,EAAC,0BAAuC,OAAf,OAAO,OAAO,EAAC;gBACrI;gBACA,WAAU;;;;;;;;;;;;AAIlB;KAnGa;uCAqGE", "debugId": null}}]}