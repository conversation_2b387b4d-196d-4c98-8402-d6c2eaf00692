# Services UI/UX

## Theme

### Color Symbolism: Biological, Cultural, and Personal

**Emotions to evoke**:  
Creativity, freshness, trust, cleanliness, simplicity

| Color   | Meaning & Usage                                                                 |
|--------|----------------------------------------------------------------------------------|
| **Blue**   | Trust, loyalty, dependability, security — used by banks & healthcare (e.g., Blue Cross). 57% of men and 35% of women rank it as their top choice. |
| **Orange** | Warmth, creativity, happiness, energy, friendliness — e.g., Nickelodeon.       |
| **Black**  | Sophistication, power, elegance — e.g., Nike, Chanel.                         |
| **White**  | Clean, simplistic, modern — e.g., Apple, Tesla.                               |

### Accessibility
- Use **high contrast colors** to improve readability.
- **Do not rely on color alone** to convey important information (e.g., use prompts for form errors).
- Consider color blindness:
  - **Protanopia & Deuteranopia**: affect red/green (common in ~8% men)
  - **Tritanopia**: affects blue/yellow (much rarer)
- Use tools like **Color Oracle** or **Stark plugin** to simulate color blindness.

### 60-30-10 Rule
- 60% Primary  
- 30% Secondary  
- 10% Accent

---

## Logo

Brand ideas:

- **Tendi** – derived from “tend” + “you”
- **Lumecrown** / **Lume**
- **Corvella**
- **Vierla** – from *Vera* (authentic) + *Bella* (beauty)

**Slogan**: *Beauty Simplified*

---

## Color Theme

### **Primary Colors**

| Name            | Hex       | Usage                           |
|-----------------|-----------|---------------------------------|
| Sage Green      | `#7c9a85` | Buttons, icons, primary actions |
| Darker Sage     | `#657d6d` | Hover states, text gradients    |

### **Secondary Colors**

| Name            | Hex       | Usage                              |
|-----------------|-----------|------------------------------------|
| Beige           | `#f0e6d9` | Subtle backgrounds, accents        |
| Light Sage      | `#b7c4b7` | Secondary elements                 |
| Navy            | `#344055` | Secondary buttons, select text     |

### **Neutral Colors**

| Name       | Hex       | Usage             |
|------------|-----------|--------------------|
| Off-White  | `#f5faf7` | Backgrounds        |
| Gray       | `#333333` | Primary text       |
| Charcoal   | `#2c3137` | Headings, emphasis |

---

## Gradients

| Type              | Value                                                                 |
|-------------------|------------------------------------------------------------------------|
| Glow Gradient     | `linear-gradient(135deg, rgba(124,154,133,0.8), rgba(101,125,109,0.6))` |
| Text Gradient     | `background-gradient-to-r from-[#2c3137] to-[#3e4e42]`                 |
| Page Background   | `linear-gradient(135deg, #f5faf7, #f3f0ed)`                            |

---

## Functional Colors

| Name                  | Hex         | Usage                                  |
|-----------------------|-------------|-----------------------------------------|
| Yellow (Star Rating)  | `#FACC15`   | Ratings                                |
| Light Sage BG         | `#f0f4f1`   | Availability badges, selected states   |
| Dropdown BG           | `#ffffff`   | Hamburger dropdown background          |
| Glass Card Fill       | `rgba(255,255,255,0.85)` | Transparent card backgrounds         |

---

## Design Token Reference Table

### 🌱 Brand Color Variables

| Variable Name               | Hex       | Usage Example                   |
|-----------------------------|-----------|----------------------------------|
| `--color-primary`           | `#7c9a85` | Buttons, links, accents         |
| `--color-primary-dark`      | `#657d6d` | Hover state                     |
| `--color-secondary-beige`   | `#f0e6d9` | Backgrounds                     |
| `--color-secondary-sage`    | `#b7c4b7` | Secondary backgrounds           |
| `--color-secondary-navy`    | `#344055` | Darker text, buttons            |

### ⚪ Neutral Colors

| Variable Name               | Hex       | Usage Example                   |
|-----------------------------|-----------|----------------------------------|
| `--color-neutral-white`     | `#f5faf7` | Background                      |
| `--color-neutral-gray`      | `#333333` | Body text                       |
| `--color-neutral-charcoal`  | `#2c3137` | Headings                        |
| Tailwind `gray-500`         | `#6b7280` | Subtext / descriptions          |
| Tailwind `gray-700`         | `#374151` | Ratings                         |
| Tailwind `gray-900`         | `#111827` | Primary headings                |

---

# Palette 1
50
f6f7f6
100
e1e6e1
200
c3ccc3
300
8b9a8c
400
79887a
500
5f6d61
600
4a574b
700
3e473f
800
343b36
900
2e332f
950
181b19

## Fonts Used

| Font Family | Usage         |
|-------------|---------------|
| `Manrope`   | Headings      |
| `Nunito`    | Body Text     |

Tailwind Extension:

```js
fontFamily: {
  sans: ['Nunito', 'sans-serif'],
  manrope: ['Manrope', 'sans-serif'],
}
